package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 月度排行榜奖励发放记录对象 monthly_ranking_reward_record
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyRankingRewardRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 年月，格式：yyyy-MM */
    @Excel(name = "年月")
    private String yearMonth;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long residentialId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 总碳豆 */
    @Excel(name = "总碳豆")
    private Long totalSilver;

    /** 小区内排名位置 */
    @Excel(name = "小区内排名")
    private Integer rankPosition;

    /** 奖励配置ID */
    @Excel(name = "奖励配置ID")
    private Long rewardConfigId;

    /** 奖励类型：SILVER碳豆，COIN金币 */
    @Excel(name = "奖励类型")
    private String rewardType;

    /** 奖励数量 */
    @Excel(name = "奖励数量")
    private Long rewardAmount;

    /** 奖励名称 */
    @Excel(name = "奖励名称")
    private String rewardName;

    /** 发放状态：PENDING待发放，SUCCESS已发放，FAILED发放失败 */
    @Excel(name = "发放状态")
    private String status;

    /** 发放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发放时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date issuedTime;

    // 以下字段不在数据库中，用于前端显示
    /** 用户昵称 */
    private String nickname;

    /** 用户头像 */
    private String avatar;

    /**
     * 发放状态枚举
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String SUCCESS = "SUCCESS";
        public static final String FAILED = "FAILED";
    }
} 