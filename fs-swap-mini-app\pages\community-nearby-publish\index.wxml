<!--pages/community-nearby-publish/index.wxml-->
<view class="container">
  <form bindsubmit="handleSubmit">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">
        <van-icon name="info-o" size="16px" color="#3B7FFF" />
        <text>基本信息</text>
      </view>

      <van-field
        value="{{ formData.name }}"
        label="名称"
        placeholder="请输入地点名称（最多50字）"
        required
        bind:change="onNameChange"
        input-class="field-input"
        error="{{ nameError }}"
        error-message="{{ nameError }}"
      />

      <view class="field-container">
        <view class="field-label required">图片</view>
        <view class="upload-container">
          <van-uploader
            file-list="{{ fileList }}"
            max-count="{{MAX_UPLOAD_COUNT}}"
            bind:after-read="afterRead"
            bind:delete="onDeleteFile"
            accept="image"
            upload-text="上传图片"
            multiple="{{true}}"
          />
        </view>
        <view class="upload-tip">第一张图片将作为封面</view>
        <view class="error-message" wx:if="{{ imageError }}">{{ imageError }}</view>
      </view>

      <view class="field-container">
        <view class="field-label required">描述</view>
        <view class="textarea-container">
          <textarea
            value="{{ formData.description }}"
            placeholder="请描述该地点的特色、服务等信息"
            maxlength="{{ MAX_DESCRIPTION_LENGTH }}"
            bindinput="onDescriptionChange"
            name="description"
          ></textarea>
          <view class="word-count">{{ formData.description.length }}/{{ MAX_DESCRIPTION_LENGTH }}</view>
        </view>
        <view class="error-message" wx:if="{{ descriptionError }}">{{ descriptionError }}</view>
      </view>

      <van-field
        value="{{ formData.address }}"
        label="地址"
        placeholder="点击选择地址"
        required
        readonly
        bindtap="chooseLocation"
        right-icon="location-o"
        bind:click-icon="chooseLocation"
        input-class="field-input"
        error="{{ addressError }}"
        error-message="{{ addressError }}"
      />

      <van-field
        value="{{ formData.categoryName }}"
        label="分类"
        placeholder="请选择分类"
        required
        readonly
        bindtap="showCategoryPicker"
        right-icon="arrow"
        input-class="field-input"
      />

      <van-field
        value="{{ selectedTagNames }}"
        label="标签"
        placeholder="选择服务标签（可选）"
        readonly
        bindtap="showTagPicker"
        right-icon="arrow"
        input-class="field-input"
      />
    </view>

    <!-- 联系信息 -->
    <view class="form-section">
      <view class="section-title">
        <van-icon name="phone-o" size="16px" color="#3B7FFF" />
        <text>联系信息</text>
      </view>

      <van-field
        value="{{ formData.contactPhone }}"
        label="联系电话"
        placeholder="请输入联系电话（选填）"
        type="number"
        bind:change="onContactPhoneChange"
        input-class="field-input"
        error="{{ phoneError }}"
        error-message="{{ phoneError }}"
      />

      <van-field
        value="{{ formData.businessHours }}"
        label="营业时间"
        placeholder="请输入营业时间（选填）"
        bind:change="onBusinessHoursChange"
        input-class="field-input"
      />
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{ submitting ? 'disabled' : '' }}"
        form-type="submit"
        loading="{{ submitting }}"
        disabled="{{ !formValid || submitting }}"
      >{{ submitting ? '提交中...' : '提交' }}</button>
    </view>
  </form>

  <!-- 分类选择器 -->
  <van-popup
    show="{{ showCategoryPicker }}"
    position="bottom"
    bind:close="hideCategoryPicker"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-picker
      columns="{{ categories }}"
      bind:confirm="onCategoryConfirm"
      bind:cancel="hideCategoryPicker"
      confirm-button-text="确认"
      cancel-button-text="取消"
      show-toolbar="{{ true }}"
      title="选择分类"
    />
  </van-popup>

  <!-- 标签选择器 -->
  <van-popup
    show="{{ showTagPicker }}"
    position="bottom"
    bind:close="hideTagPicker"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <view class="tag-picker-container">
      <view class="tag-picker-header">
        <text class="tag-picker-title">选择服务标签</text>
        <text class="tag-picker-subtitle">最多选择5个标签</text>
      </view>
      
      <view class="tag-list">
        <view 
          wx:for="{{ availableTags }}" 
          wx:key="dictValue"
          class="tag-item {{ item.selected ? 'selected' : '' }}"
          data-value="{{ item.dictValue }}"
          bindtap="onTagToggle"
        >
          <text>{{ item.dictLabel }}</text>
        </view>
      </view>
      
      <view class="tag-picker-footer">
        <button class="tag-confirm-btn" bindtap="onTagConfirm">确定</button>
      </view>
    </view>
  </van-popup>
</view>
