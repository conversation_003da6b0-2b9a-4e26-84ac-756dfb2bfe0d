/* pages/activity/list/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* 顶部筛选区域 */
.filter-header {
  background: #ffffff;
  padding: 10px 16px 6px;
  z-index: 100;
  position: relative;
  border-bottom: 1px solid #f0f2f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

/* 活动列表容器 */
.activity-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 搜索栏样式 */
.search-bar {
  margin-bottom: 10px;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

/* 自定义van-search样式 */
.search-bar .van-search {
  padding: 0 !important;
  background-color: transparent !important;
}

.search-bar .van-search__content {
  background-color: #f5f7fa !important;
  border-radius: 20px !important;
  height: 36px !important;
  border: 1px solid #e8eaed !important;
  box-shadow: none !important;
}

.search-bar .van-field__left-icon {
  color: #8a8e99 !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.search-bar .van-cell {
  padding: 4px 16px !important;
  background-color: transparent !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

.search-bar .van-field__control {
  color: #1f2329 !important;
  font-size: 14px !important;
  height: 28px !important;
  font-weight: 400 !important;
}

.search-bar .van-field__placeholder {
  color: #8a8e99 !important;
  font-size: 14px !important;
}

/* 分类区域 */
.category-section {
  position: relative;
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 分类标签样式 */
.category-tabs {
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0;
  margin: 0 -16px 16px;
  display: flex;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 隐藏WebKit滚动条 */
.category-tabs::-webkit-scrollbar {
  display: none;
}

/* 滑动指示器 */
.category-tabs::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 16px;
  background: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  pointer-events: none;
  z-index: 1;
}

.category-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 8px;
  margin-right: 6px;
  background-color: #f5f7fa;
  border: 1px solid #e8eaed;
  border-radius: 16px;
  font-size: 11px;
  color: #4e5969;
  transition: all 0.2s ease;
  height: 26px;
  min-width: 44px;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.category-tab:first-child {
  margin-left: 16px;
}

.category-tab:last-child {
  margin-right: 16px;
}

.category-tab:active {
  transform: scale(0.96);
}

.category-tab.active {
  background-color: #3B7FFF;
  border-color: #3B7FFF;
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.2);
  transform: translateY(-0.5px);
  color: #ffffff;
  font-weight: 600;
}

.category-tab:not(.active):hover {
  background-color: #f0f2f5;
  border-color: #e8eaed;
  transform: translateY(-0.5px);
}

.category-tab:not(.active):active {
  transform: scale(0.96) translateY(0);
}

/* 活动列表样式 */
.activity-list {
  padding: 16px 16px 80px;
}

.activity-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  overflow: hidden;
  transition: transform 0.3s;
}

.activity-card:active {
  transform: scale(0.98);
}

.activity-image-container {
  position: relative;
  height: 160px;
}

.activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-category {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.activity-info {
  padding: 16px;
}

.activity-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-time, .activity-address {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.activity-time text, .activity-address text {
  margin-left: 6px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f5f5f5;
}

.activity-organizer {
  display: flex;
  align-items: center;
}

.organizer-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.activity-organizer text {
  font-size: 14px;
  color: #666666;
}

/* 加载更多和空状态样式 */
.loading-more, .no-more, .empty-state {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}

/* 发布活动按钮 */
.publish-btn {
  position: fixed;
  right: 20px;
  bottom: 80px;
  width: 56px;
  height: 56px;
  background-color: #3B7FFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 127, 255, 0.4);
  z-index: 99;
}
