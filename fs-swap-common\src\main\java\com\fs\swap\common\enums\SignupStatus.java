package com.fs.swap.common.enums;

/**
 * 活动报名状态枚举
 */
public enum SignupStatus implements BaseEnum {
    /**
     * 待审核
     */
    PENDING("0", "待审核"),

    /**
     * 已通过
     */
    APPROVED("1", "已通过"),

    /**
     * 已拒绝
     */
    REJECTED("2", "已拒绝"),

    /**
     * 已取消
     */
    CANCELED("3", "已取消");

    private final String code;
    private final String info;

    SignupStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据状态码获取报名状态枚举
     *
     * @param code 状态码
     * @return 报名状态枚举
     */
    public static SignupStatus getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SignupStatus status : SignupStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
