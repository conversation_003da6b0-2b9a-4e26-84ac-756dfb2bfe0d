<!--pages/community-phone/index.wxml-->
<view class="container">
  <!-- 顶部筛选区域 -->
  <view class="filter-header">
    <!-- 搜索框 -->
    <view class="search-section">
      <van-search
        value="{{ searchValue }}"
        placeholder="搜索常用电话..."
        bind:change="onSearchChange"
        shape="round"
        background="transparent"
        clearable
        left-icon="search"
        custom-class="search-input"
        input-class="search-input-field"
      />
    </view>
    
    <!-- 分类标签 -->
    <view class="category-section">
      <scroll-view class="category-tabs" scroll-x="true" show-scrollbar="false" enhanced="true" scroll-with-animation="true">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-tab {{phoneFilter === item.id ? 'active' : ''}}" 
          bindtap="onPhoneFilterChange" 
          data-filter="{{item.id}}"
        >
          <text class="tab-text">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading size="24px" color="#3B7FFF" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主体内容区域 -->
  <view wx:else class="content-area">
    <!-- 空状态 -->
    <view wx:if="{{filteredList.length === 0}}" class="empty-container">
      <van-icon name="info-o" size="48px" color="#cccccc" />
      <text class="empty-text">暂无相关电话</text>
      <text class="empty-hint">试试搜索其他关键词或切换分类</text>
    </view>

    <!-- 电话列表 -->
    <view wx:else class="phone-list">
      <!-- 按分类循环 -->
      <block wx:for="{{categories}}" wx:key="id" wx:for-item="category">
        <block wx:if="{{category.id !== 'all' && (phoneFilter === 'all' || phoneFilter === category.id)}}">
          <view class="section-header">
            <view class="section-title">
              <van-icon name="apps-o" size="16px" color="#3B7FFF" />
              <text class="section-text">{{category.name}}</text>
            </view>
            <view class="section-divider"></view>
          </view>
          <view class="phone-group">
            <view 
              class="phone-item" 
              wx:for="{{filteredList}}" 
              wx:key="id" 
              wx:if="{{item.category === category.id}}"
            >
              <view class="phone-info">
                <view class="phone-details">
                  <view class="phone-name">{{item.name}}</view>
                  <view class="phone-number-row">
                    <text class="phone-number">{{item.displayPhoneNumber}}</text>
                    <view class="phone-stats">
                      <van-icon name="phone-o" size="10px" color="#999999" />
                      <text class="call-count">已获取{{item.callCount || 0}}次</text>
                    </view>
                  </view>
                  <view class="phone-description" wx:if="{{item.description && item.description.length > 0}}">{{item.description}}</view>
                </view>
              </view>
              <view class="phone-call" bindtap="onCallPhone" data-item="{{item}}">
                <!-- <van-icon name="phone-circle-o" size="16px" color="#ffffff" /> -->
                <van-icon name="phone-o" size="14px" color="#ffffff" />
                <text class="call-text">获取</text>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>
  </view>

  <!-- 悬浮操作按钮 -->
  <view class="floating-actions">
    <view class="fab-btn my-submissions-btn" bindtap="onMySubmissionsButtonTap" hover-class="fab-btn-hover">
      <van-icon name="orders-o" size="18px" color="#ffffff" />
    </view>
    <view class="fab-btn add-btn" bindtap="onAddPhone" hover-class="fab-btn-hover">
      <van-icon name="plus" size="22px" color="#ffffff" />
    </view>
  </view>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess"></login-action>

  <!-- 小区认证组件 -->
  <residential-auth id="residentialAuth" />
</view>