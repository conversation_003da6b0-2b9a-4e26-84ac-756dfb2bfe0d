package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 月度排行榜奖励配置对象 monthly_ranking_reward_config
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyRankingRewardConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 排名起始位置 */
    @Excel(name = "排名起始位置")
    private Integer rankStart;

    /** 排名结束位置 */
    @Excel(name = "排名结束位置")
    private Integer rankEnd;

    /** 奖励类型：SILVER碳豆，COIN金币 */
    @Excel(name = "奖励类型")
    private String rewardType;

    /** 奖励数量 */
    @Excel(name = "奖励数量")
    private Long rewardAmount;

    /** 奖励名称 */
    @Excel(name = "奖励名称")
    private String rewardName;

    /** 是否启用：1启用，0禁用 */
    @Excel(name = "是否启用")
    private Boolean isActive;

    /**
     * 奖励类型枚举
     */
    public static class RewardType {
        public static final String SILVER = "SILVER";
        public static final String COIN = "COIN";
    }
} 