package com.fs.swap.wx.service;

import com.fs.swap.common.core.domain.entity.TaskCompleteLog;
import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.enums.TaskStatus;
import com.fs.swap.common.enums.TaskType;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.constant.CacheConstants;
import com.fs.swap.system.mapper.TaskCompleteLogMapper;
import com.fs.swap.system.mapper.TaskConfigMapper;
import com.fs.swap.system.mapper.UserTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 任务事件处理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class TaskEventService {
    
    private static final Logger log = LoggerFactory.getLogger(TaskEventService.class);
    
    @Autowired
    private TaskConfigMapper taskConfigMapper;
    
    @Autowired
    private UserTaskMapper userTaskMapper;
    
    @Autowired
    private TaskCompleteLogMapper taskCompleteLogMapper;
    
    @Autowired
    private RedisCache redisCache;

    /**
     * 处理任务事件
     */
    @Transactional
    public void processTaskEvent(Long userId, String eventType, String businessId) {
        try {
            // 1. 查询匹配的任务配置
            List<TaskConfig> configs = getTaskConfigsByEvent(eventType);
            
            for (TaskConfig config : configs) {
                if (!"1".equals(config.getStatus())) continue;
                
                // 2. 获取或创建用户任务
                UserTask userTask = getOrCreateUserTask(userId, config);
                
                // 3. 检查是否已完成或已领取
                if (TaskStatus.COMPLETED.getCode().equals(userTask.getStatus()) || 
                    TaskStatus.CLAIMED.getCode().equals(userTask.getStatus())) {
                    continue;
                }
                
                // 4. 更新进度
                updateTaskProgress(userTask, config, eventType, businessId);
            }
            
        } catch (Exception e) {
            log.error("处理任务事件失败，userId: {}, eventType: {}, businessId: {}", userId, eventType, businessId, e);
            // 为了不影响主业务流程，这里选择静默处理
        }
    }

    /**
     * 批量处理任务事件
     */
    @Transactional
    public void batchProcessTaskEvent(Long[] userIds, String eventType, String businessId) {
        for (Long userId : userIds) {
            processTaskEvent(userId, eventType, businessId);
        }
    }

    /**
     * 获取或创建用户任务
     */
    private UserTask getOrCreateUserTask(Long userId, TaskConfig config) {
        Date taskDate = null;
        if (TaskType.DAILY.getCode().equals(config.getTaskType())) {
            taskDate = DateUtils.parseDate(DateUtils.getDate());
        }
        
        UserTask userTask = userTaskMapper.selectUserTaskByUserAndCode(userId, config.getTaskCode(), taskDate);
        
        if (userTask == null) {
            userTask = new UserTask();
            userTask.setUserId(userId);
            userTask.setTaskCode(config.getTaskCode());
            userTask.setTaskDate(taskDate);
            userTask.setTargetCount(config.getTargetCount());
            userTask.setCurrentCount(0);
            userTask.setStatus(TaskStatus.IN_PROGRESS.getCode());
            userTaskMapper.insertUserTask(userTask);
        }
        
        return userTask;
    }

    /**
     * 更新任务进度
     */
    @Transactional
    public void updateTaskProgress(UserTask userTask, TaskConfig config, String eventType, String businessId) {
        // 增加进度
        userTask.setCurrentCount(userTask.getCurrentCount() + 1);
        
        // 检查是否完成
        if (userTask.getCurrentCount() >= config.getTargetCount()) {
            userTask.setStatus(TaskStatus.COMPLETED.getCode());
            userTask.setCompleteTime(DateUtils.getNowDate());
            
            // 记录完成日志
            TaskCompleteLog logRecord = new TaskCompleteLog();
            logRecord.setUserId(userTask.getUserId());
            logRecord.setTaskCode(userTask.getTaskCode());
            logRecord.setEventType(eventType);
            logRecord.setBusinessId(businessId);
            logRecord.setRewardSilver(config.getRewardSilver());
            taskCompleteLogMapper.insertTaskCompleteLog(logRecord);
        }
        
        userTaskMapper.updateUserTask(userTask);
        
        // 清理用户任务缓存
        String cacheKey = CacheConstants.USER_TASK_CACHE_PREFIX + userTask.getUserId() + ":" + userTask.getTaskCode();
        redisCache.deleteObject(cacheKey);
    }

    /**
     * 根据事件类型获取任务配置
     */
    private List<TaskConfig> getTaskConfigsByEvent(String eventType) {
        String cacheKey = CacheConstants.TASK_EVENT_CACHE_PREFIX + eventType;
        List<TaskConfig> configs = redisCache.getCacheObject(cacheKey);
        
        if (configs == null) {
            configs = taskConfigMapper.selectTaskConfigByTriggerEvent(eventType);
            redisCache.setCacheObject(cacheKey, configs, CacheConstants.CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }
        
        return configs;
    }
} 