const api = require('../../config/api.js')
const util = require('../../utils/util.js')
const systemInfoService = require('../../services/systemInfo.js')
const app = getApp()

Page({
  data: {
    // 排行榜数据
    rankingList: [],
    myRanking: null,
    loading: false,
    // 分页参数
    pageNum: 1,
    pageSize: 20,
    hasMore: true,
    // 状态栏高度
    statusBarHeight: 0,
    navBarHeight: 0,
    // 当前选中的选项卡：current-当前排名，previous-上期榜单
    currentTab: 'current'
  },

  onLoad(options) {
    // 获取导航栏信息
    this.updateNavBarInfo()
    
    // 加载排行榜数据
    this.loadRankingData()
    
    // 加载我的排名
    this.loadMyRanking()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadRankingData()
    this.loadMyRanking()
  },

  // 更新导航栏信息
  updateNavBarInfo() {
    const navBarInfo = util.getNavBarInfo()
    this.setData({
      statusBarHeight: navBarInfo.statusBarHeight,
      navBarHeight: navBarInfo.navBarHeight
    })
  },

  // 加载排行榜数据
  async loadRankingData(refresh = true) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const params = {
        pageNum: refresh ? 1 : this.data.pageNum,
        pageSize: this.data.pageSize,
        // 添加榜单类型参数
        rankingType: this.data.currentTab
      }

      let res
      if (this.data.currentTab === 'current') {
        // 当前排名使用原有接口
        res = await api.getRankingList(params)
      } else {
        // 上期榜单使用新接口
        res = await api.getPreviousRanking(params)
      }

      if (res.code === 200) {
        // 新的API直接返回排行榜数组，不再包装在data.rankingList中
        const rankingList = res.data || []

        // 处理排行榜数据，包括头像URL处理
        const processedList = await Promise.all(rankingList.map(async (item, index) => {
          let processedAvatar = '/static/img/default_avatar.png'
          if (item.avatar) {
            processedAvatar = await systemInfoService.processImageUrl(item.avatar)
          }

          return {
            id: item.userId,
            rank: item.rank || item.rankPosition || (index + 1),
            name: item.nickname || `用户${item.userId}`,
            nickname: item.nickname,
            avatar: processedAvatar,
            totalSilver: item.totalSilver || 0,
            residentialId: item.residentialId,
            reward: item.reward || item.rewardName || '--' // 添加奖励字段
          }
        }))

        this.setData({
          rankingList: refresh ? processedList : [...this.data.rankingList, ...processedList],
          pageNum: refresh ? 2 : this.data.pageNum + 1,
          hasMore: processedList.length >= this.data.pageSize,
          loading: false
        })
      } else {
        throw new Error(res.msg || '获取排行榜失败')
      }
    } catch (error) {
      console.error('加载排行榜失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 加载我的排名
  async loadMyRanking() {
    try {
      const res = await api.getMyRanking()

      if (res.code === 200 && res.data) {
        let processedAvatar = '/static/img/default_avatar.png'
        if (res.data.avatar) {
          processedAvatar = await systemInfoService.processImageUrl(res.data.avatar)
        }

        this.setData({
          myRanking: {
            ...res.data,
            avatar: processedAvatar,
            name: res.data.nickname || '我'
          }
        })
      }
    } catch (error) {
      console.error('获取我的排名失败:', error)
    }
  },

  // 选项卡切换
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab
    
    if (tab === this.data.currentTab) return
    
    // 添加触感反馈
    wx.vibrateShort()
    
    // 显示加载提示
    wx.showToast({
      title: tab === 'current' ? '加载当前排名...' : '加载上期榜单...',
      icon: 'loading',
      duration: 1000
    })
    
    this.setData({
      currentTab: tab,
      rankingList: [],
      pageNum: 1,
      hasMore: true
    })
    
    // 重新加载数据
    this.loadRankingData(true)
    
    // 如果是当前排名，也加载我的排名
    if (tab === 'current') {
      this.loadMyRanking()
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadRankingData(true).then(() => {
      // 只有当前排名才加载我的排名
      if (this.data.currentTab === 'current') {
        this.loadMyRanking()
      }
      wx.stopPullDownRefresh()
    })
  },

  // 触底加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadRankingData(false)
    }
  },

  // 查看用户详情
  onUserAvatarTap(e) {
    const userId = e.currentTarget.dataset.userid
    if (userId) {
      wx.navigateTo({
        url: `/pages/user/userProfile/userProfile?userId=${userId}`,
      })
    } else {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
    }
  },

  // 查看排行榜规则
  onRulesTap() {
    wx.navigateTo({
      url: '/pages/ranking/rules/index'
    })
  },

  // 返回首页
  onBackTap() {
    wx.navigateBack({
      fail: () => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '碳豆排行榜 - 看看谁是环保达人！',
      path: '/pages/ranking/index'
    }
  }
}) 