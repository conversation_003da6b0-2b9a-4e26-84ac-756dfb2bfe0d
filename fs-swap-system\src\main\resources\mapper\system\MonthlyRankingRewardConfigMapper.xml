<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.MonthlyRankingRewardConfigMapper">

    <resultMap type="MonthlyRankingRewardConfig" id="MonthlyRankingRewardConfigResult">
        <result property="id"           column="id"            />
        <result property="rankStart"    column="rank_start"    />
        <result property="rankEnd"      column="rank_end"      />
        <result property="rewardType"   column="reward_type"   />
        <result property="rewardAmount" column="reward_amount" />
        <result property="rewardName"   column="reward_name"   />
        <result property="isActive"     column="is_active"     />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectMonthlyRankingRewardConfigVo">
        select id, rank_start, rank_end, reward_type, reward_amount, reward_name, is_active, create_time, update_time from monthly_ranking_reward_config
    </sql>

    <select id="selectMonthlyRankingRewardConfigList" parameterType="MonthlyRankingRewardConfig" resultMap="MonthlyRankingRewardConfigResult">
        <include refid="selectMonthlyRankingRewardConfigVo"/>
        <where>
            <if test="rankStart != null"> and rank_start = #{rankStart}</if>
            <if test="rankEnd != null"> and rank_end = #{rankEnd}</if>
            <if test="rewardType != null and rewardType != ''"> and reward_type = #{rewardType}</if>
            <if test="rewardAmount != null"> and reward_amount = #{rewardAmount}</if>
            <if test="rewardName != null and rewardName != ''"> and reward_name like concat('%', #{rewardName}, '%')</if>
            <if test="isActive != null"> and is_active = #{isActive}</if>
        </where>
        order by rank_start, rank_end
    </select>

    <select id="selectMonthlyRankingRewardConfigById" parameterType="Long" resultMap="MonthlyRankingRewardConfigResult">
        <include refid="selectMonthlyRankingRewardConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectActiveRewardConfigs" resultMap="MonthlyRankingRewardConfigResult">
        <include refid="selectMonthlyRankingRewardConfigVo"/>
        where is_active = 1
        order by rank_start, rank_end
    </select>

    <select id="selectRewardConfigByRank" parameterType="Integer" resultMap="MonthlyRankingRewardConfigResult">
        <include refid="selectMonthlyRankingRewardConfigVo"/>
        where is_active = 1 
        and #{rankPosition} &gt;= rank_start 
        and #{rankPosition} &lt;= rank_end
        limit 1
    </select>

    <insert id="insertMonthlyRankingRewardConfig" parameterType="MonthlyRankingRewardConfig" useGeneratedKeys="true" keyProperty="id">
        insert into monthly_ranking_reward_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rankStart != null">rank_start,</if>
            <if test="rankEnd != null">rank_end,</if>
            <if test="rewardType != null and rewardType != ''">reward_type,</if>
            <if test="rewardAmount != null">reward_amount,</if>
            <if test="rewardName != null and rewardName != ''">reward_name,</if>
            <if test="isActive != null">is_active,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rankStart != null">#{rankStart},</if>
            <if test="rankEnd != null">#{rankEnd},</if>
            <if test="rewardType != null and rewardType != ''">#{rewardType},</if>
            <if test="rewardAmount != null">#{rewardAmount},</if>
            <if test="rewardName != null and rewardName != ''">#{rewardName},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateMonthlyRankingRewardConfig" parameterType="MonthlyRankingRewardConfig">
        update monthly_ranking_reward_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="rankStart != null">rank_start = #{rankStart},</if>
            <if test="rankEnd != null">rank_end = #{rankEnd},</if>
            <if test="rewardType != null and rewardType != ''">reward_type = #{rewardType},</if>
            <if test="rewardAmount != null">reward_amount = #{rewardAmount},</if>
            <if test="rewardName != null and rewardName != ''">reward_name = #{rewardName},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyRankingRewardConfigById" parameterType="Long">
        delete from monthly_ranking_reward_config where id = #{id}
    </delete>

    <delete id="deleteMonthlyRankingRewardConfigByIds" parameterType="String">
        delete from monthly_ranking_reward_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 