package com.fs.swap.wx.controller;

import com.fs.swap.common.annotation.RepeatSubmit;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.CommunityNearby;
import com.fs.swap.common.core.domain.entity.CommunityPhone;
import com.fs.swap.common.core.domain.entity.ResidentialArea;
import com.fs.swap.common.core.domain.entity.UserHome;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.GreenTextType;
import com.fs.swap.common.enums.GreenImgType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.common.utils.GeoUtils;
import com.fs.swap.system.service.ICommunityNearbyService;
import com.fs.swap.system.service.ICommunityPhoneService;
import com.fs.swap.system.service.IResidentialAreaService;
import com.fs.swap.system.service.IUserHomeService;
import com.fs.swap.wx.domain.convert.CommunityNearbyConvert;
import com.fs.swap.wx.domain.convert.CommunityPhoneConvert;
import com.fs.swap.wx.domain.dto.CommunityNearbyDTO;
import com.fs.swap.wx.domain.dto.CommunityPhoneDTO;
import com.fs.swap.wx.domain.vo.CommunityNearbyVO;
import com.fs.swap.wx.domain.vo.CommunityPhoneVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 社区服务提交Controller
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/community-submit")
public class CommunitySubmitController extends WxApiBaseController {

    @Autowired
    private ICommunityPhoneService communityPhoneService;

    @Autowired
    private ICommunityNearbyService communityNearbyService;

    @Autowired
    private IUserHomeService userHomeService;

    @Autowired
    private IResidentialAreaService residentialAreaService;

    @Autowired
    private ContentModerationFacade contentModerationFacade;

    @Value("${dromara.fileDomain}")
    private String fileUrl;

    /**
     * 提交常用电话
     */
    @PostMapping("/phone/submit")
    @RepeatSubmit(interval = 5000, message = "请勿重复提交，请稍后再试")
    public AjaxResult submitPhone(@RequestBody @Valid CommunityPhoneDTO phoneDTO) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 内容审核 - 电话名称
        if (phoneDTO.getName() != null && !phoneDTO.getName().trim().isEmpty()) {
            contentModerationFacade.checkText(phoneDTO.getName(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 电话描述
        if (phoneDTO.getDescription() != null && !phoneDTO.getDescription().trim().isEmpty()) {
            contentModerationFacade.checkText(phoneDTO.getDescription(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 获取用户当前选择的小区
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome == null) {
            throw new ServiceException("请先选择小区");
        }

        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
        if (residentialArea == null) {
            throw new ServiceException("小区信息不存在");
        }

        // 创建电话实体
        CommunityPhone phone = new CommunityPhone();
        phone.setName(phoneDTO.getName());
        phone.setPhoneNumber(phoneDTO.getPhoneNumber());
        phone.setCategory(phoneDTO.getCategory());
        phone.setDescription(phoneDTO.getDescription());

        // 设置关联信息
        phone.setCommunityId(residentialArea.getCommunityId());
        phone.setResidentialId(userHome.getResidentialId());
        phone.setSubmitUserId(userId);

        // 设置审核状态为待审核
        phone.setAuditStatus(0L);

        // 设置排序和状态
        phone.setSort(0);
        phone.setStatus(1L);

        // 保存电话信息
        communityPhoneService.insertCommunityPhone(phone);

        return AjaxResult.success("提交成功，等待审核");
    }

    /**
     * 提交周边推荐
     */
    @PostMapping("/nearby/submit")
    @RepeatSubmit(interval = 5000, message = "请勿重复提交，请稍后再试")
    public AjaxResult submitNearby(@RequestBody @Valid CommunityNearbyDTO nearbyDTO) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 内容审核 - 服务名称
        if (nearbyDTO.getName() != null && !nearbyDTO.getName().trim().isEmpty()) {
            contentModerationFacade.checkText(nearbyDTO.getName(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 服务描述
        if (nearbyDTO.getDescription() != null && !nearbyDTO.getDescription().trim().isEmpty()) {
            contentModerationFacade.checkText(nearbyDTO.getDescription(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 服务图片
        if (nearbyDTO.getImages() != null && !nearbyDTO.getImages().trim().isEmpty()) {
            String[] imageUrls = nearbyDTO.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }

        // 获取用户当前选择的小区
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome == null) {
            throw new ServiceException("请先选择小区");
        }

        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
        if (residentialArea == null) {
            throw new ServiceException("小区信息不存在");
        }

        // 创建周边实体
        CommunityNearby nearby = new CommunityNearby();
        nearby.setName(nearbyDTO.getName());
        nearby.setAddress(nearbyDTO.getAddress());
        nearby.setCategory(nearbyDTO.getCategory());
        nearby.setImages(nearbyDTO.getImages());
        nearby.setDescription(nearbyDTO.getDescription());
        nearby.setTags(nearbyDTO.getTags());
        nearby.setContactPhone(nearbyDTO.getContactPhone());
        nearby.setBusinessHours(nearbyDTO.getBusinessHours());

        // 处理位置信息，标准化为POINT格式
        String originalLocation = nearbyDTO.getLocation();
        String normalizedLocation = GeoUtils.normalizePointFormat(originalLocation);
        
        // 详细验证位置信息
        if (StringUtils.isBlank(originalLocation)) {
            throw new ServiceException("位置信息不能为空");
        }
        
        if (!GeoUtils.isValidPointFormat(normalizedLocation)) {
            // 记录详细的错误信息用于调试
            throw new ServiceException("位置信息格式错误，请重新选择位置");
        }
        
        nearby.setLocation(normalizedLocation);

        // 设置关联信息
        nearby.setRegionId(residentialArea.getRegionId());
        nearby.setCommunityId(residentialArea.getCommunityId());
        nearby.setResidentialId(userHome.getResidentialId());

        // 使用Service的提交方法，会自动设置各种默认值
        int result = communityNearbyService.submitCommunityNearby(nearby, userId);
        
        if (result > 0) {
            return AjaxResult.success("提交成功，等待审核");
        } else {
            return AjaxResult.error("提交失败，请重试");
        }
    }

    /**
     * 获取我提交的常用电话列表
     */
    @GetMapping("/phone/my-submitted")
    public TableDataInfo getMySubmittedPhones() {
        // 获取当前用户ID
        Long userId = getUserId();

        startPage();
        List<CommunityPhone> list = communityPhoneService.selectCommunityPhoneListBySubmitUserId(userId);
        TableDataInfo dataTable = getDataTable(list);

        // 转换为VO
        List<CommunityPhoneVO> voList = CommunityPhoneConvert.INSTANCE.toVOList(list);

        dataTable.setRows(voList);
        return dataTable;
    }

    /**
     * 获取我提交的周边推荐列表
     */
    @GetMapping("/nearby/my-submitted")
    public TableDataInfo getMySubmittedNearby() {
        // 获取当前用户ID
        Long userId = getUserId();

        startPage();
        List<CommunityNearby> list = communityNearbyService.selectCommunityNearbyListBySubmitUserId(userId);
        TableDataInfo dataTable = getDataTable(list);

        // 转换为VO
        List<CommunityNearbyVO> voList = CommunityNearbyConvert.INSTANCE.toVOList(list);

        // 处理图片路径
        for (CommunityNearbyVO vo : voList) {
            // 处理图片
            if (StringUtils.isNotBlank(vo.getImages())) {
                String[] imageUrls = vo.getImages().split(",");
                for (int i = 0; i < imageUrls.length; i++) {
                    imageUrls[i] = fileUrl + imageUrls[i];
                }
                vo.setImageUrls(imageUrls);
            }
            
            // 位置信息保持POINT格式，不再转换
            // vo.setLocation() 保持原始的POINT格式
        }

        dataTable.setRows(voList);
        return dataTable;
    }
}
