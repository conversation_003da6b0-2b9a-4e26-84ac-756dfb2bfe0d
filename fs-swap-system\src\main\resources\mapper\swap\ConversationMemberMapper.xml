<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.ConversationMemberMapper">

    <resultMap type="ConversationMember" id="ConversationMemberResult">
        <id     property="id"                   column="id"                   />
        <result property="conversationId"      column="conversation_id"      />
        <result property="userId"              column="user_id"              />
        <result property="unreadCount"         column="unread_count"         />
        <result property="lastReadMessageId"   column="last_read_message_id" />
        <result property="isMuted"             column="is_muted"             />
        <result property="joinTime"            column="join_time"            />
        <result property="leaveTime"           column="leave_time"           />
        <result property="status"              column="status"               />
    </resultMap>

    <!-- 带用户信息的结果映射 -->
    <resultMap type="ConversationMember" id="ConversationMemberWithUserResult" extends="ConversationMemberResult">
        <association property="userInfo" javaType="UserInfo">
            <id     property="id"         column="user_id"         />
            <result property="nickname"   column="nickname"        />
            <result property="avatar"     column="user_avatar"     />
            <result property="phone"      column="phone"           />
        </association>
    </resultMap>

    <sql id="selectConversationMemberVo">
        select id, conversation_id, user_id, unread_count, last_read_message_id, is_muted, join_time, leave_time, status from conversation_member
    </sql>

    <select id="selectConversationMemberList" parameterType="ConversationMember" resultMap="ConversationMemberResult">
        <include refid="selectConversationMemberVo"/>
        <where>  
            <if test="conversationId != null  and conversationId != ''"> and conversation_id = #{conversationId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isMuted != null "> and is_muted = #{isMuted}</if>
        </where>
        order by join_time desc
    </select>
    
    <select id="selectConversationMemberById" parameterType="Long" resultMap="ConversationMemberResult">
        <include refid="selectConversationMemberVo"/>
        where id = #{id}
    </select>

    <!-- 根据会话ID和用户ID查询会话成员 -->
    <select id="selectByConversationAndUser" resultMap="ConversationMemberResult">
        <include refid="selectConversationMemberVo"/>
        where conversation_id = #{conversationId} and user_id = #{userId} and status = 0
    </select>

    <!-- 查询会话的所有成员（包含用户信息） -->
    <select id="selectMembersByConversation" resultMap="ConversationMemberWithUserResult">
        SELECT 
            cm.id, cm.conversation_id, cm.user_id, cm.unread_count, 
            cm.last_read_message_id, cm.is_muted, cm.join_time, cm.leave_time, cm.status,
            u.nickname, u.avatar as user_avatar, u.phone
        FROM conversation_member cm
        LEFT JOIN user_info u ON cm.user_id = u.id
        WHERE cm.conversation_id = #{conversationId} AND cm.status = 0
        ORDER BY cm.join_time ASC
    </select>

    <!-- 查询用户参与的所有会话 -->
    <select id="selectConversationsByUser" resultMap="ConversationMemberResult">
        <include refid="selectConversationMemberVo"/>
        where user_id = #{userId} and status = 0
        order by join_time desc
    </select>

    <insert id="insertConversationMember" parameterType="ConversationMember" useGeneratedKeys="true" keyProperty="id">
        insert into conversation_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="conversationId != null">conversation_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="unreadCount != null">unread_count,</if>
            <if test="lastReadMessageId != null">last_read_message_id,</if>
            <if test="isMuted != null">is_muted,</if>
            <if test="joinTime != null">join_time,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="conversationId != null">#{conversationId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="unreadCount != null">#{unreadCount},</if>
            <if test="lastReadMessageId != null">#{lastReadMessageId},</if>
            <if test="isMuted != null">#{isMuted},</if>
            <if test="joinTime != null">#{joinTime},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <!-- 批量新增会话成员 -->
    <insert id="batchInsertConversationMembers">
        insert into conversation_member(conversation_id, user_id, unread_count, is_muted, join_time, status)
        values
        <foreach collection="members" item="member" separator=",">
            (#{member.conversationId}, #{member.userId}, #{member.unreadCount}, #{member.isMuted}, #{member.joinTime}, #{member.status})
        </foreach>
    </insert>

    <update id="updateConversationMember" parameterType="ConversationMember">
        update conversation_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="conversationId != null">conversation_id = #{conversationId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="unreadCount != null">unread_count = #{unreadCount},</if>
            <if test="lastReadMessageId != null">last_read_message_id = #{lastReadMessageId},</if>
            <if test="isMuted != null">is_muted = #{isMuted},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 更新未读消息数量 -->
    <update id="updateUnreadCount">
        update conversation_member 
        set unread_count = #{unreadCount}
        where conversation_id = #{conversationId} and user_id = #{userId}
    </update>

    <!-- 增加未读消息数量 -->
    <update id="incrementUnreadCount">
        update conversation_member 
        set unread_count = unread_count + #{increment}
        where conversation_id = #{conversationId} and user_id = #{userId}
    </update>

    <!-- 清零未读消息数量 -->
    <update id="clearUnreadCount">
        update conversation_member 
        set unread_count = 0,
            last_read_message_id = #{lastReadMessageId}
        where conversation_id = #{conversationId} and user_id = #{userId}
    </update>

    <!-- 设置会话免打扰 -->
    <update id="updateMuteStatus">
        update conversation_member 
        set is_muted = #{isMuted}
        where conversation_id = #{conversationId} and user_id = #{userId}
    </update>

    <delete id="deleteConversationMemberById" parameterType="Long">
        delete from conversation_member where id = #{id}
    </delete>

    <delete id="deleteConversationMemberByIds" parameterType="String">
        delete from conversation_member where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除会话的所有成员 -->
    <delete id="deleteByConversationId">
        delete from conversation_member where conversation_id = #{conversationId}
    </delete>

    <!-- 用户离开会话（软删除） -->
    <update id="leaveConversation">
        update conversation_member 
        set status = 1, leave_time = NOW()
        where conversation_id = #{conversationId} and user_id = #{userId}
    </update>

    <!-- 检查用户是否在会话中 -->
    <select id="checkMemberExists" resultType="int">
        select count(1) from conversation_member 
        where conversation_id = #{conversationId} and user_id = #{userId} and status = 0
    </select>

</mapper> 