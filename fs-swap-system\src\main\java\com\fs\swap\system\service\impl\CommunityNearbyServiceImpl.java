package com.fs.swap.system.service.impl;

import java.util.List;

import com.fs.swap.common.core.domain.entity.CommunityNearby;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.CommunityNearbyMapper;
import com.fs.swap.system.service.ICommunityNearbyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 社区服务-周边推荐Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
public class CommunityNearbyServiceImpl implements ICommunityNearbyService
{
    @Autowired
    private CommunityNearbyMapper communityNearbyMapper;

    /**
     * 查询社区服务-周边推荐
     *
     * @param id 社区服务-周边推荐主键
     * @return 社区服务-周边推荐
     */
    @Override
    public CommunityNearby selectCommunityNearbyById(Long id)
    {
        return communityNearbyMapper.selectCommunityNearbyById(id);
    }

    /**
     * 查询社区服务-周边推荐列表
     *
     * @param communityNearby 社区服务-周边推荐
     * @return 社区服务-周边推荐
     */
    @Override
    public List<CommunityNearby> selectCommunityNearbyList(CommunityNearby communityNearby)
    {
        return communityNearbyMapper.selectCommunityNearbyList(communityNearby);
    }

    /**
     * 新增社区服务-周边推荐
     *
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    @Override
    public int insertCommunityNearby(CommunityNearby communityNearby)
    {
        communityNearby.setCreateTime(DateUtils.getNowDate());
        return communityNearbyMapper.insertCommunityNearby(communityNearby);
    }

    /**
     * 修改社区服务-周边推荐
     *
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    @Override
    public int updateCommunityNearby(CommunityNearby communityNearby)
    {
        communityNearby.setUpdateTime(DateUtils.getNowDate());
        return communityNearbyMapper.updateCommunityNearby(communityNearby);
    }

    /**
     * 批量删除社区服务-周边推荐
     *
     * @param ids 需要删除的社区服务-周边推荐主键
     * @return 结果
     */
    @Override
    public int deleteCommunityNearbyByIds(Long[] ids)
    {
        return communityNearbyMapper.deleteCommunityNearbyByIds(ids);
    }

    /**
     * 删除社区服务-周边推荐信息
     *
     * @param id 社区服务-周边推荐主键
     * @return 结果
     */
    @Override
    public int deleteCommunityNearbyById(Long id)
    {
        return communityNearbyMapper.deleteCommunityNearbyById(id);
    }

    /**
     * 根据用户所在位置查询适用的周边列表
     * 如果参数为空，则返回所有可用的周边服务
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 周边列表
     */
    @Override
    public List<CommunityNearby> selectCommunityNearbyListByLocation(Long regionId, Long communityId, Long residentialId)
    {
        return communityNearbyMapper.selectCommunityNearbyListByLocation(regionId, communityId, residentialId);
    }

    /**
     * 根据用户位置和距离查询周边列表
     *
     * @param userLocation 用户位置
     * @param maxDistance 最大距离（米）
     * @return 周边列表
     */
    @Override
    public List<CommunityNearby> selectCommunityNearbyListByDistance(String userLocation, Integer maxDistance)
    {
        return communityNearbyMapper.selectCommunityNearbyListByDistance(userLocation, maxDistance);
    }

    /**
     * 根据提交用户ID查询周边列表
     *
     * @param submitUserId 提交用户ID
     * @return 周边列表
     */
    @Override
    public List<CommunityNearby> selectCommunityNearbyListBySubmitUserId(Long submitUserId)
    {
        return communityNearbyMapper.selectCommunityNearbyListBySubmitUserId(submitUserId);
    }

    /**
     * 更新浏览次数
     *
     * @param id 服务ID
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long id)
    {
        return communityNearbyMapper.incrementViewCount(id);
    }

    /**
     * 更新拨打电话次数
     *
     * @param id 服务ID
     * @return 结果
     */
    @Override
    public int incrementCallCount(Long id)
    {
        return communityNearbyMapper.incrementCallCount(id);
    }

    /**
     * 更新导航次数
     *
     * @param id 服务ID
     * @return 结果
     */
    @Override
    public int incrementNavigateCount(Long id)
    {
        return communityNearbyMapper.incrementNavigateCount(id);
    }

    /**
     * 审核服务
     *
     * @param id 服务ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int auditCommunityNearby(Long id, Integer auditStatus, Long auditUserId, String auditRemark)
    {
        return communityNearbyMapper.auditCommunityNearby(id, auditStatus, auditUserId, DateUtils.getNowDate(), auditRemark);
    }

    /**
     * 批量审核服务
     *
     * @param ids 服务ID列表
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAuditCommunityNearby(List<Long> ids, Integer auditStatus, Long auditUserId, String auditRemark)
    {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (Long id : ids) {
            int result = communityNearbyMapper.auditCommunityNearby(id, auditStatus, auditUserId, DateUtils.getNowDate(), auditRemark);
            if (result > 0) {
                successCount++;
            }
        }
        return successCount;
    }

    /**
     * 根据标签搜索服务
     *
     * @param tags 标签列表
     * @return 服务列表
     */
    @Override
    public List<CommunityNearby> selectCommunityNearbyListByTags(List<String> tags)
    {
        return communityNearbyMapper.selectCommunityNearbyListByTags(tags);
    }

    /**
     * 获取推荐服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID  
     * @param residentialId 小区ID
     * @param limit 限制数量
     * @return 推荐服务列表
     */
    @Override
    public List<CommunityNearby> selectRecommendedCommunityNearby(Long regionId, Long communityId, Long residentialId, Integer limit)
    {
        return communityNearbyMapper.selectRecommendedCommunityNearby(regionId, communityId, residentialId, limit);
    }

    /**
     * 获取热门服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param category 分类
     * @param limit 限制数量
     * @return 热门服务列表
     */
    @Override
    public List<CommunityNearby> selectPopularCommunityNearby(Long regionId, Long communityId, String category, Integer limit)
    {
        return communityNearbyMapper.selectPopularCommunityNearby(regionId, communityId, category, limit);
    }

    /**
     * 用户提交服务信息
     *
     * @param communityNearby 服务信息
     * @param submitUserId 提交用户ID
     * @return 结果
     */
    @Override
    public int submitCommunityNearby(CommunityNearby communityNearby, Long submitUserId)
    {
        // 设置提交用户ID
        communityNearby.setSubmitUserId(submitUserId);
        
        // 设置审核状态为待审核
        communityNearby.setAuditStatus(0L);
        
        // 设置默认统计数据
        communityNearby.setViewCount(0);
        communityNearby.setCallCount(0);
        communityNearby.setNavigateCount(0);
        
        // 设置默认状态
        communityNearby.setIsRecommended(0);
        communityNearby.setIsOfficial(0);
        communityNearby.setStatus(1L);
        communityNearby.setSort(9999);
        
        // 设置创建时间
        communityNearby.setCreateTime(DateUtils.getNowDate());
        
        return communityNearbyMapper.insertCommunityNearby(communityNearby);
    }
}
