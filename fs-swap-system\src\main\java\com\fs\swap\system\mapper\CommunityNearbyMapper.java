package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.CommunityNearby;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 社区服务-周边推荐Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface CommunityNearbyMapper 
{
    /**
     * 查询社区服务-周边推荐
     * 
     * @param id 社区服务-周边推荐主键
     * @return 社区服务-周边推荐
     */
    public CommunityNearby selectCommunityNearbyById(Long id);

    /**
     * 查询社区服务-周边推荐列表
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 社区服务-周边推荐集合
     */
    public List<CommunityNearby> selectCommunityNearbyList(CommunityNearby communityNearby);

    /**
     * 新增社区服务-周边推荐
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    public int insertCommunityNearby(CommunityNearby communityNearby);

    /**
     * 修改社区服务-周边推荐
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    public int updateCommunityNearby(CommunityNearby communityNearby);

    /**
     * 删除社区服务-周边推荐
     * 
     * @param id 社区服务-周边推荐主键
     * @return 结果
     */
    public int deleteCommunityNearbyById(Long id);

    /**
     * 批量删除社区服务-周边推荐
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunityNearbyByIds(Long[] ids);
    
    /**
     * 根据用户所在位置查询适用的周边列表
     * 如果参数为空，则返回所有可用的周边服务
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByLocation(@Param("regionId") Long regionId, 
                                                                    @Param("communityId") Long communityId, 
                                                                    @Param("residentialId") Long residentialId);
    
    /**
     * 根据用户位置和距离查询周边列表
     *
     * @param userLocation 用户位置
     * @param maxDistance 最大距离（米）
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByDistance(@Param("userLocation") String userLocation, 
                                                                    @Param("maxDistance") Integer maxDistance);
    
    /**
     * 根据提交用户ID查询周边列表
     *
     * @param submitUserId 提交用户ID
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListBySubmitUserId(@Param("submitUserId") Long submitUserId);
    
    /**
     * 更新浏览次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementViewCount(@Param("id") Long id);
    
    /**
     * 更新拨打电话次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementCallCount(@Param("id") Long id);
    
    /**
     * 更新导航次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementNavigateCount(@Param("id") Long id);
    
    /**
     * 审核服务
     *
     * @param id 服务ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditTime 审核时间
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditCommunityNearby(@Param("id") Long id, 
                                   @Param("auditStatus") Integer auditStatus,
                                   @Param("auditUserId") Long auditUserId, 
                                   @Param("auditTime") java.util.Date auditTime,
                                   @Param("auditRemark") String auditRemark);
    
    /**
     * 根据标签搜索服务
     *
     * @param tags 标签列表
     * @return 服务列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByTags(@Param("tags") List<String> tags);
    
    /**
     * 获取推荐服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID  
     * @param residentialId 小区ID
     * @param limit 限制数量
     * @return 推荐服务列表
     */
    public List<CommunityNearby> selectRecommendedCommunityNearby(@Param("regionId") Long regionId,
                                                                  @Param("communityId") Long communityId,
                                                                  @Param("residentialId") Long residentialId,
                                                                  @Param("limit") Integer limit);
    
    /**
     * 获取热门服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param category 分类
     * @param limit 限制数量
     * @return 热门服务列表
     */
    public List<CommunityNearby> selectPopularCommunityNearby(@Param("regionId") Long regionId,
                                                              @Param("communityId") Long communityId,
                                                              @Param("category") String category,
                                                              @Param("limit") Integer limit);
}
