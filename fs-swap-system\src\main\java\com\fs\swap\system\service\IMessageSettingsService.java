package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.MessageSettings;

/**
 * 消息设置Service接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface IMessageSettingsService 
{
    /**
     * 查询用户消息设置
     * 
     * @param userId 用户ID
     * @return 消息设置
     */
    public MessageSettings selectMessageSettingsByUserId(Long userId);

    /**
     * 新增消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    public int insertMessageSettings(MessageSettings messageSettings);

    /**
     * 修改消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    public int updateMessageSettings(MessageSettings messageSettings);

    /**
     * 初始化用户消息设置（使用默认设置）
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int initUserMessageSettings(Long userId);

    /**
     * 设置消息免打扰
     * 
     * @param userId 用户ID
     * @param type 消息类型或'all'表示全部
     * @param mute 是否免打扰
     * @return 结果
     */
    public int setMessageMute(Long userId, String type, boolean mute);
} 