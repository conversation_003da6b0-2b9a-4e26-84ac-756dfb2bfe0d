/* pages/community-service/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f6f8;
  overflow: hidden;
}

/* 顶部筛选区域 */
.filter-header {
  background: #ffffff;
  padding: 10px 16px 6px;
  z-index: 100;
  position: relative;
  border-bottom: 1px solid #f0f2f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

/* 搜索框样式 */
.search-section {
  margin-bottom: 10px;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

/* 自定义van-search样式 */
.search-input {
  padding: 0 !important;
  background-color: transparent !important;
}

.search-input .van-search__content {
  background-color: #f8f9fb !important;
  border-radius: 12px !important;
  height: 36px !important;
  box-shadow: none !important;
  border: 1px solid #e8eaed !important;
  transition: all 0.2s ease !important;
  position: relative !important;
}

.search-input .van-search__content:focus-within {
  border-color: #3B7FFF !important;
  background-color: #ffffff !important;
  box-shadow: 0 0 0 3px rgba(59, 127, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

.search-input .van-search__content:hover:not(:focus-within) {
  border-color: #d0d3d9 !important;
  background-color: #ffffff !important;
}

.search-input .van-field__left-icon {
  color: #8a8e99 !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.search-input .van-cell {
  padding: 4px 16px !important;
  background-color: transparent !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

.search-input-field {
  color: #1f2329 !important;
  font-size: 14px !important;
  height: 28px !important;
  font-weight: 400 !important;
}

.search-input .van-field__placeholder {
  color: #8a8e99 !important;
  font-size: 14px !important;
}

/* 分类区域 */
.category-section {
  position: relative;
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 分类标签样式 */
.category-tabs {
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0;
  margin: 0 -16px;
  padding-left: 16px;
  padding-right: 16px;
  position: relative;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

/* 滑动指示器 */
.category-tabs::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 24px;
  background: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  pointer-events: none;
  z-index: 1;
}

.category-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 8px;
  margin-right: 6px;
  background-color: #f8f9fb;
  border-radius: 12px;
  min-width: auto;
  height: 22px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.category-tab:last-child {
  margin-right: 16px;
}

.category-tab:active {
  transform: scale(0.96);
}

.category-tab.active {
  background-color: #3B7FFF;
  border-color: #3B7FFF;
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.2);
  transform: translateY(-0.5px);
}

.category-tab:not(.active):hover {
  background-color: #f0f2f5;
  border-color: #e8eaed;
  transform: translateY(-0.5px);
}

.category-tab:not(.active):active {
  transform: scale(0.96) translateY(0);
}

.tab-text {
  font-size: 11px;
  font-weight: 500;
  color: #4e5969;
  transition: color 0.2s ease;
  line-height: 1;
  user-select: none;
}

.category-tab.active .tab-text {
  color: #ffffff;
  font-weight: 600;
}

/* 地图区域 */
.map-container {
  position: relative;
  height: calc(100vh - 140px);
  min-height: 400px;
}

.map {
  width: 100%;
  height: 100%;
  border-radius: 0;
}

.location-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: all 0.3s ease;
}

.location-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.location-icon {
  width: 20px;
  height: 20px;
}

/* 地图控制按钮 */
.map-controls {
  position: absolute;
  top: 70px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  z-index: 10;
}

.control-btn {
  width: 36px;
  height: 36px;
  background-color: #ffffff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.plus-icon, .minus-icon {
  font-size: 18px;
  line-height: 1;
}

/* 底部列表区域 */
.list-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  z-index: 10;
  box-shadow: 
    0 -8px 32px rgba(0, 0, 0, 0.08),
    0 -4px 16px rgba(0, 0, 0, 0.04),
    0 -2px 8px rgba(0, 0, 0, 0.02);
  overflow: visible;
  transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  height: 280px;
  margin-top: 40px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  will-change: height, transform;
}

.list-container.expanded {
  height: 70vh !important;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.list-container.show-detail {
  height: 85vh !important;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.list-container.collapsed {
  height: 80px !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 完全隐藏状态 */
.list-container[style*="height: 0px"] {
  height: 0px !important;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* 完全隐藏状态下的拖拽手柄仍然可见 */
.list-container[style*="height: 0px"] .drag-handle {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.list-container[style*="height: 0px"] .drag-handle .handle-bar {
}

/* 拖拽手柄 */
.drag-handle {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  padding: 8px 0;
  cursor: grab;
  background-color: transparent;
  border-radius: 12px;
  z-index: 11;
  width: 80px;
  transition: all 0.2s ease;
}

.drag-handle:active {
  cursor: grabbing;
  transform: translateX(-50%) scale(1.05);
}

.handle-bar {
  width: 40px;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.handle-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(59, 127, 255, 0.3), rgba(59, 127, 255, 0.6), rgba(59, 127, 255, 0.3));
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.25s ease;
}

.drag-handle:active .handle-bar {
  background-color: rgba(59, 127, 255, 0.4);
  transform: scaleX(1.2);
  box-shadow: 0 2px 8px rgba(59, 127, 255, 0.3);
}

.drag-handle:active .handle-bar::before {
  opacity: 1;
}

/* 拖拽手柄悬停效果 */
/* 拖拽手柄呼吸灯效果 */
/* 呼吸动画 */
/* 激活状态下停止呼吸动画 */

/* 服务详情区域 */
.service-detail {
  position: absolute;
  top: 36px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 20;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow-y: auto;
}

.service-detail.show {
  transform: translateY(0);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 21;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-info {
  display: flex;
  flex-direction: column;
}

.detail-title .service-name {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}

.close-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.close-detail:active {
  background-color: #e9ecef;
}

.detail-content {
  padding: 0 16px 20px;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f7ff;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px 0;
}

.info-text {
  font-size: 14px;
  color: #666666;
  flex: 1;
}

.detail-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.call-button {
  background-color: #3B7FFF;
  color: #ffffff;
}

.call-button:active {
  background-color: #2563EB;
  transform: scale(0.98);
}

.nav-button {
  background-color: #10B981;
  color: #ffffff;
}

.nav-button:active {
  background-color: #059669;
  transform: scale(0.98);
}

.button-text {
  font-size: 14px;
  font-weight: 500;
}

/* 详情区域中的服务标签 */
.detail-section .service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.detail-section .tag {
  font-size: 12px;
  background-color: #f0f7ff;
  color: #3B7FFF;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
  border: 1px solid #e0f0ff;
}

/* 遮罩层 */
.detail-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.detail-mask.show {
  opacity: 1;
  visibility: visible;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  flex-shrink: 0;
}

.list-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
  margin-right: 8px;
}

.count-text {
  font-size: 11px;
  color: #3B7FFF;
  background-color: #f0f7ff;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-btn {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: #f8f9fa;
  border-radius: 14px;
  font-size: 11px;
  color: #666666;
  gap: 3px;
}

.list-mode-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #f8f9fa;
  border-radius: 15px;
  color: #666666;
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  flex: 1;
}

.loading-text {
  font-size: 14px;
  color: #666666;
  margin-top: 16px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  flex: 1;
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-top: 16px;
  font-weight: 500;
}

.empty-tip {
  font-size: 12px;
  color: #cccccc;
  margin-top: 8px;
}

/* 服务列表 */
.service-list {
  flex: 1;
  padding: 0 12px 12px;
  box-sizing: border-box;
  overflow-y: auto;
}

.service-item {
  display: flex;
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 10px 12px;
  margin-bottom: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  gap: 12px;
}

.service-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3B7FFF, #1E64FF);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.service-item.selected::before {
  transform: scaleX(1);
}

.service-item.selected {
  border-color: #3B7FFF;
  box-shadow: 0 6px 24px rgba(59, 127, 255, 0.15);
  transform: translateY(-2px);
}

.service-item:active {
  transform: translateY(1px);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

/* 左侧图标区域 */
.service-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 12px;
}

.service-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
  position: relative;
  overflow: hidden;
}

.service-image {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
}

.service-icon.business {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
}

.service-icon.entertainment {
  background: linear-gradient(135deg, #4ECDC4, #6BCFCA);
}

.service-icon.facility {
  background: linear-gradient(135deg, #45B7D1, #6BC5D8);
}

.service-status {
  font-size: 9px;
  padding: 2px 5px;
  border-radius: 6px;
  min-width: 44px;
  text-align: center;
}

.status-text.open {
  background-color: #E8F5E8;
  color: #2E7D32;
}

.status-text.closed {
  background-color: #FFF3E0;
  color: #F57C00;
}

/* 主要信息区域 */
.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; /* 防止内容溢出 */
}

.service-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.service-name {
  font-size: 15px;
  font-weight: 600;
  color: #333333;
  line-height: 1.3;
}

.service-badge {
  background: linear-gradient(135deg, #FFB800, #FFC947);
  border-radius: 8px;
  padding: 2px 6px;
}

.badge-text {
  font-size: 10px;
  color: #ffffff;
  font-weight: 500;
}

.service-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1px;
}

.rating-distance {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.rating-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

.distance {
  font-size: 12px;
  color: #666666;
}

.view-count {
  font-size: 10px;
  color: #86909c;
  font-weight: 400;
  margin-left: 8px;
}

.update-time {
  font-size: 10px;
  color: #999999;
}

.service-address {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-top: 1px;
  padding-right: 60px; /* 为右侧按钮留出空间 */
}

/* 移除重复的样式，因为上面已经定义了 */

.address-text {
  font-size: 12px;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 1px;
}

.tag {
  font-size: 9px;
  background-color: #f0f7ff;
  color: #3B7FFF;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;
}

/* 悬浮操作按钮 */
.floating-actions {
  position: fixed;
  bottom: 300px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 100;
  transition: bottom 0.3s ease;
}

.floating-actions.list-expanded {
  bottom: calc(30vh + 20px);
}

.floating-actions.detail-shown {
  bottom: calc(15vh + 20px);
}

.fab-btn {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.refresh-btn {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.my-submissions-btn {
  background: linear-gradient(135deg, #FF9500, #FF7A00);
}

.add-btn {
  background: linear-gradient(135deg, #3B7FFF, #1E64FF);
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .filter-tabs {
    padding: 12px 12px 8px;
    gap: 6px;
  }
  
  .filter-tab {
    padding: 10px 6px;
    min-height: 56px;
  }
  
  .tab-text {
    font-size: 11px;
  }
  
  .service-item {
    padding: 14px;
  }
  
  .service-name {
    font-size: 15px;
  }
}

/* 地图信息提示 */
.map-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.info-content {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 16px;
  padding: 8px 16px;
  backdrop-filter: blur(10px);
}

.info-text {
  font-size: 12px;
  color: #ffffff;
}

/* 筛选结果提示 */
.filter-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.result-text {
  font-size: 12px;
  color: #666666;
}

.clear-filter {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 3px 8px;
  border-radius: 10px;
  background-color: #ffffff;
  transition: background-color 0.2s ease;
}

.clear-filter:active {
  background-color: #f0f0f0;
}

.clear-text {
  font-size: 11px;
  color: #999999;
}

/* 空状态操作按钮 */
.empty-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.empty-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background-color: #f8f9fa;
  border-radius: 20px;
  font-size: 13px;
  color: #666666;
  transition: all 0.2s ease;
}

.empty-btn:active {
  background-color: #e8f4fd;
  color: #3B7FFF;
  transform: scale(0.95);
}

/* 列表底部 */
.list-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  margin-top: 12px;
  background-color: #f8f9fa;
  border-radius: 12px;
}

.footer-text {
  font-size: 12px;
  color: #999999;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #ffffff;
  border-radius: 12px;
  font-size: 12px;
  color: #666666;
  transition: all 0.2s ease;
}

.footer-btn:active {
  background-color: #e8f4fd;
  color: #3B7FFF;
}

/* 悬浮按钮加载状态 */
.fab-btn.loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 展开提示 */
.expand-hint {
  position: absolute;
  top: 36px;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  z-index: 15;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.expand-hint.show {
  opacity: 1;
  visibility: visible;
}

.hint-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  border: none;
  transition: all 0.2s ease;
}

.expand-hint:active .hint-content {
  background-color: transparent;
  transform: scale(0.98);
}

.hint-text {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
  text-align: center;
}

/* 隐藏状态 */
.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.list-header.hidden,
.filter-result.hidden,
.service-list.hidden {
  transition: all 0.3s ease;
}

/* 服务信息卡片 */
.transport-card {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 200;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 40vh;
  overflow: hidden;
}

.transport-card.show {
  transform: translateY(0);
}

.transport-header {
  display: flex;
  flex-direction: column;
  padding: 20px 16px 16px;
  position: relative;
}

.service-basic-info {
  display: flex;
  gap: 12px;
  flex: 1;
  margin-bottom: 16px;
  padding-right: 40px; /* 为关闭按钮留出空间 */
}

.basic-info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service-rating-distance {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 1px;
  flex-wrap: wrap;
}

.visitor-count {
  font-size: 12px;
  color: #666666;
}

.service-type {
  font-size: 12px;
  color: #3B7FFF;
  background-color: #f0f7ff;
  padding: 2px 6px;
  border-radius: 8px;
}

.service-location {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 1px;
}

.distance-text {
  font-size: 13px;
  color: #333333;
  font-weight: 500;
}

.business-hours {
  margin-top: 2px;
}

.hours-text {
  font-size: 12px;
  color: #00C851;
  font-weight: 500;
}

.close-transport {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 6px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.close-transport:active {
  background-color: #f0f0f0;
}

/* 服务信息卡片操作按钮 - 重新设计为水平布局 */
.transport-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.transport-action-btn {
  flex: 1;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.call-btn {
  background-color: #3B7FFF;
  color: #ffffff;
}

.call-btn:active {
  background-color: #2563EB;
  transform: scale(0.98);
}

.nav-btn {
  background-color: #10B981;
  color: #ffffff;
}

.nav-btn:active {
  background-color: #059669;
  transform: scale(0.98);
}

/* 返回列表按钮 - 调整样式 */
.back-to-list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 20px;
  margin: 0 16px 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e8eaed;
  transition: all 0.2s ease;
}

.back-to-list:active {
  background-color: #e8eaed;
  transform: scale(0.98);
}

.back-text {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

/* 列表展开状态下的拖拽手柄样式 */
/* 列表收起状态下的拖拽手柄样式 */
/* 脉动动画 */

/* 服务项操作按钮 */
.service-action {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.get-btn {
  background: linear-gradient(135deg, #3B7FFF, #1E64FF);
  border-radius: 4px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(59, 127, 255, 0.2);
  min-width: 44px;
  height: 24px;
}

.get-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.3);
}

.get-btn-text {
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  line-height: 1;
}
