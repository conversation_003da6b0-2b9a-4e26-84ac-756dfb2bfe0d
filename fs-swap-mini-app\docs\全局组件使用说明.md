# 全局组件使用说明

## 概述

为了简化组件的使用和管理，我们已经将 `login-action` 和 `residential-auth` 组件配置为全局组件，并提供了统一的管理工具。

## 全局组件配置

在 `app.json` 中已经配置了全局组件：

```json
{
  "usingComponents": {
    "van-button": "@vant/weapp/button/index",
    "van-icon": "@vant/weapp/icon/index",
    "van-dialog": "@vant/weapp/dialog/index",
    "van-loading": "@vant/weapp/loading/index",
    "van-tab": "@vant/weapp/tab/index",
    "van-tabs": "@vant/weapp/tabs/index",
    "van-image": "@vant/weapp/image/index",
    "login-action": "/components/login-action/index",
    "residential-auth": "/components/residential-auth/index"
  }
}
```

## 页面中的使用

### 1. 在 WXML 中添加组件

在任何页面的 `.wxml` 文件中直接使用，无需在页面的 `.json` 文件中配置：

```xml
<!-- 登录组件 -->
<login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>

<!-- 小区认证弹框 -->
<residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />
```

### 2. 在 JS 中使用全局组件工具

#### 方式一：直接调用工具函数

```javascript
// 引入全局组件工具
const globalComponents = require('../../utils/globalComponents')

Page({
  data: {
    // 页面数据
  },

  // 显示登录组件
  showLogin() {
    globalComponents.showLoginComponent(this, true)
  },

  // 显示小区认证组件
  showResidentialAuth() {
    globalComponents.showResidentialAuthComponent(this)
  },

  // 登录成功处理
  onLoginSuccess() {
    console.log('登录成功')
    // 处理登录成功逻辑
  },

  // 小区认证确认处理
  onConfirmResidentialAuth(e) {
    console.log('小区认证确认', e.detail)
    // 处理小区认证确认逻辑
  }
})
```

#### 方式二：使用混入对象（推荐）

```javascript
// 引入全局组件工具
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  data: {
    // 页面数据
  },

  // 自定义方法
  customMethod() {
    // 直接调用混入的方法
    this.showLogin()
    this.showResidentialAuth()
  },

  // 可以重写混入的方法来添加特定逻辑
  onLoginSuccess() {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginSuccess.call(this)
    
    // 添加页面特定的处理逻辑
    console.log('页面特定的登录成功处理')
  }

}, globalComponents.globalComponentsMixin))
```

## 可用的方法

### 工具函数

- `showLoginComponent(pageContext, autoClose)` - 显示登录组件
- `hideLoginComponent(pageContext)` - 隐藏登录组件
- `showResidentialAuthComponent(pageContext)` - 显示小区认证组件
- `hideResidentialAuthComponent(pageContext)` - 隐藏小区认证组件

### 混入方法

- `showLogin(autoClose)` - 显示登录组件
- `hideLogin()` - 隐藏登录组件
- `showResidentialAuth()` - 显示小区认证组件
- `hideResidentialAuth()` - 隐藏小区认证组件
- `onLoginSuccess()` - 登录成功处理
- `onLoginFail(e)` - 登录失败处理
- `onConfirmResidentialAuth(e)` - 小区认证确认处理
- `onCloseResidentialAuth()` - 小区认证关闭处理

## 迁移指南

### 清理页面配置

如果页面的 `.json` 文件中还有这两个组件的配置，请删除：

```json
// 删除这些配置
{
  "usingComponents": {
    "login-action": "/components/login-action/index",
    "residential-auth": "/components/residential-auth/index"
  }
}
```

### 更新页面代码

将现有的组件操作代码替换为使用全局工具：

```javascript
// 旧代码
const loginComp = this.selectComponent('#loginAction')
if (loginComp) {
  loginComp.show()
}

// 新代码
this.showLogin()
```

## 优势

1. **统一管理**：所有组件操作都通过统一的接口
2. **减少重复**：不需要在每个页面重复配置组件
3. **易于维护**：组件路径变更时只需修改一处
4. **错误处理**：内置了完善的错误处理和提示
5. **代码简洁**：页面代码更加简洁易读

## 注意事项

1. 确保页面的 WXML 中包含了正确的组件标签和 ID
2. 使用混入对象时，注意方法名冲突
3. 重写混入方法时，可以选择是否调用父类方法
