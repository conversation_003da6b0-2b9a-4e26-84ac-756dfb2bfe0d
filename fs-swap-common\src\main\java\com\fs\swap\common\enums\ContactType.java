package com.fs.swap.common.enums;

/**
 * 联系方式类型枚举
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public enum ContactType implements BaseEnum {

    /**
     * 手机号
     */
    MOBILE("1", "手机号"),

    /**
     * 微信号
     */
    WECHAT_ID("2", "微信号"),

    /**
     * 微信二维码
     */
    WECHAT_QR("3", "微信二维码");

    private final String code;
    private final String info;

    ContactType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 获取联系方式类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取联系方式类型信息
     */
    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 联系方式类型枚举
     */
    public static ContactType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ContactType type : ContactType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断代码是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
