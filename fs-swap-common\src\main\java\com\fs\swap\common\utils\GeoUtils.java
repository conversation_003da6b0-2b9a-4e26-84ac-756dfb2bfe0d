package com.fs.swap.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**
 * 地理位置工具类
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
public class GeoUtils {

    private static final Pattern POINT_PATTERN = Pattern.compile("POINT\\(\\s*([\\d.-]+)\\s+([\\d.-]+)\\s*\\)");
    private static final Pattern COORD_PATTERN = Pattern.compile("^([\\d.-]+),\\s*([\\d.-]+)$");

    /**
     * 将POINT格式的位置信息转换为经度,纬度格式
     *
     * @param pointLocation POINT(经度 纬度)格式的字符串
     * @return 经度, 纬度格式的字符串，转换失败则返回原字符串
     */
    public static String convertPointToCoordinates(String pointLocation) {
        if (pointLocation == null || !pointLocation.startsWith("POINT(")) {
            return pointLocation;
        }

        try {
            // 提取POINT(x y)中的坐标
            String pointContent = pointLocation.substring(6, pointLocation.length() - 1);
            String[] coordinates = pointContent.trim().split("\\s+");
            if (coordinates.length == 2) {
                // 重新组合为经度,纬度格式
                return coordinates[0] + "," + coordinates[1];
            }
        } catch (Exception e) {
            // 转换失败，返回原字符串
        }

        return pointLocation;
    }

    /**
     * 解析坐标信息，支持两种格式：
     * 1. POINT(经度 纬度)
     * 2. 经度,纬度
     *
     * @param location 坐标字符串
     * @return 坐标数组，[0]为经度，[1]为纬度，如果解析失败返回null
     */
    public static String[] parseCoordinates(String location) {
        if (location == null || location.isEmpty()) {
            return null;
        }

        // 先尝试解析POINT格式
        Matcher pointMatcher = POINT_PATTERN.matcher(location);
        if (pointMatcher.find()) {
            String longitude = pointMatcher.group(1); // 经度
            String latitude = pointMatcher.group(2);  // 纬度
            return new String[]{longitude, latitude};
        }

        // 再尝试解析经度,纬度格式
        Matcher coordMatcher = COORD_PATTERN.matcher(location);
        if (coordMatcher.find()) {
            String longitude = coordMatcher.group(1); // 经度
            String latitude = coordMatcher.group(2);  // 纬度
            return new String[]{longitude, latitude};
        }

        return null;
    }

    /**
     * 将经度,纬度格式的位置信息转换为POINT格式
     *
     * @param coordinatesLocation 经度,纬度格式的字符串
     * @return POINT(经度 纬度)格式的字符串，转换失败则返回原字符串
     */
    public static String convertCoordinatesToPoint(String coordinatesLocation) {
        if (coordinatesLocation == null || coordinatesLocation.startsWith("POINT(")) {
            return coordinatesLocation;
        }

        try {
            String[] coordinates = coordinatesLocation.split(",");
            if (coordinates.length == 2) {
                // 重新组合为POINT(经度 纬度)格式
                return "POINT(" + coordinates[0].trim() + " " + coordinates[1].trim() + ")";
            }
        } catch (Exception e) {
            // 转换失败，返回原字符串
        }

        return coordinatesLocation;
    }

    /**
     * 验证POINT格式字符串是否有效
     *
     * @param location POINT格式字符串
     * @return 是否为有效的POINT格式
     */
    public static boolean isValidPointFormat(String location) {
        if (StringUtils.isBlank(location)) {
            return false;
        }

        // 符合OGC WKT标准的POINT格式正则表达式
        String pointPattern = "^POINT\\(\\s*[-+]?(\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?\\s+[-+]?(\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?\\s*\\)$";
        return location.matches(pointPattern);
    }

    /**
     * 验证坐标是否在有效范围内
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 坐标是否有效
     */
    public static boolean isValidCoordinates(double longitude, double latitude) {
        return longitude >= -180.0 && longitude <= 180.0 &&
                latitude >= -90.0 && latitude <= 90.0 &&
                !Double.isNaN(longitude) && !Double.isNaN(latitude) &&
                !Double.isInfinite(longitude) && !Double.isInfinite(latitude);
    }

    /**
     * 规范化POINT格式字符串
     * 将任意格式的位置信息转换为标准的POINT格式
     *
     * @param location 位置字符串
     * @return 规范化的POINT格式字符串
     */
    public static String normalizePointFormat(String location) {
        String[] coords = parseCoordinates(location);
        if (coords != null && coords.length == 2) {
            try {
                double longitude = Double.parseDouble(coords[0]);
                double latitude = Double.parseDouble(coords[1]);

                if (isValidCoordinates(longitude, latitude)) {
                    // 保留6位小数精度（约1米精度）
                    return String.format("POINT(%.6f %.6f)", longitude, latitude);
                }
            } catch (NumberFormatException e) {
                // 坐标解析失败
            }
        }
        return location; // 如果无法规范化，返回原始值
    }

    /**
     * 创建标准POINT格式字符串
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return POINT格式字符串，如果坐标无效则返回null
     */
    public static String createPoint(double longitude, double latitude) {
        if (!isValidCoordinates(longitude, latitude)) {
            return null;
        }
        return String.format("POINT(%.6f %.6f)", longitude, latitude);
    }

    /**
     * 将POINT格式转换为地图API需要的字符串格式
     *
     * @param location POINT格式字符串
     * @return 经度, 纬度格式字符串
     */
    public static String pointToMapApiFormat(String location) {
        String[] coords = parseCoordinates(location);
        if (coords != null && coords.length == 2) {
            return coords[0] + "," + coords[1];
        }
        return null;
    }

    /**
     * 计算两个位置之间的距离（使用Haversine公式）
     *
     * @param lat1 第一个位置的纬度
     * @param lon1 第一个位置的经度
     * @param lat2 第二个位置的纬度
     * @param lon2 第二个位置的经度
     * @return 距离（单位：千米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径（千米）
        final double EARTH_RADIUS = 6371.0;

        // 将角度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // 计算纬度和经度的差值
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        // 使用Haversine公式计算距离
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 计算两个位置之间的距离并返回格式化的距离文本
     *
     * @param point1 第一个位置点，格式：POINT(经度 纬度)
     * @param point2 第二个位置点，格式：POINT(经度 纬度)
     */
    public static double calculateDistance(String point1, String point2) {
        if (StringUtils.isEmpty(point1) || StringUtils.isEmpty(point2)) {
            return 0;
        }

        // 解析第一个点的坐标
        double[] coords1 = parsePointCoordinates(point1);
        double lon1 = coords1[0];
        double lat1 = coords1[1];

        // 解析第二个点的坐标
        double[] coords2 = parsePointCoordinates(point2);
        double lon2 = coords2[0];
        double lat2 = coords2[1];

        // 计算距离
        double distanceInKm = calculateDistance(lat1, lon1, lat2, lon2);

        return distanceInKm * 1000;
    }

    /**
     * 解析POINT字符串格式的坐标
     *
     * @param pointStr POINT字符串，格式：POINT(经度 纬度)
     * @return 坐标数组 [经度, 纬度]
     */
    private static double[] parsePointCoordinates(String pointStr) {
        // 移除 "POINT(" 前缀和 ")" 后缀
        String coordinates = pointStr.replace("POINT(", "").replace(")", "").trim();

        // 按空格分割坐标
        String[] parts = coordinates.split("\\s+");
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid POINT format: " + pointStr);
        }

        double longitude = Double.parseDouble(parts[0]);
        double latitude = Double.parseDouble(parts[1]);

        return new double[]{longitude, latitude};
    }
}
