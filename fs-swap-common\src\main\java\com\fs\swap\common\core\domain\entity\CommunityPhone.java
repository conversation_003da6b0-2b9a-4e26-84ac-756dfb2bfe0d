package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fs.swap.common.annotation.Excel;

/**
 * 社区服务-常用电话对象 community_phone
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public class CommunityPhone extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 电话ID */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String phoneNumber;

    /** 分类(1:物业服务,2:便民服务,3:紧急救援) */
    @Excel(name = "分类(1:物业服务,2:便民服务,3:紧急救援)")
    private String category;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 关联社区ID */
    @Excel(name = "关联社区ID")
    private Long communityId;

    /** 关联小区ID */
    @Excel(name = "关联小区ID")
    private Long residentialId;

    /** 提交用户ID */
    @Excel(name = "提交用户ID")
    private Long submitUserId;

    /** 审核状态(0:待审核,1:已通过,2:已拒绝) */
    @Excel(name = "审核状态(0:待审核,1:已通过,2:已拒绝)")
    private Long auditStatus;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long auditUserId;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private java.util.Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 状态(0:停用,1:正常) */
    @Excel(name = "状态(0:停用,1:正常)")
    private Long status;

    /** 拨打次数 */
    @Excel(name = "拨打次数")
    private Integer callCount;

    /** 是否删除(0:否,1:是) */
    private Long deleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPhoneNumber(String phoneNumber) 
    {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() 
    {
        return phoneNumber;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setCommunityId(Long communityId) 
    {
        this.communityId = communityId;
    }

    public Long getCommunityId() 
    {
        return communityId;
    }
    public void setResidentialId(Long residentialId) 
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId() 
    {
        return residentialId;
    }
    public void setSubmitUserId(Long submitUserId) 
    {
        this.submitUserId = submitUserId;
    }

    public Long getSubmitUserId() 
    {
        return submitUserId;
    }
    public void setAuditStatus(Long auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Long getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditUserId(Long auditUserId) 
    {
        this.auditUserId = auditUserId;
    }

    public Long getAuditUserId() 
    {
        return auditUserId;
    }
    public void setAuditTime(java.util.Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public java.util.Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }
    public void setSort(Integer sort) 
    {
        this.sort = sort;
    }

    public Integer getSort() 
    {
        return sort;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setCallCount(Integer callCount) 
    {
        this.callCount = callCount;
    }

    public Integer getCallCount() 
    {
        return callCount;
    }
    public void setDeleted(Long deleted) 
    {
        this.deleted = deleted;
    }

    public Long getDeleted() 
    {
        return deleted;
    }

    @Override    public String toString() {        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)            .append("id", getId())            .append("name", getName())            .append("phoneNumber", getPhoneNumber())            .append("category", getCategory())            .append("description", getDescription())            .append("communityId", getCommunityId())            .append("residentialId", getResidentialId())            .append("submitUserId", getSubmitUserId())            .append("auditStatus", getAuditStatus())            .append("auditUserId", getAuditUserId())            .append("auditTime", getAuditTime())            .append("auditRemark", getAuditRemark())            .append("sort", getSort())            .append("status", getStatus())            .append("callCount", getCallCount())            .append("createBy", getCreateBy())            .append("createTime", getCreateTime())            .append("updateBy", getUpdateBy())            .append("updateTime", getUpdateTime())            .append("deleted", getDeleted())            .toString();    }
}
