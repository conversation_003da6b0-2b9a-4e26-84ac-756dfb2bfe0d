const messageService = require('../../../services/message')

Page({
  data: {
    contactList: [],
    isLoading: false,
    searchKeyword: ''
  },

  onLoad() {
    this.loadContactList()
  },

  /**
   * 加载联系人列表
   */
  async loadContactList() {
    try {
      this.setData({ isLoading: true })
      
      // 这里应该调用联系人接口，暂时使用模拟数据
      const mockContacts = [
        {
          userId: 2,
          nickname: '张三',
          avatar: '/static/img/default-avatar.png',
          isOnline: true
        },
        {
          userId: 3,
          nickname: '李四',
          avatar: '/static/img/default-avatar.png',
          isOnline: false
        },
        {
          userId: 4,
          nickname: '王五',
          avatar: '/static/img/default-avatar.png',
          isOnline: true
        }
      ]
      
      this.setData({
        contactList: mockContacts
      })
      
    } catch (error) {
      console.error('加载联系人列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 搜索联系人
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    
    // 简单的本地搜索过滤
    this.filterContacts(keyword)
  },

  /**
   * 过滤联系人
   */
  filterContacts(keyword) {
    if (!keyword.trim()) {
      this.loadContactList()
      return
    }
    
    const filteredList = this.data.contactList.filter(contact => 
      contact.nickname.includes(keyword)
    )
    
    this.setData({
      contactList: filteredList
    })
  },

  /**
   * 选择联系人
   */
  async onContactSelect(e) {
    const { contact } = e.currentTarget.dataset
    
    try {
      wx.showLoading({ title: '创建会话...' })
      
      const result = await messageService.createConversation(contact.userId)
      const conversationId = result.data.conversationId
      
      wx.hideLoading()
      
      // 跳转到聊天页面
      wx.navigateTo({
        url: `/pages/conversation/detail/index?conversationId=${conversationId}&title=${contact.nickname}`
      })
      
    } catch (error) {
      wx.hideLoading()
      console.error('创建会话失败:', error)
      wx.showToast({
        title: '创建会话失败',
        icon: 'none'
      })
    }
  }
}) 
 