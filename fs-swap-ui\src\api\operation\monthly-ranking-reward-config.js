import request from '@/utils/request'

// 查询月度排行榜奖励配置列表
export function listRewardConfig(query) {
  return request({
    url: '/operation/monthly-ranking-reward-config/list',
    method: 'get',
    params: query
  })
}

// 查询月度排行榜奖励配置详细
export function getRewardConfig(id) {
  return request({
    url: '/operation/monthly-ranking-reward-config/' + id,
    method: 'get'
  })
}

// 新增月度排行榜奖励配置
export function addRewardConfig(data) {
  return request({
    url: '/operation/monthly-ranking-reward-config',
    method: 'post',
    data: data
  })
}

// 修改月度排行榜奖励配置
export function updateRewardConfig(data) {
  return request({
    url: '/operation/monthly-ranking-reward-config',
    method: 'put',
    data: data
  })
}

// 删除月度排行榜奖励配置
export function delRewardConfig(id) {
  return request({
    url: '/operation/monthly-ranking-reward-config/' + id,
    method: 'delete'
  })
}

// 获取启用的奖励配置列表
export function getActiveRewardConfigs() {
  return request({
    url: '/operation/monthly-ranking-reward-config/active',
    method: 'get'
  })
}

// 切换奖励配置状态
export function changeRewardConfigStatus(id, isActive) {
  const data = {
    id,
    isActive
  }
  return request({
    url: '/operation/monthly-ranking-reward-config/changeStatus',
    method: 'put',
    data: data
  })
} 