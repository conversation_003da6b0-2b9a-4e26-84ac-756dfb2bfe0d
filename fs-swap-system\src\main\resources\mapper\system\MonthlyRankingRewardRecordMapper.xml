<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.MonthlyRankingRewardRecordMapper">

    <resultMap type="MonthlyRankingRewardRecord" id="MonthlyRankingRewardRecordResult">
        <result property="id"              column="id"                />
        <result property="yearMonth"       column="year_month"        />
        <result property="residentialId"   column="residential_id"    />
        <result property="userId"          column="user_id"           />
        <result property="totalSilver"     column="total_silver"      />
        <result property="rankPosition"    column="rank_position"     />
        <result property="rewardConfigId"  column="reward_config_id"  />
        <result property="rewardType"      column="reward_type"       />
        <result property="rewardAmount"    column="reward_amount"     />
        <result property="rewardName"      column="reward_name"       />
        <result property="status"          column="status"            />
        <result property="issuedTime"      column="issued_time"       />
        <result property="createTime"      column="create_time"       />
        <result property="updateTime"      column="update_time"       />
        <result property="nickname"        column="nickname"          />
        <result property="avatar"          column="avatar"            />
    </resultMap>

    <sql id="selectMonthlyRankingRewardRecordVo">
        select id, `year_month`, residential_id, user_id, total_silver, rank_position, reward_config_id, reward_type, reward_amount, reward_name, status, issued_time, create_time, update_time from monthly_ranking_reward_record
    </sql>

    <select id="selectMonthlyRankingRewardRecordList" parameterType="MonthlyRankingRewardRecord" resultMap="MonthlyRankingRewardRecordResult">
        <include refid="selectMonthlyRankingRewardRecordVo"/>
        <where>
            <if test="yearMonth != null and yearMonth != ''"> and `year_month` = #{yearMonth}</if>
            <if test="residentialId != null"> and residential_id = #{residentialId}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="rankPosition != null"> and rank_position = #{rankPosition}</if>
            <if test="rewardConfigId != null"> and reward_config_id = #{rewardConfigId}</if>
            <if test="rewardType != null and rewardType != ''"> and reward_type = #{rewardType}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
        order by `year_month` desc, residential_id, rank_position
    </select>

    <select id="selectMonthlyRankingRewardRecordById" parameterType="Long" resultMap="MonthlyRankingRewardRecordResult">
        <include refid="selectMonthlyRankingRewardRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectRewardRecordWithUserInfo" resultMap="MonthlyRankingRewardRecordResult">
        select 
            rr.id, rr.`year_month`, rr.residential_id, rr.user_id, rr.total_silver, rr.rank_position, rr.reward_config_id, 
            rr.reward_type, rr.reward_amount, rr.reward_name, 
            rr.status, rr.issued_time, rr.create_time, rr.update_time,
            ui.nickname, ui.avatar
        from monthly_ranking_reward_record rr
        left join user_info ui on rr.user_id = ui.id
        where rr.`year_month` = #{yearMonth}
        <if test="residentialId != null"> and rr.residential_id = #{residentialId}</if>
        order by rr.residential_id, rr.rank_position
    </select>

    <select id="selectRewardRecordWithUserInfoPaged" resultMap="MonthlyRankingRewardRecordResult">
        select 
            rr.id, rr.`year_month`, rr.residential_id, rr.user_id, rr.total_silver, rr.rank_position, rr.reward_config_id, 
            rr.reward_type, rr.reward_amount, rr.reward_name, 
            rr.status, rr.issued_time, rr.create_time, rr.update_time,
            ui.nickname, ui.avatar
        from monthly_ranking_reward_record rr
        left join user_info ui on rr.user_id = ui.id
        where rr.`year_month` = #{yearMonth}
        <if test="residentialId != null"> and rr.residential_id = #{residentialId}</if>
        order by rr.residential_id, rr.rank_position
        limit #{pageSize} offset #{pageNum}
    </select>

    <select id="countRewardRecord" resultType="java.lang.Long">
        select count(1)
        from monthly_ranking_reward_record rr
        where rr.`year_month` = #{yearMonth}
        <if test="residentialId != null"> and rr.residential_id = #{residentialId}</if>
    </select>

    <select id="selectUserRewardHistory" parameterType="Long" resultMap="MonthlyRankingRewardRecordResult">
        select 
            rr.id, rr.`year_month`, rr.residential_id, rr.user_id, rr.total_silver, rr.rank_position, rr.reward_config_id, 
            rr.reward_type, rr.reward_amount, rr.reward_name, 
            rr.status, rr.issued_time, rr.create_time, rr.update_time
        from monthly_ranking_reward_record rr
        where rr.user_id = #{userId}
        order by rr.`year_month` desc
    </select>

    <select id="selectPendingRewards" resultMap="MonthlyRankingRewardRecordResult">
        <include refid="selectMonthlyRankingRewardRecordVo"/>
        where status = 'PENDING'
        order by create_time
    </select>

    <insert id="insertMonthlyRankingRewardRecord" parameterType="MonthlyRankingRewardRecord" useGeneratedKeys="true" keyProperty="id">
        insert into monthly_ranking_reward_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">`year_month`,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalSilver != null">total_silver,</if>
            <if test="rankPosition != null">rank_position,</if>
            <if test="rewardConfigId != null">reward_config_id,</if>
            <if test="rewardType != null and rewardType != ''">reward_type,</if>
            <if test="rewardAmount != null">reward_amount,</if>
            <if test="rewardName != null and rewardName != ''">reward_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="issuedTime != null">issued_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">#{yearMonth},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalSilver != null">#{totalSilver},</if>
            <if test="rankPosition != null">#{rankPosition},</if>
            <if test="rewardConfigId != null">#{rewardConfigId},</if>
            <if test="rewardType != null and rewardType != ''">#{rewardType},</if>
            <if test="rewardAmount != null">#{rewardAmount},</if>
            <if test="rewardName != null and rewardName != ''">#{rewardName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="issuedTime != null">#{issuedTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertRewardRecords" parameterType="java.util.List">
        insert into monthly_ranking_reward_record 
        (`year_month`, residential_id, user_id, total_silver, rank_position, reward_config_id, reward_type, reward_amount, reward_name, status, create_time)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.yearMonth}, #{record.residentialId}, #{record.userId}, #{record.totalSilver}, #{record.rankPosition}, #{record.rewardConfigId}, 
             #{record.rewardType}, #{record.rewardAmount}, #{record.rewardName}, 
             #{record.status}, #{record.createTime})
        </foreach>
    </insert>

    <update id="updateMonthlyRankingRewardRecord" parameterType="MonthlyRankingRewardRecord">
        update monthly_ranking_reward_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">`year_month` = #{yearMonth},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalSilver != null">total_silver = #{totalSilver},</if>
            <if test="rankPosition != null">rank_position = #{rankPosition},</if>
            <if test="rewardConfigId != null">reward_config_id = #{rewardConfigId},</if>
            <if test="rewardType != null and rewardType != ''">reward_type = #{rewardType},</if>
            <if test="rewardAmount != null">reward_amount = #{rewardAmount},</if>
            <if test="rewardName != null and rewardName != ''">reward_name = #{rewardName},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="issuedTime != null">issued_time = #{issuedTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateRewardStatus">
        update monthly_ranking_reward_record 
        set status = #{status}, issued_time = now(), update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteMonthlyRankingRewardRecordById" parameterType="Long">
        delete from monthly_ranking_reward_record where id = #{id}
    </delete>

    <delete id="deleteMonthlyRankingRewardRecordByIds" parameterType="String">
        delete from monthly_ranking_reward_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearMonth" parameterType="String">
        delete from monthly_ranking_reward_record where `year_month` = #{yearMonth}
    </delete>

    <delete id="deleteByResidentialAndYearMonth">
        delete from monthly_ranking_reward_record 
        where `year_month` = #{yearMonth} and residential_id = #{residentialId}
    </delete>

</mapper> 