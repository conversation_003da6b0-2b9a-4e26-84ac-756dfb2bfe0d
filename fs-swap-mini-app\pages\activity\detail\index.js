// pages/activity/detail/index.js
const api = require('../../../config/api.js')
const userUtils = require('../../../utils/user')
const systemInfoService = require('../../../services/systemInfo.js')
const globalComponents = require('../../../utils/globalComponents')
const app = getApp()

Page(Object.assign({
  data: {
    activityId: '',
    activity: {},
    loading: true,
    hasLogin: false,
    // 评论相关
    comments: [],
    commentLoading: false,
    commentPageNum: 1,
    commentPageSize: 10,
    hasMoreComments: true,
    showCommentInput: false,
    commentContent: '',
    replyToId: null,
    replyToName: '',
    parentCommentId: null,
    // 报名相关
    showSignupPopup: false,
    additionalInfo: '',
    isSignedUp: false,
    // 联系方式弹窗
    showContactModal: false,
    targetNickname: '',
    contacts: [],
    fileUrl: '',
    // 地图相关
    mapInfo: {
      longitude: 0,
      latitude: 0,
      markers: []
    },
    // 分页参数
    signupPageNum: 1,
    signupPageSize: 10,
    hasMoreSignups: true,
    loadingContact: false
  },

  onLoad(options) {
    if (options.id) {
      // 获取文件服务器URL前缀
      const fileUrl = wx.getStorageSync('systemInfo').fileUrl || '';

      this.setData({
        activityId: options.id,
        fileUrl: fileUrl
      })
      this.loadActivityDetail()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    // 如果地图已初始化，确保地图正确显示
    if (this.data.activity && this.data.activity.location && this.mapContext) {
      setTimeout(() => {
        this.mapContext.moveToLocation({
          longitude: this.data.mapInfo.longitude,
          latitude: this.data.mapInfo.latitude
        })
      }, 300)
    }
  },

  onReady() {
    // 页面渲染完成后，如果有地图数据，初始化地图上下文
    if (this.data.activity && this.data.activity.location) {
      this.mapContext = wx.createMapContext('activityMap', this)
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.refreshing) return

    this.setData({ refreshing: true })

    // 重新加载活动详情
    this.loadActivityDetail().then(() => {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh()
      this.setData({ refreshing: false })
    }).catch(() => {
      wx.stopPullDownRefresh()
      this.setData({ refreshing: false })
    })
  },

  // 加载活动详情
  async loadActivityDetail() {
    try {
      this.setData({ loading: true })

      const res = await api.getActivityDetail(this.data.activityId)
      if (res.code === 200) {
        // 格式化时间
        const activity = res.data
        // 使用系统信息服务处理图片
        try {
          if (activity.images) {
            const imageResult = await systemInfoService.processImageUrls(activity.images)
            // 处理图片数组
            if (imageResult.images) {
              activity.imageUrls = imageResult.images.split(',').filter(url => url.trim())
            } else {
              activity.imageUrls = []
            }
            
            // 保持原始格式
            activity.images = imageResult.images || ''
          } else {
            activity.imageUrls = []
            activity.images = ''
          }
        } catch (imageError) {
          console.error('图片处理失败:', imageError)
          // 图片处理失败不影响主流程
          activity.imageUrls = []
          activity.images = ''
        }

        // 检查是否是活动发布者
        const app = getApp()
        const isOwner = app.globalData.hasLogin && app.globalData.userInfo &&
                        app.globalData.userInfo.id === activity.userId

        // 初始化地图数据
        try {
          if (activity.location) {
            this.initMapData(activity.location, activity.title, activity.address)
          }
        } catch (mapError) {
          console.error('地图初始化失败:', mapError)
          // 地图初始化失败不影响主流程
        }

        this.setData({
          activity,
          isOwner,
          isSignedUp: activity.isSignedUp
        })

        // 加载评论列表
        try {
          this.loadComments()
        } catch (commentError) {
          console.error('加载评论失败:', commentError)
        }

        // 加载报名列表（如果是活动发布者）
        if (isOwner) {
          try {
            this.loadSignups(true) // 使用 true 参数强制刷新报名列表
          } catch (signupError) {
            console.error('加载报名列表失败:', signupError)
          }
        }

        } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载活动详情异常:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载评论列表
  async loadComments(refresh = false) {
    if (this.data.commentLoading) return

    try {
      this.setData({ commentLoading: true })

      if (refresh) {
        this.setData({
          commentPageNum: 1,
          hasMoreComments: true,
          comments: []
        })
      }

      const params = {
        pageNum: this.data.commentPageNum,
        pageSize: this.data.commentPageSize
      }

      const res = await api.getActivityComments(this.data.activityId, params)

      if (res.code === 200) {
        const { rows, total } = res
        const hasMoreComments = this.data.commentPageNum * this.data.commentPageSize < total

        // 确保 rows 是数组
        const commentRows = Array.isArray(rows) ? rows : []

        this.setData({
          comments: refresh ? commentRows : [...this.data.comments, ...commentRows],
          hasMoreComments,
          commentPageNum: this.data.commentPageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载评论失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载评论异常:', error)
    } finally {
      this.setData({ commentLoading: false })
    }
  },

  // 加载报名列表
  async loadSignups(refresh = false) {
    if (this.data.signupLoading || !this.data.isOwner) return

    try {
      this.setData({ signupLoading: true })

      if (refresh) {
        this.setData({
          signupPageNum: 1,
          hasMoreSignups: true,
          signups: []
        })
      }

      const params = {
        pageNum: this.data.signupPageNum,
        pageSize: this.data.signupPageSize
      }

      const res = await api.getActivitySignups(this.data.activityId, params)

      if (res.code === 200) {
        const { rows, total } = res
        const hasMoreSignups = this.data.signupPageNum * this.data.signupPageSize < total

        // 确保 rows 是数组
        const signupRows = Array.isArray(rows) ? rows : []

        this.setData({
          signups: refresh ? signupRows : [...this.data.signups, ...signupRows],
          hasMoreSignups,
          signupPageNum: this.data.signupPageNum + 1
        })
      } else {
        wx.showToast({
          title: res.msg || '加载报名列表失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载报名列表异常:', error)
    } finally {
      this.setData({ signupLoading: false })
    }
  },

  // 显示评论输入框
  showCommentInput(e) {
    // 检查登录状态
    if (!this.ensureLogin()) return

    const replyToId = e.currentTarget.dataset.id
    const replyToName = e.currentTarget.dataset.name

    this.setData({
      showCommentInput: true,
      replyToId: replyToId || null,
      replyToName: replyToName || ''
    })
  },

  // 隐藏评论输入框
  hideCommentInput() {
    this.setData({
      showCommentInput: false,
      commentContent: '',
      replyToId: null,
      replyToName: ''
    })
  },

  // 评论内容变化
  onCommentChange(e) {
    this.setData({
      commentContent: e.detail
    })
  },

  // 提交评论
  async submitComment() {
    if (!this.data.commentContent.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      })
      return
    }

    try {
      const data = {
        activityId: this.data.activityId,
        content: this.data.commentContent,
        parentId: this.data.replyToId
      }

      const res = await api.addActivityComment(data)

      if (res.code === 200) {
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        })

        // 重新加载评论列表
        this.loadComments(true)

        // 隐藏评论输入框
        this.hideCommentInput()
      } else {
        wx.showToast({
          title: res.msg || '评论失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.showToast({
        title: '评论失败，请重试',
        icon: 'none'
      })
    }
  },

  // 删除评论
  deleteComment(e) {
    const commentId = e.currentTarget.dataset.id

    wx.showModal({
      title: '提示',
      content: '确定要删除这条评论吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const res = await api.deleteActivityComment(commentId)

            if (res.code === 200) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 重新加载评论列表
              this.loadComments(true)
            } else {
              wx.showToast({
                title: res.msg || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 显示报名弹窗
  showSignupPopup() {
    // 检查登录状态
    if (!this.ensureLogin()) return

    // 检查是否已报名
    if (this.data.isSignedUp) {
      wx.showToast({
        title: '您已报名该活动',
        icon: 'none'
      })
      return
    }

    this.setData({
      showSignupPopup: true
    })
  },

  // 隐藏报名弹窗
  hideSignupPopup() {
    this.setData({
      showSignupPopup: false,
      additionalInfo: ''
    })
  },

  // 附加信息变化
  onAdditionalInfoChange(e) {
    this.setData({
      additionalInfo: e.detail
    })
  },

  // 提交报名
  async submitSignup() {
    try {
      const data = {
        activityId: this.data.activityId,
        additionalInfo: this.data.additionalInfo
      }

      const res = await api.signupActivity(data)

      if (res.code === 200) {
        wx.showToast({
          title: '报名成功',
          icon: 'success'
        })

        // 隐藏报名弹窗
        this.hideSignupPopup()

        // 更新报名状态
        this.setData({
          isSignedUp: true
        })

        // 重新加载活动详情
        this.loadActivityDetail()
      } else {
        wx.showToast({
          title: res.msg || '报名失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.showToast({
        title: '报名失败，请重试',
        icon: 'none'
      })
    }
  },

  // 取消报名
  cancelSignup() {
    wx.showModal({
      title: '提示',
      content: '确定要取消报名吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 获取报名ID
            const signupId = this.data.activity.signupId

            if (!signupId) {
              wx.showToast({
                title: '报名信息不存在',
                icon: 'none'
              })
              return
            }

            const res = await api.cancelActivitySignup(signupId)

            if (res.code === 200) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              })

              // 更新报名状态和报名ID
              const updatedActivity = { ...this.data.activity, signupId: null }
              this.setData({
                isSignedUp: false,
                activity: updatedActivity
              })

              // 重新加载活动详情
              this.loadActivityDetail()
            } else {
              wx.showToast({
                title: res.msg || '取消失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.showToast({
              title: '取消失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 审核报名
  auditSignup(e) {
    const signupId = e.currentTarget.dataset.id
    const status = e.currentTarget.dataset.status

    wx.showModal({
      title: '提示',
      content: status === '1' ? '确定通过该报名申请吗？' : '确定拒绝该报名申请吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const res = await api.auditActivitySignup(signupId, status)

            if (res.code === 200) {
              wx.showToast({
                title: '操作成功',
                icon: 'success'
              })

              // 重新加载报名列表
              this.loadSignups(true)
            } else {
              wx.showToast({
                title: res.msg || '操作失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 取消活动
  cancelActivity() {
    wx.showModal({
      title: '提示',
      content: '确定要取消该活动吗？取消后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const res = await api.cancelActivity(this.data.activityId)

            if (res.code === 200) {
              wx.showToast({
                title: '取消成功',
                icon: 'success'
              })

              // 重新加载活动详情
              this.loadActivityDetail()
            } else {
              wx.showToast({
                title: res.msg || '取消失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.showToast({
              title: '取消失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 编辑活动
  editActivity() {
    wx.navigateTo({
      url: `/pages/activity-publish/index?id=${this.data.activityId}`
    })
  },

  // 删除活动
  deleteActivity() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该活动吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const res = await api.deleteActivity(this.data.activityId)

            if (res.code === 200) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: res.msg || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 确保登录状态（用于UI交互前检查）
  ensureLogin(callback) {
    const app = getApp()
    const hasLogin = app.safeGetGlobalData('hasLogin', false)

    if (hasLogin) {
      // 已登录，直接执行回调
      if (typeof callback === 'function') {
        callback()
      }
      return true
    } else {
      // 未登录，显示登录组件
      this.showLogin(true)
      return false
    }
  },

  // 重写混入的登录成功方法，添加页面特定逻辑
  onLoginSuccess() {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginSuccess.call(this)

    // 添加页面特定的处理逻辑
    console.log('活动详情页：登录成功，刷新活动信息')

    // 重新加载活动详情
    this.loadActivityDetail()

    // 重新加载评论列表
    this.loadComments(true)

    // 检查是否是活动发布者，如果是则加载报名列表
    const app = getApp()
    if (app.globalData.userInfo && this.data.activity &&
        app.globalData.userInfo.id === this.data.activity.userId) {
      this.setData({ isOwner: true })
      this.loadSignups(true)
    }
  },

  // 初始化地图数据
  initMapData(location, title, address) {
    const residential = require('../../../services/residential.js');
    
    try {
      // 使用统一的位置解析方法
      const coords = residential.parseLocation(location);
      if (!coords) {
        console.error('位置信息解析失败:', location);
        return;
      }
      
      const { longitude, latitude } = coords;

      // 创建标记点
      const marker = {
        id: 1,
        longitude,
        latitude,
        width: 18,
        height: 22,
        callout: {
          content: `📍 ${title}`,
          color: '#ffffff',
          fontSize: 9,
          borderRadius: 3,
          bgColor: 'rgba(59, 127, 255, 0.85)',
          padding: 3,
          display: 'ALWAYS',
          borderWidth: 1,
          borderColor: 'rgba(59, 127, 255, 0.9)',
          anchorY: 0,
          anchorX: 0
        }
      }

      // 更新地图数据
      this.setData({
        mapInfo: {
          longitude,
          latitude,
          markers: [marker]
        }
      })

      // 创建地图上下文
      this.mapContext = wx.createMapContext('activityMap', this)
    } catch (error) {
      console.error('初始化地图数据失败', error)
    }
  },

  // 打开地图
  openLocation() {
    if (!this.data.activity || !this.data.activity.location) {
      wx.showToast({
        title: '位置信息不存在',
        icon: 'none'
      })
      return
    }

    const residential = require('../../../services/residential.js');
    
    // 解析位置信息
    try {
      const coords = residential.parseLocation(this.data.activity.location);
      if (!coords) {
        wx.showToast({
          title: '位置信息格式错误',
          icon: 'none'
        })
        return;
      }
      
      const { longitude, latitude } = coords;

      wx.openLocation({
        latitude,
        longitude,
        name: this.data.activity.title,
        address: this.data.activity.address
      })
    } catch (error) {
      wx.showToast({
        title: '位置信息格式错误',
        icon: 'none'
      })
    }
  },

  // 联系发布者
  contactOrganizer() {
    // 检查登录状态
    if (!this.ensureLogin()) return

    // 获取活动发布者的联系方式
    this.getContactInfo('organizer')
  },

  // 联系报名者
  contactSignupUser(e) {
    // 检查登录状态
    if (!this.ensureLogin()) return

    const signupId = e.currentTarget.dataset.id
    if (!signupId) {
      wx.showToast({
        title: '报名信息不存在',
        icon: 'none'
      })
      return
    }

    // 获取报名者的联系方式
    this.getContactInfo('signup', signupId)
  },

  // 获取联系方式
  getContactInfo(type, signupId) {
    if (this.data.loadingContact) {
      return
    }

    // 显示加载提示
    wx.showLoading({
      title: '获取联系方式...',
      mask: true
    })

    this.setData({
      loadingContact: true
    })

    let promise
    if (type === 'organizer') {
      // 如果当前用户已报名，则通过报名ID获取联系方式
      if (this.data.isSignedUp && this.data.activity && this.data.activity.signupId) {
        promise = api.getActivitySignupContact(this.data.activity.signupId)
      } else {
        wx.hideLoading()
        this.setData({ loadingContact: false })
        wx.showToast({
          title: '请先报名活动',
          icon: 'none'
        })
        return
      }
    } else if (type === 'signup' && signupId) {
      // 通过报名ID获取报名者联系方式
      promise = api.getActivitySignupContact(signupId)
    } else {
      wx.hideLoading()
      this.setData({ loadingContact: false })
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    promise.then(res => {
      wx.hideLoading()

      if (res.code === 200 && res.data) {
        // 获取目标用户信息
        let targetNickname = ''
        if (type === 'organizer') {
          targetNickname = this.data.activity.nickname
        } else {
          // 查找对应的报名记录获取昵称
          const signup = this.data.signups.find(item => item.id === signupId)
          if (signup) {
            targetNickname = signup.nickname
          }
        }

        // 转换后端返回的联系方式数据格式
        const contacts = res.data.map(item => ({
          type: parseInt(item.contactType), // 转换为数字类型
          value: item.contactValue,
          isVisible: item.isVisible
        }))

        this.setData({
          contacts: contacts,
          targetNickname: targetNickname || '用户',
          loadingContact: false,
          showContactModal: true // 直接显示自定义弹窗
        })
      } else {
        console.error('获取联系方式失败:', res)

        this.setData({
          loadingContact: false
        })

        wx.showToast({
          title: res.msg || '获取联系方式失败',
          icon: 'none',
          duration: 2000
        })
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('获取联系方式异常:', err)

      this.setData({
        loadingContact: false
      })

      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    })
  },

  // 关闭联系方式弹窗
  closeContactModal() {
    this.setData({
      showContactModal: false
    })
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index;

    // 获取当前活动
    const activity = this.data.activity;
    if (activity && activity.imageUrls && activity.imageUrls[index]) {
      const updatedImageUrls = [...activity.imageUrls];
      updatedImageUrls.splice(index, 1);

      // 更新activity对象
      const updatedActivity = {
        ...activity,
        imageUrls: updatedImageUrls,
        images: updatedImageUrls.join(',')
      };

      // 更新数据，触发视图更新
      this.setData({
        activity: updatedActivity
      });
    }
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 地图点击事件处理
  onMapTap(e) {
    // 阻止事件冒泡，防止触发外层容器的事件
    e.stopPropagation();
    // 不执行任何操作，允许地图正常交互
  },

  // 分享活动
  onShareAppMessage() {
    if (!this.data.activity) return {}

    return {
      title: this.data.activity.title,
      path: `/pages/activity/detail/index?id=${this.data.activityId}`,
      imageUrl: this.data.activity.imageUrls && this.data.activity.imageUrls.length > 0 ? this.data.activity.imageUrls[0] : ''
    }
  },

  // 跳转到用户主页
  navigateToUserProfile(e) {
    const userId = e.currentTarget.dataset.userId
    if (!userId) {
      console.error('用户ID不存在')
      return
    }

    // 使用 ensureLogin 检查登录状态
    if (!this.ensureLogin()) {
      return
    }

    // 跳转到用户主页
    wx.navigateTo({
      url: `/pages/user/userProfile/userProfile?userId=${userId}`,
      fail: (err) => {
        console.error('跳转到用户主页失败:', err)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 地图触摸移动事件处理
  onMapTouchMove(e) {
    // 阻止事件冒泡，防止触发外层容器的事件
    // 这个方法主要用于阻止事件传播，不需要特殊处理
    return false
  },

  // 重写混入的登录失败方法，添加页面特定逻辑
  onLoginFail(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginFail.call(this, e)

    // 添加页面特定的处理逻辑
    console.log('活动详情页：登录失败')
  },

  // 重写混入的小区认证确认方法，添加页面特定逻辑
  onConfirmResidentialAuth(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onConfirmResidentialAuth.call(this, e)

    // 添加页面特定的处理逻辑
    console.log('活动详情页：小区认证确认')
  }

}, globalComponents.globalComponentsMixin))
