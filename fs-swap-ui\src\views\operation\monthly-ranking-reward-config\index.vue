<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="奖励类型" prop="rewardType">
        <el-select v-model="queryParams.rewardType" placeholder="请选择奖励类型" clearable>
          <el-option
            v-for="dict in dict.type.ranking_reward_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isActive">
        <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:monthly-ranking-reward-config:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:monthly-ranking-reward-config:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:monthly-ranking-reward-config:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:monthly-ranking-reward-config:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="rewardConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="排名范围" align="center" prop="rankRange" width="120">
        <template slot-scope="scope">
          <el-tag type="info">{{ scope.row.rankStart }}-{{ scope.row.rankEnd }}名</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="奖励类型" align="center" prop="rewardType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ranking_reward_type" :value="scope.row.rewardType"/>
        </template>
      </el-table-column>
      <el-table-column label="奖励数量" align="center" prop="rewardAmount" width="120">
        <template slot-scope="scope">
          <span class="reward-amount">{{ scope.row.rewardAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="奖励名称" align="center" prop="rewardName" />
      <el-table-column label="状态" align="center" prop="isActive" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isActive"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['operation:monthly-ranking-reward-config:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:monthly-ranking-reward-config:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:monthly-ranking-reward-config:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改奖励配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="排名起始位置" prop="rankStart">
          <el-input-number
            v-model="form.rankStart"
            :min="1"
            :max="9999"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入排名起始位置"
          />
        </el-form-item>
        <el-form-item label="排名结束位置" prop="rankEnd">
          <el-input-number
            v-model="form.rankEnd"
            :min="1"
            :max="9999"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入排名结束位置"
          />
        </el-form-item>
        <el-form-item label="奖励类型" prop="rewardType">
          <el-select v-model="form.rewardType" style="width: 100%" placeholder="请选择奖励类型">
            <el-option
              v-for="dict in dict.type.ranking_reward_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励数量" prop="rewardAmount">
          <el-input-number
            v-model="form.rewardAmount"
            :min="1"
            :max="999999"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入奖励数量"
          />
        </el-form-item>
        <el-form-item label="奖励名称" prop="rewardName">
          <el-input v-model="form.rewardName" placeholder="请输入奖励名称" maxlength="100" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-radio-group v-model="form.isActive">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRewardConfig, getRewardConfig, delRewardConfig, addRewardConfig, updateRewardConfig, changeRewardConfigStatus } from '@/api/operation/monthly-ranking-reward-config'

export default {
  name: 'MonthlyRankingRewardConfig',
  dicts: ['ranking_reward_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 奖励配置表格数据
      rewardConfigList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rewardType: null,
        isActive: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        rankStart: [
          { required: true, message: '排名起始位置不能为空', trigger: 'blur' }
        ],
        rankEnd: [
          { required: true, message: '排名结束位置不能为空', trigger: 'blur' }
        ],
        rewardType: [
          { required: true, message: '奖励类型不能为空', trigger: 'change' }
        ],
        rewardAmount: [
          { required: true, message: '奖励数量不能为空', trigger: 'blur' }
        ],
        rewardName: [
          { required: true, message: '奖励名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询奖励配置列表 */
    getList() {
      this.loading = true
      listRewardConfig(this.queryParams).then(response => {
        this.rewardConfigList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        rankStart: null,
        rankEnd: null,
        rewardType: null,
        rewardAmount: null,
        rewardName: null,
        isActive: true
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加奖励配置'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getRewardConfig(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改奖励配置'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 验证排名范围
          if (this.form.rankStart > this.form.rankEnd) {
            this.$modal.msgError('排名起始位置不能大于结束位置')
            return
          }

          if (this.form.id != null) {
            updateRewardConfig(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addRewardConfig(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除奖励配置编号为"' + ids + '"的数据项？').then(function() {
        return delRewardConfig(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/monthly-ranking-reward-config/export', {
        ...this.queryParams
      }, `reward_config_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.isActive ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.rewardName + '"配置吗？').then(function() {
        return changeRewardConfigStatus(row.id, row.isActive)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        row.isActive = row.isActive === false
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.reward-amount {
  font-weight: bold;
  color: #E6A23C;
}
</style>
