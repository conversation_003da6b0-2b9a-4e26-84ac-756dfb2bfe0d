/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  position: relative;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300px;
  overflow: hidden;
  z-index: 1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 127, 255, 0.1) 0%, rgba(139, 69, 255, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
  animation-delay: 0s;
}

.circle2 {
  width: 80px;
  height: 80px;
  top: 100px;
  left: -40px;
  animation-delay: 2s;
}

.circle3 {
  width: 60px;
  height: 60px;
  top: 160px;
  right: 40px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.custom-nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-center {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
}

/* 页面内容 */
.ranking-content {
  padding-top: 100px;
  padding-bottom: 20px;
}

/* 排行榜头部 */
.ranking-header {
  position: relative;
  height: 200px;
  margin: 0 16px 20px;
  border-radius: 16px;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gradient-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.8;
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.trophy-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 排行榜列表 */
.ranking-list {
  background: white;
  margin: 0 16px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 127, 255, 0.05);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1px solid rgba(59, 127, 255, 0.08);
  position: relative;
}

.list-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(59, 127, 255, 0.02) 0%, rgba(139, 69, 255, 0.02) 100%);
  pointer-events: none;
}

.list-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  position: relative;
  z-index: 1;
}

.list-title::before {
  content: '📊';
  margin-right: 6px;
  font-size: 14px;
}

.list-count {
  font-size: 12px;
  color: #888;
  position: relative;
  z-index: 1;
}

/* 表头 */
.table-header {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  background: linear-gradient(135deg, #3B7FFF 0%, #667eea 100%);
  border-bottom: 1px solid rgba(59, 127, 255, 0.2);
  position: relative;
  color: white;
}

.table-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.header-rank {
  width: 15%;
  min-width: 50px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-right: 4px;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.header-user {
  flex: 1;
  min-width: 100px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  margin-left: 8px;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.header-score {
  font-size: 13px;
  font-weight: 600;
  color: white;
  text-align: center;
  width: 25%;
  min-width: 70px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-reward {
  font-size: 13px;
  font-weight: 600;
  color: white;
  text-align: center;
  width: 20%;
  min-width: 60px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 排行榜条目 */
.ranking-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border-bottom: 1px solid rgba(240, 240, 240, 0.6);
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:active {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  transform: scale(0.98);
}

.ranking-item:hover {
  background: rgba(59, 127, 255, 0.02);
}

.item-rank {
  width: 15%;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 4px;
  flex-shrink: 0;
}

.rank-number {
  font-size: 16px;
  font-weight: 600;
  color: #666;
  min-width: 20px;
  text-align: center;
}

.item-user {
  flex: 1;
  min-width: 100px;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.item-user .user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1.5px solid rgba(59, 127, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.ranking-item:hover .item-user .user-avatar {
  border-color: rgba(59, 127, 255, 0.2);
  transform: scale(1.05);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.item-user .user-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-location {
  font-size: 12px;
  color: #999;
}

.item-score {
  display: flex;
  align-items: center;
  flex-direction: row;
  text-align: center;
  width: 25%;
  min-width: 70px;
  flex-shrink: 0;
  justify-content: center;
  gap: 3px;
}

.score-number {
  font-size: 14px;
  font-weight: 600;
  color: #3B7FFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
}

.score-unit {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
}

.item-reward {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 20%;
  min-width: 60px;
  flex-shrink: 0;
}

.reward-text {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 加载状态 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.load-text {
  margin-left: 8px;
  font-size: 14px;
}

.no-more {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #ccc;
}

.empty-text {
  margin-top: 12px;
  font-size: 14px;
}

/* 我的排名卡片 */
.my-ranking-card {
  background: white;
  margin: 16px 16px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(59, 127, 255, 0.1);
  position: relative;
}

.my-ranking-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 127, 255, 0.02) 0%, rgba(139, 69, 255, 0.02) 100%);
  pointer-events: none;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  background: linear-gradient(135deg, #3B7FFF 0%, #667eea 50%, #8B45FF 100%);
  color: white;
  position: relative;
  z-index: 1;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.card-title {
  margin-left: 6px;
  font-size: 15px;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.my-ranking-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  position: relative;
  z-index: 1;
}

.my-rank-info {
  flex: 1;
}

.my-rank-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.rank-text {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.rank-num {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #3B7FFF 0%, #8B45FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 4px;
}

.my-rank-detail {
  display: flex;
  align-items: baseline;
  background: rgba(59, 127, 255, 0.05);
  padding: 5px 8px;
  width: 80%;
  border-radius: 8px;
  border: 1px solid rgba(59, 127, 255, 0.1);
}

.detail-text {
  font-size: 11px;
  color: #666;
  margin-right: 5px;
  font-weight: 500;
}

.detail-score {
  font-size: 14px;
  font-weight: 600;
  color: #3B7FFF;
}

.my-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.my-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-bottom: 6px;
  border: 2px solid #3B7FFF;
  box-shadow: 0 3px 8px rgba(59, 127, 255, 0.2);
  transition: all 0.3s ease;
}

.my-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 127, 255, 0.3);
}

.my-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.rank-tip {
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-top: 1px solid rgba(59, 127, 255, 0.08);
  text-align: center;
  position: relative;
  z-index: 1;
}

.tip-text {
  font-size: 12px;
  font-weight: 500;
}

.tip-text.up {
  color: #52c41a;
}

.tip-text.down {
  color: #ff4d4f;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

/* 响应式布局 - 小屏幕优化 */
@media (max-width: 375px) {
  .table-header, .ranking-item {
    padding: 12px 12px;
  }

  .header-rank, .item-rank {
    width: 12%;
    min-width: 40px;
  }

  .header-user, .item-user {
    margin-left: 6px;
  }

  .header-score, .item-score {
    width: 28%;
    min-width: 65px;
  }

  .header-reward, .item-reward {
    width: 22%;
    min-width: 55px;
  }

  .item-user .user-avatar {
    width: 20px;
    height: 20px;
    margin-right: 6px;
  }

  .item-user .user-name {
    font-size: 12px;
  }

  .score-number {
    font-size: 13px;
  }

  .reward-text {
    font-size: 11px;
  }

  .rank-number {
    font-size: 14px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
  .table-header, .ranking-item {
    padding: 10px 8px;
  }

  .header-rank, .item-rank {
    width: 10%;
    min-width: 35px;
  }

  .header-user, .item-user {
    margin-left: 4px;
  }

  .header-score, .item-score {
    width: 30%;
    min-width: 60px;
  }

  .header-reward, .item-reward {
    width: 25%;
    min-width: 50px;
  }

  .item-user .user-avatar {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }

  .item-user .user-name {
    font-size: 11px;
  }

  .score-number {
    font-size: 12px;
  }

  .score-unit {
    font-size: 10px;
  }

  .reward-text {
    font-size: 10px;
  }

  .rank-number {
    font-size: 13px;
  }

  .header-rank, .header-user, .header-score, .header-reward {
    font-size: 12px;
  }
}

/* 页面标题 */
.page-header {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px 16px 16px;
  margin-bottom: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.9) 100%);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 127, 255, 0.15);
  box-shadow: 0 8px 32px rgba(59, 127, 255, 0.12);
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 127, 255, 0.02) 0%, rgba(139, 69, 255, 0.02) 100%);
  pointer-events: none;
}

.header-content {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 22px;
  font-weight: 700;
  background: linear-gradient(135deg, #3B7FFF 0%, #667eea 50%, #8B45FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  display: block;
  position: relative;
  z-index: 1;
  letter-spacing: 0.5px;
}

.page-subtitle {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  opacity: 0.85;
  position: relative;
  z-index: 1;
  font-weight: 400;
}

.rules-button {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #3B7FFF;
  border-radius: 16px;
  border: 1px solid rgba(59, 127, 255, 0.3);
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  z-index: 2;
  transition: all 0.3s ease;
}

.rules-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 127, 255, 0.5);
}

.rules-text {
  margin-left: 4px;
  color: #3B7FFF;
}

/* 榜单切换选项卡 */
.ranking-tabs {
  display: flex;
  margin: 0 16px 12px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 4px;
  box-shadow: 0 4px 16px rgba(59, 127, 255, 0.12);
  border: 1px solid rgba(59, 127, 255, 0.15);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.ranking-tabs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 127, 255, 0.03) 0%, rgba(139, 69, 255, 0.02) 100%);
  pointer-events: none;
}

.tab-item {
  flex: 1;
  height: 38px;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  z-index: 2;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 10px;
  background: transparent;
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3B7FFF 0%, #667eea 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
  z-index: 1;
}

.tab-item.active::before {
  opacity: 1;
}

.tab-item:active {
  transform: scale(0.98);
}

.tab-item .van-icon {
  position: relative;
  z-index: 3;
}

.tab-text {
  font-size: 13px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  z-index: 3;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 上期榜单提示信息 */
.previous-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  margin: 0 16px 12px;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1) 0%, rgba(255, 193, 7, 0.08) 100%);
  border-radius: 8px;
  border: 1px solid rgba(255, 149, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.previous-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 149, 0, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

.previous-tip .tip-text {
  font-size: 12px;
  color: #FF9500;
  margin-left: 5px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}