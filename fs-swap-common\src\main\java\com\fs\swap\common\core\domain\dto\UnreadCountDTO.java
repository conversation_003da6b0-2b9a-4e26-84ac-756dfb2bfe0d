package com.fs.swap.common.core.domain.dto;

/**
 * 未读消息数量统计DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
public class UnreadCountDTO
{
    /** 消息类型 */
    private String type;

    /** 未读数量 */
    private Integer count;

    public UnreadCountDTO()
    {
    }

    public UnreadCountDTO(String type, Integer count)
    {
        this.type = type;
        this.count = count;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public Integer getCount()
    {
        return count;
    }

    public void setCount(Integer count)
    {
        this.count = count;
    }

    @Override
    public String toString()
    {
        return "UnreadCountDTO{" +
                "type='" + type + '\'' +
                ", count=" + count +
                '}';
    }
} 