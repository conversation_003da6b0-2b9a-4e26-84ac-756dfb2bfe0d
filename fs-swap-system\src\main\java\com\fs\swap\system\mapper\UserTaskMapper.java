package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.UserTask;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 用户任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface UserTaskMapper {
    /**
     * 查询用户任务
     * 
     * @param id 用户任务主键
     * @return 用户任务
     */
    public UserTask selectUserTaskById(Long id);

    /**
     * 查询用户任务列表
     * 
     * @param userTask 用户任务
     * @return 用户任务集合
     */
    public List<UserTask> selectUserTaskList(UserTask userTask);

    /**
     * 新增用户任务
     * 
     * @param userTask 用户任务
     * @return 结果
     */
    public int insertUserTask(UserTask userTask);

    /**
     * 修改用户任务
     * 
     * @param userTask 用户任务
     * @return 结果
     */
    public int updateUserTask(UserTask userTask);

    /**
     * 删除用户任务
     * 
     * @param id 用户任务主键
     * @return 结果
     */
    public int deleteUserTaskById(Long id);

    /**
     * 批量删除用户任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserTaskByIds(Long[] ids);

    /**
     * 根据用户ID和任务编码查询用户任务
     * 
     * @param userId 用户ID
     * @param taskCode 任务编码
     * @param taskDate 任务日期(每日任务用)
     * @return 用户任务
     */
    public UserTask selectUserTaskByUserAndCode(@Param("userId") Long userId, 
                                              @Param("taskCode") String taskCode, 
                                              @Param("taskDate") Date taskDate);

    /**
     * 查询用户的任务列表
     * 
     * @param userId 用户ID
     * @return 用户任务集合
     */
    public List<UserTask> selectUserTasksByUserId(Long userId);

    /**
     * 查询用户待领取的任务列表
     * 
     * @param userId 用户ID
     * @return 用户任务集合
     */
    public List<UserTask> selectUserTasksToClaimByUserId(Long userId);
} 