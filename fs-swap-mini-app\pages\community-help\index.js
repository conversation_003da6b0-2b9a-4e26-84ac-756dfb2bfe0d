const api = require('../../config/api.js')
const { ensureLogin } = require('../../utils/user.js')
const userUtils = require('../../utils/user.js')
const systemInfoService = require('../../services/systemInfo')
const { formatImageUrls } = require('../../utils/util.js')
const dateUtil = require('../../utils/dateUtil')

Page({
  data: {
    // 筛选相关
    publishTypeFilter: 'all', // 发布类型筛选：'all'-全部, '1'-需求信息, '2'-服务信息
    helpFilter: 'all', // 互助筛选：all, 或具体分类ID
    searchValue: '', // 搜索关键词

    // 排序相关
    sortType: 'createTime', // 排序类型：createTime, endTime
    sortOrder: 'desc', // 排序顺序：desc, asc

    // 分类数据（从后端字典接口获取）
    categories: [],
    categoryColumns: [], // 分类选择器数据
    selectedCategoryName: '分类', // 当前选中的分类名称
    showCategoryPicker: false, // 是否显示分类选择器

    // 发布类型数据
    publishTypes: [], // 发布类型数据（从systemInfo获取）
    publishTypeColumns: [], // 发布类型选择器数据
    selectedPublishTypeName: '全部', // 当前选中的发布类型名称
    showPublishTypePicker: false, // 是否显示发布类型选择器

    // 列表数据
    helpList: [], // 原始互助列表
    filteredList: [], // 筛选后的互助列表

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,

    // 页面状态
    isFirstLoad: true,

    // 滚动相关
    scrollTop: 0,
    filterFixed: false,
    filterOffsetTop: 0,

    // 系统配置
    helpSilver: '5', // 互助服务费用，默认值

    // 发布弹框
    showPublishDialog: false,
    publishStep: 1, // 发布步骤：1-选择类型，2-选择类别
    selectedPublishType: '', // 选中的发布类型
    availableCategories: [], // 可用的分类列表（排除"全部"选项）

    // 搜索防抖
    searchTimer: null
  },

  onLoad(options) {
    console.log('邻里互助页面加载', options)
    
    // 如果有分类参数，设置默认选中
    if (options.categoryId) {
      this.setData({
        helpFilter: options.categoryId
      })
    }

    // 加载系统配置
    this.loadSystemConfig()

    this.initPage()
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (!this.data.isFirstLoad) {
      this.refreshData()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    this.loadMoreData()
  },

  onPageScroll(e) {
    this.handleScroll(e.scrollTop)
  },

  onUnload() {
    // 清理搜索定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }
  },

  /**
   * 加载系统配置
   */
  async loadSystemConfig() {
    try {
      const helpSilver = await systemInfoService.getHelpSilver()
      this.setData({ helpSilver })
    } catch (error) {
      console.error('获取互助服务费用失败:', error)
      // 使用默认值
      this.setData({ helpSilver: '5' })
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 加载分类列表
      await this.loadCategories()

      // 加载发布类型列表
      await this.loadPublishTypes()

      // 加载互助列表
      await this.loadHelpList(true)

      this.setData({ isFirstLoad: false })

      // 获取筛选区域的位置信息
      this.getFilterPosition()
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 获取筛选区域位置
   */
  getFilterPosition() {
    wx.nextTick(() => {
      const query = wx.createSelectorQuery().in(this)
      query.select('.filter-header').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            filterOffsetTop: rect.top
          })
        }
      }).exec()
    })
  },

  /**
   * 处理页面滚动
   */
  handleScroll(scrollTop) {
    this.setData({ scrollTop })

    // 判断是否需要固定筛选区域
    const shouldFixed = scrollTop >= this.data.filterOffsetTop

    if (shouldFixed !== this.data.filterFixed) {
      this.setData({ filterFixed: shouldFixed })
    }
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      // 获取需求分类和服务分类数据
      const requireCategories = await systemInfoService.getCommunityHelpRequireCategories()
      const serviceCategories = await systemInfoService.getCommunityHelpServiceCategories()

      // 合并两个分类集合的所有数据用于筛选显示
      const allCategoryData = [
        ...(requireCategories || []),
        ...(serviceCategories || [])
      ]

      // 去重处理（基于dictValue）
      const uniqueCategories = []
      const seenValues = new Set()
      allCategoryData.forEach(item => {
        if (!seenValues.has(item.dictValue)) {
          seenValues.add(item.dictValue)
          uniqueCategories.push(item)
        }
      })

      // 添加"全部"选项到分类列表开头
      const allCategories = [
        { id: 'all', name: '全部' },
        ...uniqueCategories.map(item => ({
          id: item.dictValue,
          name: item.dictLabel
        }))
      ]

      // 为选择器准备数据格式
      const categoryColumns = allCategories.map(item => ({
        id: item.id,
        name: item.name
      }))

      this.setData({
        categories: allCategories,
        categoryColumns: categoryColumns,
        requireCategories: requireCategories || [],
        serviceCategories: serviceCategories || []
      })

      // 更新当前选中分类的显示名称
      this.updateSelectedCategoryName()

      // 如果已有互助列表数据，更新分类名称映射
      if (this.data.helpList.length > 0) {
        this.updateCategoryNames()
      }
    } catch (error) {
      console.error('加载分类失败:', error)

      // 设置默认的分类数据，确保页面能正常工作
      const defaultCategories = [{ id: 'all', name: '全部' }]

      this.setData({
        categories: defaultCategories,
        categoryColumns: defaultCategories,
        requireCategories: [],
        serviceCategories: []
      })

      // 显示错误提示，但不影响页面功能
      wx.showToast({
        title: '分类加载失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 加载发布类型列表
   */
  async loadPublishTypes() {
    try {
      // 使用统一的系统信息服务获取发布类型数据
      const publishTypes = await systemInfoService.getCommunityHelpPublishTypes()

      // 添加"全部"选项到发布类型列表开头
      const allPublishTypes = [
        { id: 'all', name: '全部' },
        ...(publishTypes || []).map(item => ({
          id: item.dictValue,
          name: item.dictLabel
        }))
      ]

      // 为选择器准备数据格式
      const publishTypeColumns = allPublishTypes.map(item => ({
        id: item.id,
        name: item.name
      }))

      this.setData({
        publishTypes: allPublishTypes,
        publishTypeColumns: publishTypeColumns
      })

      // 如果已有互助列表数据，更新发布类型名称映射
      if (this.data.helpList.length > 0) {
        this.updateCategoryNames()
      }
    } catch (error) {
      console.error('加载发布类型失败:', error)

      // 设置默认的发布类型数据，确保页面能正常工作
      const defaultPublishTypes = [{ id: 'all', name: '全部' }]

      this.setData({
        publishTypes: defaultPublishTypes,
        publishTypeColumns: defaultPublishTypes
      })

      // 显示错误提示，但不影响页面功能
      wx.showToast({
        title: '发布类型加载失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 更新分类名称映射
   */
  updateCategoryNames() {
    const updatedHelpList = this.data.helpList.map(item => {
      const category = this.data.categories.find(cat => cat.id === item.category)
      const categoryName = category ? category.name : (item.category || '其他')

      const publishType = this.data.publishTypes.find(type => type.id === item.publishType)
      const publishTypeName = publishType ? publishType.name : (item.publishType || '未知类型')

      // 如果还没有处理过图片列表，这里也处理一下
      if (!item.imageList && item.images) {
        const imageList = formatImageUrls(item.images, null, { returnArray: true })
        const firstImage = formatImageUrls(item.images, null, { firstImageOnly: true })

        return {
          ...item,
          categoryName: categoryName,
          publishTypeName: publishTypeName,
          imageList: imageList,
          firstImage: firstImage
        }
      }

      return {
        ...item,
        categoryName: categoryName,
        publishTypeName: publishTypeName
      }
    })

    this.setData({
      helpList: updatedHelpList
    })

    // 重新应用筛选条件
    this.applyFilters()
  },

  /**
   * 加载互助列表
   */
  async loadHelpList(reset = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 构建排序参数，参考首页商品列表的实现
      let orderByColumn = 'create_time'; // 默认按创建时间排序（使用数据库字段名）
      let isAsc = 'desc'; // 默认降序

      // 根据sortType设置排序参数
      if (this.data.sortType === 'createTime') {
        orderByColumn = 'create_time';
        isAsc = this.data.sortOrder === 'asc' ? 'asc' : 'desc';
      } else if (this.data.sortType === 'endTime') {
        orderByColumn = 'end_time';
        isAsc = this.data.sortOrder === 'asc' ? 'asc' : 'desc';
      }

      const params = {
        pageNum: reset ? 1 : this.data.pageNum,
        pageSize: this.data.pageSize,
        orderByColumn: orderByColumn, // 使用后端期望的参数格式
        isAsc: isAsc
      }

      // 添加发布类型参数（如果不是"全部"）
      if (this.data.publishTypeFilter !== 'all') {
        params.publishType = this.data.publishTypeFilter
      }

      // 添加分类筛选参数（如果不是"全部"）
      if (this.data.helpFilter && this.data.helpFilter !== 'all') {
        params.category = this.data.helpFilter
      }

      // 添加搜索参数（如果有搜索关键词）
      if (this.data.searchValue && this.data.searchValue.trim()) {
        params.searchValue = this.data.searchValue.trim()
      }

      const res = await api.getCommunityHelpList(params)

      const newList = res.rows || []

      // 处理图片显示、分类名称映射和用户头像
      const processedList = await Promise.all(newList.map(async item => {
        // 使用工具函数处理图片数据（逗号分隔格式）
        const imageList = formatImageUrls(item.images, null, { returnArray: true })
        const firstImage = formatImageUrls(item.images, null, { firstImageOnly: true })

        // 获取分类名称
        const category = this.data.categories.find(cat => cat.id === item.category)
        const categoryName = category ? category.name : (item.category || '其他')

        // 确保publishType为字符串类型
        const publishType = typeof item.publishType === 'number' ? item.publishType.toString() : item.publishType

        // 处理截止时间显示，只显示到天
        const endTime = dateUtil.formatToDateOnly(item.endTime)

        // 处理发布时间显示，只显示到天
        const createTime = dateUtil.formatToDateOnly(item.createTime)

        // 使用系统信息服务处理用户头像
        let avatar = '/static/img/default_avatar.png'
        if (item.avatar) {
          avatar = await systemInfoService.processImageUrl(item.avatar)
        }

        // 确保有用户名
        const nickname = item.nickname || '趣换用户'

        return {
          ...item,
          imageList: imageList,
          firstImage: firstImage,
          categoryName: categoryName,
          publishType: publishType,
          endTime: endTime,
          createTime: createTime,
          avatar: avatar,
          nickname: nickname
        }
      }))

      const helpList = reset ? processedList : [...this.data.helpList, ...processedList]

      this.setData({
        helpList,
        filteredList: helpList, // 直接设置筛选后的列表，因为后端已经返回筛选和排序后的数据
        pageNum: reset ? 2 : this.data.pageNum + 1,
        hasMore: processedList.length >= this.data.pageSize,
        loading: false
      })

    } catch (error) {
      console.error('加载互助列表失败:', error)
      this.setData({ loading: false })

      if (!reset) {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    await this.loadHelpList(true)
    wx.stopPullDownRefresh()
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadHelpList()
    }
  },

  // 应用筛选条件（注意：排序和主要筛选已由后端处理，这里只处理本地数据的显示逻辑）
  applyFilters: function () {
    // 由于后端已经处理了筛选和排序，这里直接使用helpList
    // 只在需要本地筛选时使用（比如搜索功能的实时筛选）
    let filteredList = [...this.data.helpList]

    this.setData({
      filteredList: filteredList,
    })

    // 更新分类状态
    this.updateCategoriesStatus()
  },



  // 更新分类状态
  updateCategoriesStatus: function () {
    const { filteredList, categories } = this.data

    // 动态检查每个分类是否有数据
    const categoryStatus = {}

    // 遍历所有分类，检查是否有对应的互助数据
    categories.forEach((category) => {
      if (category.id !== 'all') {
        categoryStatus[
          `has${
            category.id.charAt(0).toUpperCase() +
            category.id.slice(1)
          }`
        ] = this.hasCategoryData(category.id)
      }
    })

    this.setData(categoryStatus)
  },

  // 检查分类是否有数据
  hasCategoryData: function (categoryId) {
    const { filteredList } = this.data
    return filteredList.some((item) => item.category === categoryId)
  },

  // 分类选择
  onCategorySelect: function () {
    this.setData({
      showCategoryPicker: true
    })
  },

  // 分类选择确认
  onCategoryConfirm: function (event) {
    const { value, index } = event.detail
    const selectedCategory = this.data.categoryColumns[index]

    this.setData({
      helpFilter: selectedCategory.id,
      selectedCategoryName: selectedCategory.name,
      showCategoryPicker: false,
      // 重置分页参数
      pageNum: 1,
      hasMore: true,
      helpList: [],
      filteredList: []
    })

    // 重新加载数据
    this.loadHelpList(true)
  },

  // 关闭分类选择器
  onCategoryPickerClose: function () {
    this.setData({
      showCategoryPicker: false
    })
  },

  // 更新选中分类的显示名称
  updateSelectedCategoryName: function () {
    const currentFilter = this.data.helpFilter
    const category = this.data.categories.find(cat => cat.id === currentFilter)
    let categoryName = category ? category.name : '全部'

    // 如果是"全部"，显示为"分类"
    if (categoryName === '全部') {
      categoryName = '分类'
    }

    this.setData({
      selectedCategoryName: categoryName
    })
  },

  // 搜索变化
  onSearchChange: function (event) {
    const searchValue = event.detail

    this.setData({
      searchValue: searchValue
    })

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    // 设置新的定时器，500ms后执行搜索
    const timer = setTimeout(() => {
      this.setData({
        // 重置分页参数
        pageNum: 1,
        hasMore: true,
        helpList: [],
        filteredList: []
      })
      // 重新加载数据
      this.loadHelpList(true)
    }, 500)

    this.setData({
      searchTimer: timer
    })
  },

  // 发布类型选择
  onPublishTypeSelect: function () {
    this.setData({
      showPublishTypePicker: true
    })
  },

  // 发布类型选择器关闭
  onPublishTypePickerClose: function () {
    this.setData({
      showPublishTypePicker: false
    })
  },

  // 发布类型选择确认
  onPublishTypeConfirm: function (event) {
    const selectedItem = event.detail.value
    const publishType = selectedItem.id
    const publishTypeName = selectedItem.name

    // 如果选择的是当前已选中的类型，不做任何操作
    if (this.data.publishTypeFilter === publishType) {
      this.setData({
        showPublishTypePicker: false
      })
      return
    }

    this.setData({
      publishTypeFilter: publishType,
      selectedPublishTypeName: publishTypeName,
      showPublishTypePicker: false,
      // 切换类型时重置分类筛选和搜索
      helpFilter: 'all',
      selectedCategoryName: '分类',
      searchValue: '',
      // 重置分页参数
      pageNum: 1,
      hasMore: true,
      helpList: [],
      filteredList: []
    })

    // 重新加载数据
    this.loadHelpList(true)
  },

  // 排序变化
  onSortChange: function (event) {
    const sortType = event.currentTarget.dataset.sort
    let sortOrder = 'desc'

    // 如果点击的是当前排序字段，切换排序顺序
    if (this.data.sortType === sortType) {
      sortOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    }

    this.setData({
      sortType: sortType,
      sortOrder: sortOrder,
      // 重置分页参数
      pageNum: 1,
      hasMore: true,
      helpList: [],
      filteredList: []
    })

    // 重新加载数据
    this.loadHelpList(true)
  },

  /**
   * 预览图片
   */
  onPreviewImage(event) {
    const current = event.currentTarget.dataset.current
    const index = event.currentTarget.dataset.index

    if (current && current.length > 0) {
      wx.previewImage({
        current: current[index],
        urls: current
      })
    }
  },

  /**
   * 查看互助详情
   */
  onHelpDetail(event) {
    const id = event.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/community-help-detail/index?id=${id}`
    })
  },

  /**
   * 点击用户头像跳转到用户主页
   */
  onUserAvatarTap(event) {
    const userId = event.currentTarget.dataset.userId
    if (userId) {
      wx.navigateTo({
        url: `/pages/user/userProfile/userProfile?userId=${userId}`,
        fail: (err) => {
          console.error('跳转到用户主页失败:', err)
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
    }
  },

  /**
   * 联系发布者
   */
  onContact(event) {
    const id = event.currentTarget.dataset.id

    ensureLogin(this, () => {
      this.contactPublisher(id)
    })
  },

  /**
   * 联系发布者
   */
  async contactPublisher(id) {
    try {
      wx.showLoading({ title: '处理中...' })

      const result = await api.contactCommunityHelp(id)

      wx.hideLoading()

      wx.showModal({
        title: '联系成功',
        content: `已扣除${this.data.helpSilver}碳豆，您可以在订单中查看联系方式`,
        showCancel: false,
        success: () => {
          // 跳转到订单详情
          wx.navigateTo({
            url: `/pages/order/detail?id=${result.orderId}`
          })
        }
      })
    } catch (error) {
      wx.hideLoading()
      console.error('联系失败:', error)
      wx.showToast({
        title: '联系失败',
        icon: 'none'
      })
    }
  },

  /**
   * 显示发布选择弹框
   */
  onShowPublishDialog() {
    this.setData({
      showPublishDialog: true,
      publishStep: 1,
      selectedPublishType: ''
    })
  },

  /**
   * 关闭发布选择弹框
   */
  onClosePublishDialog() {
    this.setData({
      showPublishDialog: false,
      publishStep: 1,
      selectedPublishType: ''
    })
  },

  /**
   * 选择发布需求
   */
  onPublishRequest() {
    this.setData({
      selectedPublishType: '1',
      publishStep: 2
    })
  },

  /**
   * 选择提供服务
   */
  onPublishOffer() {
    this.setData({
      selectedPublishType: '2',
      publishStep: 2
    })
  },

  /**
   * 根据发布类型获取对应的分类列表
   */
  getPublishCategories(publishType) {
    if (publishType === '1') {
      // 发布需求时使用需求分类
      return this.data.requireCategories || []
    } else if (publishType === '2') {
      // 提供服务时使用服务分类
      return this.data.serviceCategories || []
    }
    return []
  },

  /**
   * 返回发布类型选择
   */
  onBackToPublishType() {
    this.setData({
      publishStep: 1,
      selectedPublishType: ''
    })
  },

  /**
   * 选择分类并跳转到发布页面
   */
  onSelectCategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId

    // 在关闭弹框前保存当前选中的发布类型
    const currentPublishType = this.data.selectedPublishType

    // 关闭弹框
    this.setData({
      showPublishDialog: false,
      publishStep: 1,
      selectedPublishType: ''
    })

    // 检查小区认证状态
    userUtils.ensureResidentialAuth(this, () => {
      // 小区认证通过，跳转到发布页面，使用保存的发布类型
      wx.navigateTo({
        url: `/pages/community-help-publish/index?publishType=${currentPublishType}&category=${categoryId}`
      })
    })
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    return {
      title: '邻里互助 - 邻里之间，互帮互助',
      path: '/pages/community-help/index'
    }
  }
})
