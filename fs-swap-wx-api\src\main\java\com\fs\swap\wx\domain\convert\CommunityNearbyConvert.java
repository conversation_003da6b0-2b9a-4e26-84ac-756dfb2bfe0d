package com.fs.swap.wx.domain.convert;

import com.fs.swap.common.core.domain.entity.CommunityNearby;
import com.fs.swap.wx.domain.vo.CommunityNearbyVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 社区服务-周边推荐对象转换器
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@Mapper
public interface CommunityNearbyConvert {
    
    CommunityNearbyConvert INSTANCE = Mappers.getMapper(CommunityNearbyConvert.class);
    
    /**
     * 实体转VO
     */
    @Mapping(target = "imageUrls", ignore = true)
    @Mapping(target = "latitude", ignore = true)
    @Mapping(target = "longitude", ignore = true)
    @Mapping(target = "distanceText", ignore = true)
    CommunityNearbyVO toVO(CommunityNearby entity);
    
    /**
     * 实体列表转VO列表
     */
    List<CommunityNearbyVO> toVOList(List<CommunityNearby> entityList);
}
