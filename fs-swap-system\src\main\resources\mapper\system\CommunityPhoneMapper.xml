<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.CommunityPhoneMapper">
    
    <resultMap type="CommunityPhone" id="CommunityPhoneResult">
        <id     property="id"             column="id"              />
        <result property="name"           column="name"            />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="category"       column="category"        />
        <result property="description"    column="description"     />
        <result property="communityId"    column="community_id"    />
        <result property="residentialId"  column="residential_id"  />
        <result property="submitUserId"   column="submit_user_id"  />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditUserId"    column="audit_user_id"   />
        <result property="auditTime"      column="audit_time"      />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="sort"           column="sort"            />
        <result property="status"         column="status"          />
        <result property="callCount"      column="call_count"      />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="deleted"        column="deleted"         />
    </resultMap>

    <sql id="selectCommunityPhoneVo">
        select id, name, phone_number, category, description, community_id, residential_id, submit_user_id, 
        audit_status, audit_user_id, audit_time, audit_remark, sort, status, call_count, create_by, create_time, 
        update_by, update_time, deleted
        from community_phone
    </sql>

    <select id="selectCommunityPhoneList" parameterType="CommunityPhone" resultMap="CommunityPhoneResult">
        <include refid="selectCommunityPhoneVo"/>
        <where>
            deleted = 0
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number like concat('%', #{phoneNumber}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="description != null and description != ''">
                AND description like concat('%', #{description}, '%')
            </if>
            <if test="communityId != null">
                AND community_id = #{communityId}
            </if>
            <if test="residentialId != null">
                AND residential_id = #{residentialId}
            </if>
            <if test="submitUserId != null">
                AND submit_user_id = #{submitUserId}
            </if>
            <if test="auditStatus != null">
                AND audit_status = #{auditStatus}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by sort asc, create_time desc
    </select>
    
    <select id="selectCommunityPhoneById" parameterType="Long" resultMap="CommunityPhoneResult">
        <include refid="selectCommunityPhoneVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectCommunityPhoneListByLocation" resultMap="CommunityPhoneResult">
        <include refid="selectCommunityPhoneVo"/>
        <where>
            deleted = 0
            AND status = 1
            AND audit_status = 1
            <if test="communityId != null and residentialId != null">
                AND (
                (community_id = #{communityId} AND residential_id IS NULL)
                OR
                (residential_id = #{residentialId})
                )
            </if>
        </where>
        order by call_count desc ,sort asc, create_time desc
    </select>
    
    <select id="selectCommunityPhoneListBySubmitUserId" parameterType="Long" resultMap="CommunityPhoneResult">
        <include refid="selectCommunityPhoneVo"/>
        where submit_user_id = #{submitUserId} and deleted = 0
        order by create_time desc
    </select>
        
    <insert id="insertCommunityPhone" parameterType="CommunityPhone" useGeneratedKeys="true" keyProperty="id">
        insert into community_phone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="category != null">category,</if>
            <if test="description != null">description,</if>
            <if test="communityId != null">community_id,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="submitUserId != null">submit_user_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="callCount != null">call_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="category != null">#{category},</if>
            <if test="description != null">#{description},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="submitUserId != null">#{submitUserId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="callCount != null">#{callCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateCommunityPhone" parameterType="CommunityPhone">
        update community_phone
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="category != null">category = #{category},</if>
            <if test="description != null">description = #{description},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="submitUserId != null">submit_user_id = #{submitUserId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="callCount != null">call_count = #{callCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCommunityPhoneById" parameterType="Long">
        update community_phone set deleted = 1 where id = #{id}
    </update>

    <update id="deleteCommunityPhoneByIds" parameterType="String">
        update community_phone set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 审核电话 -->
    <update id="auditCommunityPhone">
        update community_phone
        set audit_status = #{auditStatus},
            audit_user_id = #{auditUserId},
            audit_time = #{auditTime},
            audit_remark = #{auditRemark},
            update_time = sysdate()
        where id = #{id} and deleted = 0
    </update>
    
    <!-- 更新拨打次数 -->
    <update id="incrementCallCount">
        update community_phone
        set call_count = COALESCE(call_count, 0) + 1,
            update_time = sysdate()
        where id = #{id} and deleted = 0
    </update>
    
    <!-- 根据分类统计电话数量 -->
    <select id="getPhoneStatsByCategory" resultMap="CommunityPhoneResult">
        select category, count(*) as phone_count
        from community_phone
        <where>
            deleted = 0
            AND status = 1
            AND audit_status = 1
            <if test="regionId != null">
                AND region_id = #{regionId}
            </if>
            <if test="communityId != null">
                AND community_id = #{communityId}
            </if>
            <if test="residentialId != null">
                AND residential_id = #{residentialId}
            </if>
        </where>
        group by category
        order by phone_count desc
    </select>
</mapper>
