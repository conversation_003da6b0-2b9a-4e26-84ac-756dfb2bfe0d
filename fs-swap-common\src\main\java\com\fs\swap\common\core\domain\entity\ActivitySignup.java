package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 活动报名对象 activity_signup
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class ActivitySignup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID */
    private Long id;

    /** 活动ID */
    @Excel(name = "活动ID")
    private Long activityId;

    /** 报名用户ID */
    @Excel(name = "报名用户ID")
    private Long userId;

    /** 状态（0-待审核 1-已通过 2-已拒绝 3-已取消） */
    @Excel(name = "状态", readConverterExp = "0=-待审核,1=-已通过,2=-已拒绝,3=-已取消")
    private String status;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signupTime;


    /** 附加信息 */
    @Excel(name = "附加信息")
    private String additionalInfo;

    // 非数据库字段
    /** 用户昵称 */
    private String nickname;
    /** 用户头像 */
    private String avatar;
    /** 活动标题 */
    private String activityTitle;
    /** 活动图片 */
    private String activityImages;
}
