package com.fs.swap.framework.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fs.swap.common.utils.ip.IpUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * 请求日志切面
 * 统一记录controller请求日志
 */
@Aspect
@Component
public class RequestLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(RequestLogAspect.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private RequestLogConfig logConfig;
    
    /**
     * 定义切点：匹配controller包下的所有方法
     */
    @Pointcut("execution(* com.fs.swap..controller..*.*(..))")
    public void controllerMethods() {}
    
    /**
     * 环绕通知：记录请求和响应信息
     */
    @Around("controllerMethods()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 如果日志记录被禁用，直接执行原方法
        if (!logConfig.isEnabled()) {
            return joinPoint.proceed();
        }
        
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 记录请求开始日志
        try {
            StringBuilder requestInfo = new StringBuilder();
            requestInfo.append("==> 请求开始 [").append(className).append(".").append(methodName).append("]");
            
            if (request != null) {
                requestInfo.append(" ").append(request.getMethod()).append(" ").append(request.getRequestURI());
                
                if (logConfig.isLogIp()) {
                    requestInfo.append(" | IP: ").append(getClientIpAddress(request));
                }
            }
            
            if (logConfig.isLogParams()) {
                requestInfo.append(" | 参数: ").append(formatArgs(args));
            }
            
            logger.info(requestInfo.toString());
        } catch (Exception e) {
            logger.warn("记录请求日志时发生异常: {}", e.getMessage());
        }
        
        Object result = null;
        Exception exception = null;
        
        try {
            // 执行原方法
            result = joinPoint.proceed();
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 记录请求结束日志
            try {
                if (exception != null) {
                    logger.error("<== 请求异常 [{}.{}] | 耗时: {}ms | 异常: {}", 
                        className, methodName, duration, exception.getMessage());
                } else {
                    StringBuilder responseInfo = new StringBuilder();
                    responseInfo.append("<== 请求完成 [").append(className).append(".").append(methodName).append("]");
                    
                    if (logConfig.isLogExecutionTime()) {
                        responseInfo.append(" | 耗时: ").append(duration).append("ms");
                        
                        // 标记慢请求
                        if (duration > logConfig.getSlowRequestThreshold()) {
                            responseInfo.append(" [慢请求]");
                        }
                    }
                    
                    if (logConfig.isLogResponse()) {
                        responseInfo.append(" | 响应: ").append(formatResponse(result));
                    }
                    
                    // 慢请求使用warn级别，普通请求使用info级别
                    if (duration > logConfig.getSlowRequestThreshold()) {
                        logger.warn(responseInfo.toString());
                    } else {
                        logger.info(responseInfo.toString());
                    }
                }
            } catch (Exception e) {
                logger.warn("记录响应日志时发生异常: {}", e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 异常通知：记录异常信息
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void afterThrowing(JoinPoint joinPoint, Throwable exception) {
        if (!logConfig.isEnabled()) {
            return;
        }
        
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        logger.error("!!! 方法执行异常 [{}.{}] | 异常类型: {} | 异常信息: {}", 
            className, methodName, exception.getClass().getSimpleName(), exception.getMessage());
    }
    
    /**
     * 格式化参数
     */
    private String formatArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "无参数";
        }
        
        try {
            // 过滤掉HttpServletRequest, HttpServletResponse等类型的参数
            Object[] filteredArgs = Arrays.stream(args)
                .filter(arg -> arg != null && 
                    !arg.getClass().getName().startsWith("javax.servlet") &&
                    !arg.getClass().getName().startsWith("org.springframework.web"))
                .toArray();
                
            if (filteredArgs.length == 0) {
                return "无业务参数";
            }
            
            String argsJson = objectMapper.writeValueAsString(filteredArgs);
            // 根据配置限制参数长度
            int maxLength = logConfig.getMaxParamLength();
            return argsJson.length() > maxLength ? argsJson.substring(0, maxLength) + "..." : argsJson;
        } catch (Exception e) {
            return "参数序列化失败: " + e.getMessage();
        }
    }
    
    /**
     * 格式化响应结果
     */
    private String formatResponse(Object result) {
        if (result == null) {
            return "null";
        }
        
        try {
            String resultJson = objectMapper.writeValueAsString(result);
            // 根据配置限制响应长度
            int maxLength = logConfig.getMaxResponseLength();
            return resultJson.length() > maxLength ? resultJson.substring(0, maxLength) + "..." : resultJson;
        } catch (Exception e) {
            return "响应序列化失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        try {
            return IpUtils.getIpAddr(request);
        } catch (Exception e) {
            return "未知IP";
        }
    }
} 