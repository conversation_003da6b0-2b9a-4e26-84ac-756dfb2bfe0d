package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardRecord;

import java.util.List;

/**
 * 月度排行榜奖励业务Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface IMonthlyRankingRewardService {
    
    /**
     * 查询月度排行榜奖励发放记录
     *
     * @param id 月度排行榜奖励发放记录主键
     * @return 月度排行榜奖励发放记录
     */
    public MonthlyRankingRewardRecord selectMonthlyRankingRewardRecordById(Long id);

    /**
     * 查询月度排行榜奖励发放记录列表
     *
     * @param monthlyRankingRewardRecord 月度排行榜奖励发放记录
     * @return 月度排行榜奖励发放记录集合
     */
    public List<MonthlyRankingRewardRecord> selectMonthlyRankingRewardRecordList(MonthlyRankingRewardRecord monthlyRankingRewardRecord);

    /**
     * 查询指定年月的奖励发放记录（带用户信息）
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID（可选）
     * @return 奖励发放记录列表
     */
    public List<MonthlyRankingRewardRecord> selectRewardRecordWithUserInfo(String yearMonth, Long residentialId);

    /**
     * 查询用户的奖励历史记录
     *
     * @param userId 用户ID
     * @return 用户奖励历史
     */
    public List<MonthlyRankingRewardRecord> selectUserRewardHistory(Long userId);

    /**
     * 生成指定月份的奖励记录
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @return 生成的奖励记录数量
     */
    public int generateMonthlyRewards(String yearMonth);

    /**
     * 发放奖励给用户
     *
     * @param rewardRecord 奖励记录
     * @return 是否发放成功
     */
    public boolean issueReward(MonthlyRankingRewardRecord rewardRecord);

    /**
     * 批量发放待发放的奖励
     *
     * @return 发放成功的奖励数量
     */
    public int batchIssueRewards();

    /**
     * 删除指定年月的奖励记录
     *
     * @param yearMonth 年月
     * @return 结果
     */
    public int deleteRewardsByYearMonth(String yearMonth);

    /**
     * 获取上期榜单列表（从奖励记录中查询）
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @param residentialId 小区ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 上期榜单数据
     */
    public com.fs.swap.common.core.page.TableDataInfo getPreviousRankingList(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize);
} 