package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.CommunityNearby;

import java.util.List;

/**
 * 社区服务-周边推荐Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface ICommunityNearbyService 
{
    /**
     * 查询社区服务-周边推荐
     * 
     * @param id 社区服务-周边推荐主键
     * @return 社区服务-周边推荐
     */
    public CommunityNearby selectCommunityNearbyById(Long id);

    /**
     * 查询社区服务-周边推荐列表
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 社区服务-周边推荐集合
     */
    public List<CommunityNearby> selectCommunityNearbyList(CommunityNearby communityNearby);

    /**
     * 新增社区服务-周边推荐
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    public int insertCommunityNearby(CommunityNearby communityNearby);

    /**
     * 修改社区服务-周边推荐
     * 
     * @param communityNearby 社区服务-周边推荐
     * @return 结果
     */
    public int updateCommunityNearby(CommunityNearby communityNearby);

    /**
     * 批量删除社区服务-周边推荐
     * 
     * @param ids 需要删除的社区服务-周边推荐主键集合
     * @return 结果
     */
    public int deleteCommunityNearbyByIds(Long[] ids);

    /**
     * 删除社区服务-周边推荐信息
     * 
     * @param id 社区服务-周边推荐主键
     * @return 结果
     */
    public int deleteCommunityNearbyById(Long id);
    
    /**
     * 根据用户所在位置查询适用的周边列表
     * 如果参数为空，则返回所有可用的周边服务
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByLocation(Long regionId, Long communityId, Long residentialId);
    
    /**
     * 根据用户位置和距离查询周边列表
     *
     * @param userLocation 用户位置
     * @param maxDistance 最大距离（米）
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByDistance(String userLocation, Integer maxDistance);
    
    /**
     * 根据提交用户ID查询周边列表
     *
     * @param submitUserId 提交用户ID
     * @return 周边列表
     */
    public List<CommunityNearby> selectCommunityNearbyListBySubmitUserId(Long submitUserId);
    
    /**
     * 更新浏览次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementViewCount(Long id);
    
    /**
     * 更新拨打电话次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementCallCount(Long id);
    
    /**
     * 更新导航次数
     *
     * @param id 服务ID
     * @return 结果
     */
    public int incrementNavigateCount(Long id);
    
    /**
     * 审核服务
     *
     * @param id 服务ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditCommunityNearby(Long id, Integer auditStatus, Long auditUserId, String auditRemark);
    
    /**
     * 批量审核服务
     *
     * @param ids 服务ID列表
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int batchAuditCommunityNearby(List<Long> ids, Integer auditStatus, Long auditUserId, String auditRemark);
    
    /**
     * 根据标签搜索服务
     *
     * @param tags 标签列表
     * @return 服务列表
     */
    public List<CommunityNearby> selectCommunityNearbyListByTags(List<String> tags);
    
    /**
     * 获取推荐服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID  
     * @param residentialId 小区ID
     * @param limit 限制数量
     * @return 推荐服务列表
     */
    public List<CommunityNearby> selectRecommendedCommunityNearby(Long regionId, Long communityId, Long residentialId, Integer limit);
    
    /**
     * 获取热门服务列表
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param category 分类
     * @param limit 限制数量
     * @return 热门服务列表
     */
    public List<CommunityNearby> selectPopularCommunityNearby(Long regionId, Long communityId, String category, Integer limit);
    
    /**
     * 用户提交服务信息
     *
     * @param communityNearby 服务信息
     * @param submitUserId 提交用户ID
     * @return 结果
     */
    public int submitCommunityNearby(CommunityNearby communityNearby, Long submitUserId);
}
