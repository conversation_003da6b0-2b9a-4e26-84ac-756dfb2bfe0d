<view class="container">
  <view wx:if="{{ helpDetail }}" class="detail-content">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <image
          class="user-avatar"
          src="{{ helpDetail.avatar || '/static/img/default_avatar.png' }}"
          mode="aspectFill"
          bindtap="onUserProfile"
        />
        <view class="user-details">
          <view class="user-name-row">
            <text class="user-name">{{ helpDetail.nickname || '匿名用户' }}</text>
            <!-- 发布类型标签 -->
            <view class="publish-type-tag {{ helpDetail.publishType === '1' ? 'request-tag' : 'offer-tag' }}">
              <text class="tag-text">{{ helpDetail.publishType === '1' ? '需求' : '服务' }}</text>
            </view>
          </view>
          <view class="user-meta">
            <text class="publish-time">{{ helpDetail.createTime }}</text>
            <view class="meta-divider">·</view>
            <view class="view-count">
              <van-icon name="eye-o" size="12px" color="#969799" />
              <text class="view-text">{{ helpDetail.viewCount || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 标题和分类标签 -->
      <view class="title-section">
        <view class="title-row">
          <view wx:if="{{ helpDetail.title }}" class="help-title">{{ helpDetail.title }}</view>
          <view wx:if="{{ helpDetail.categoryName }}" class="category-tag">
            <text class="category-text">{{ helpDetail.categoryName }}</text>
          </view>
        </view>
      </view>

      <!-- 内容描述 -->
      <view class="help-content">{{ helpDetail.content }}</view>

      <!-- 图片展示 -->
      <view wx:if="{{ helpDetail.images && helpDetail.images.length > 0 }}" class="images-container">
        <view
          class="image-item {{ helpDetail.images.length === 1 ? 'single-image' : (helpDetail.images.length === 2 ? 'two-images' : 'three-images') }}"
          wx:for="{{ helpDetail.images }}"
          wx:for-index="idx"
          wx:key="index"
          wx:if="{{ idx < 6 }}"
          bindtap="onPreviewImage"
          data-index="{{ index }}"
          data-more="{{ helpDetail.images.length > 6 ? (helpDetail.images.length - 6) : '' }}"
        >
          <van-image
            width="100%"
            height="100%"
            src="{{ item }}"
            fit="cover"
            radius="12"
            lazy-load
          />
          <!-- 超过6张图片时在第6张显示更多提示 -->
          <view wx:if="{{ idx === 5 && helpDetail.images.length > 6 }}" class="more-images-overlay">
            <text class="more-text">+{{ helpDetail.images.length - 6 }}</text>
          </view>
        </view>
      </view>

      <!-- 截止日期 -->
      <view wx:if="{{ helpDetail.endTime }}" class="end-time-section">
        <view class="end-time">
          <van-icon name="clock-o" size="14px" color="#ff6b35" />
          <text class="end-time-text">截止日期 {{ helpDetail.endTime }}</text>
        </view>
      </view>
    </view>




  </view>

  <!-- 加载状态 -->
  <view wx:else class="loading-container">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 底部操作栏 - 始终显示 -->
  <view class="bottom-actions" wx:if="{{ helpDetail }}">
    <view class="action-group left">
      <button class="action-item share-button" open-type="share" hover-class="action-hover">
        <van-icon name="share-o" size="22px" color="#1989fa" />
        <text class="share-text">分享</text>
      </button>
    </view>
    <view class="action-group right">
      <view wx:if="{{ !isMyHelp }}" class="action-button-group">
        <!-- 联系按钮 - 只在状态不是2和3时显示 -->
        <view wx:if="{{ helpDetail.status !== '2' && helpDetail.status !== '3' }}" class="action-button contact" bindtap="onContact" hover-class="button-hover">
          <van-icon wx:if="{{ !contactLoading }}" name="chat-o" size="16px" />
          <van-loading wx:if="{{ contactLoading }}" size="16px" color="white" />
          <text>{{ contactLoading ? '联系中...' : '联系TA' }}</text>
        </view>
      </view>
      <view wx:else class="action-button-group">
        <!-- 根据互助状态显示不同的按钮 -->
        <view wx:if="{{helpDetail.status == '3'}}" class="action-button online" bindtap="onOnline" hover-class="button-hover">
          <text>重新上架</text>
        </view>
        <view wx:else class="action-button offline" bindtap="onOffline" hover-class="button-hover">
          <text>下架</text>
        </view>
        <view class="action-button edit" bindtap="onEdit" hover-class="button-hover">
          <van-icon name="edit" size="16px" />
          <text>编辑</text>
        </view>
      </view>
    </view>
  </view>



  <!-- 联系确认弹框 -->
  <van-dialog
    use-slot
    title="确认联系"
    show="{{ showContactConfirmDialog }}"
    show-cancel-button
    confirm-button-text="确认联系"
    cancel-button-text="取消"
    confirm-button-color="#ee0a24"
    bind:close="onContactConfirmDialogClose"
    bind:confirm="onContactConfirm"
  >
    <view class="contact-dialog-content">
      <view class="contact-dialog-item">
        <text class="contact-dialog-label">互助：</text>
        <text class="contact-dialog-value">{{helpDetail.title}}</text>
      </view>
      <view class="contact-dialog-item">
        <text class="contact-dialog-label">发布者：</text>
        <text class="contact-dialog-value">{{helpDetail.nickname}}</text>
      </view>
      <view class="contact-dialog-item">
        <text class="contact-dialog-label">类型：</text>
        <text class="contact-dialog-value">{{helpDetail.publishType === '1' ? '需求' : '服务'}}</text>
      </view>
      <view class="contact-dialog-item">
        <text class="contact-dialog-label">分类：</text>
        <text class="contact-dialog-value">{{helpDetail.categoryName}}</text>
      </view>
      <view wx:if="{{ helpDetail.endTime }}" class="contact-dialog-item">
        <text class="contact-dialog-label">截止日期：</text>
        <text class="contact-dialog-value">{{helpDetail.endTime}}</text>
      </view>
      <view class="contact-dialog-item">
        <text class="contact-dialog-label">服务费：</text>
        <text class="contact-dialog-value">
          <text class="original-price">50碳豆</text>
          {{helpSilver}}碳豆
        </text>
      </view>
      <view class="contact-dialog-notice">
        <text>联系后将从您的账户扣除相应碳豆</text>
      </view>
    </view>
  </van-dialog>

  <!-- 联系结果弹框 -->
  <van-dialog
    use-slot
    title="{{contactResultTitle}}"
    show="{{ showContactResultDialog }}"
    confirm-button-text="{{contactResultTitle === '联系成功' ? '联系发布者' : '确定'}}"
    show-cancel-button="{{contactResultTitle === '联系成功'}}"
    cancel-button-text="查看订单"
    bind:close="onContactResultDialogClose"
    bind:confirm="onShowContactInfo"
    bind:cancel="onViewOrder"
  >
    <view class="contact-result-content">
      <view class="success-icon" wx:if="{{contactResultTitle === '联系成功'}}">
        <van-icon name="success" size="48px" color="#07c160" />
      </view>
      <view class="result-message" wx:if="{{contactResultTitle !== '联系成功'}}">
        <text class="message-text">
          联系失败，请稍后重试
        </text>
      </view>
      <view class="contact-tip" wx:if="{{contactResultTitle === '联系成功'}}">
        <view class="tip-item">
          <van-icon name="info-o" size="16px" color="#1989fa" />
          <text class="tip-text">点击下方联系对方获取联系方式</text>
        </view>
      </view>
    </view>
  </van-dialog>

  <!-- 联系方式弹窗组件 -->
  <contact-modal
    show="{{showContactModal}}"
    targetNickname="{{targetNickname}}"
    contacts="{{contacts}}"
    fileUrl="{{fileUrl}}"
    bind:close="closeContactModal"
  ></contact-modal>

  <!-- 下架确认弹窗 -->
  <van-dialog
    title="确认下架"
    show="{{ showOfflineDialog }}"
    show-cancel-button
    cancel-button-text="取消"
    confirm-button-text="确认下架"
    bind:close="onOfflineDialogClose"
    bind:confirm="onOfflineConfirm"
  >
    <view class="offline-dialog-content">
      <text>确定要下架此互助吗？下架后其他用户将无法看到此互助。</text>
    </view>
  </van-dialog>

  <!-- 重新上架确认弹窗 -->
  <van-dialog
    title="确认上架"
    show="{{ showOnlineDialog }}"
    show-cancel-button
    cancel-button-text="取消"
    confirm-button-text="确认上架"
    bind:close="onOnlineDialogClose"
    bind:confirm="onOnlineConfirm"
  >
    <view class="online-dialog-content">
      <text>确定要重新上架此互助吗？上架后其他用户将可以看到此互助。</text>
    </view>
  </van-dialog>

  <!-- 登录检查组件 -->
  <login-action id="loginAction" />


</view>
