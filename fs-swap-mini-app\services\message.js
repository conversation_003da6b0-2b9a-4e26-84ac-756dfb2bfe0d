const api = require('../config/api')

/**
 * 消息服务
 * 处理系统通知、用户消息等功能
 */
class MessageService {
  
  /**
   * 获取消息列表
   * @param {Object} params - 查询参数
   * @param {string} params.type - 消息类型 system|chat|activity
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页大小
   */
  async getMessageList(params = {}) {
    try {
      const result = await api.getMessageList(params)
      
      // 兼容不同的API数据格式
      // 优先使用 result.rows，如果不存在则尝试 result.data.rows 或 result.data.list
      const dataList = result.rows || result.data?.rows || result.data?.list || []
      
      // 数据处理和格式化
      const processedList = dataList.map(item => ({
        id: item.id,
        type: item.type,
        title: item.title,
        content: item.content,
        avatar: item.avatar,
        isRead: Boolean(item.isRead), // 将数字转换为布尔值
        createTime: item.createTime,
        jumpType: item.jumpType, // page|tabBar|order|activity|none
        jumpUrl: item.jumpUrl,
        relatedId: item.relatedId,
        actionText: item.actionText
      }))
      
      // 兼容不同的total字段位置
      const total = result.total || result.data?.total || 0
      
      return {
        list: processedList,
        total: total,
        hasMore: processedList.length === (params.pageSize || 20)
      }
    } catch (error) {
      console.error('获取消息列表失败:', error)
      // 根据错误类型进行不同处理
      if (error.code === 401) {
        // 未授权，交给请求拦截器处理
        throw error
      }
      
      // 其他错误返回空数据，避免页面崩溃
      return {
        list: [],
        total: 0,
        hasMore: false
      }
    }
  }

  /**
   * 获取未读消息数量
   * @returns {Promise<Object>} 未读消息统计
   */
  async getUnreadCount() {
    try {
      const result = await api.getMessageUnreadCount()
      
      // 兼容不同的数据格式
      const data = result.data || result
      
      return {
        system: data?.system || 0,
        chat: data?.chat || 0,
        activity: data?.activity || 0,
        total: data?.total || 0
      }
    } catch (error) {
      console.error('获取未读数量失败:', error)
      // 网络错误时返回0，不影响页面正常显示
      return {
        system: 0,
        chat: 0,
        activity: 0,
        total: 0
      }
    }
  }

  /**
   * 标记单条或多条消息为已读
   * @param {Array|string} messageIds - 消息ID数组或单个ID
   */
  async markAsRead(messageIds) {
    try {
      const ids = Array.isArray(messageIds) ? messageIds : [messageIds]
      const result = await api.markMessageAsRead({ messageIds: ids })
      
      // 通知全局更新未读数量
      this._notifyUnreadCountChange(-ids.length)
      
      return result
    } catch (error) {
      console.error('标记已读失败:', error)
      throw error
    }
  }

  /**
   * 标记某类型的所有消息为已读
   * @param {string} type - 消息类型
   */
  async markAllAsRead(type) {
    try {
      const result = await api.markAllMessagesAsRead({ type })
      
      // 重新获取未读数量
      const app = getApp()
      if (app && app.refreshMessageUnreadCount) {
        app.refreshMessageUnreadCount()
      }
      
      return result
    } catch (error) {
      console.error('批量标记已读失败:', error)
      throw error
    }
  }

  /**
   * 删除消息
   * @param {Array|string} messageIds - 消息ID数组或单个ID
   */
  async deleteMessages(messageIds) {
    try {
      const ids = Array.isArray(messageIds) ? messageIds : [messageIds]
      const result = await api.deleteMessages({ messageIds: ids })
      return result
    } catch (error) {
      console.error('删除消息失败:', error)
      throw error
    }
  }

  /**
   * 发送系统通知（管理端使用）
   * @param {Object} message - 消息内容
   * @param {string} message.title - 消息标题
   * @param {string} message.content - 消息内容
   * @param {string} message.type - 消息类型
   * @param {Array} message.targetUsers - 目标用户ID数组
   * @param {Object} message.jumpConfig - 跳转配置
   */
  async sendSystemNotification(message) {
    try {
      const result = await api.sendSystemNotification(message)
      return result
    } catch (error) {
      console.error('发送系统通知失败:', error)
      throw error
    }
  }

  /**
   * 用户间发送消息（后期功能）
   * @param {Object} params - 发送参数
   * @param {string} params.toUserId - 接收用户ID
   * @param {string} params.content - 消息内容
   * @param {string} params.type - 消息类型 text|image|voice
   * @param {string} params.extraData - 额外数据（如图片URL、语音时长等）
   */
  async sendUserMessage(params) {
    try {
      const result = await api.sendUserMessage(params)
      return result
    } catch (error) {
      console.error('发送用户消息失败:', error)
      throw error
    }
  }

  /**
   * 获取会话列表（后期功能）
   * @param {Object} params - 查询参数
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页大小
   */
  async getConversationList(params = {}) {
    try {
      const result = await api.getConversationList(params)
      return {
        list: result.data?.list || [],
        total: result.data?.total || 0,
        hasMore: result.data?.hasMore || false
      }
    } catch (error) {
      console.error('获取会话列表失败:', error)
      return { list: [], total: 0, hasMore: false }
    }
  }

  /**
   * 获取会话详情和历史消息
   * @param {string} conversationId - 会话ID
   * @param {Object} params - 查询参数
   */
  async getConversationMessages(conversationId, params = {}) {
    try {
      const result = await api.getConversationMessages(conversationId, params)
      return {
        list: result.data?.list || [],
        hasMore: result.data?.hasMore || false
      }
    } catch (error) {
      console.error('获取会话消息失败:', error)
      return { list: [], hasMore: false }
    }
  }

  /**
   * 设置消息免打扰
   * @param {string} type - 消息类型或会话ID
   * @param {boolean} mute - 是否免打扰
   */
  async setMessageMute(type, mute) {
    try {
      const result = await api.setMessageMute({ type, mute })
      return result
    } catch (error) {
      console.error('设置消息免打扰失败:', error)
      throw error
    }
  }

  /**
   * 创建或获取会话
   * @param {number} targetUserId - 目标用户ID
   */
  async createConversation(targetUserId) {
    try {
      const result = await api.createConversation({ targetUserId })
      return result
    } catch (error) {
      console.error('创建会话失败:', error)
      throw error
    }
  }

  /**
   * 更新消息设置
   * @param {Object} settings - 消息设置
   */
  async updateMessageSettings(settings) {
    try {
      const result = await api.updateMessageSettings(settings)
      return result
    } catch (error) {
      console.error('更新消息设置失败:', error)
      throw error
    }
  }

  /**
   * 搜索消息
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.type - 消息类型
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页大小
   */
  async searchMessages(params) {
    try {
      const result = await api.searchMessages(params)
      return {
        list: result.data?.list || [],
        total: result.data?.total || 0,
        hasMore: result.data?.hasMore || false
      }
    } catch (error) {
      console.error('搜索消息失败:', error)
      return { list: [], total: 0, hasMore: false }
    }
  }

  /**
   * 撤回消息
   * @param {string} messageId - 消息ID
   */
  async recallMessage(messageId) {
    try {
      const result = await api.recallMessage(messageId)
      return result
    } catch (error) {
      console.error('撤回消息失败:', error)
      throw error
    }
  }

  /**
   * 通知全局未读数量变化
   * @private
   */
  _notifyUnreadCountChange(delta) {
    try {
      const app = getApp()
      if (app && app.decreaseMessageUnreadCount && delta < 0) {
        app.decreaseMessageUnreadCount(Math.abs(delta))
      } else if (app && app.increaseMessageUnreadCount && delta > 0) {
        app.increaseMessageUnreadCount(delta)
      }
    } catch (error) {
      console.error('通知未读数量变化失败:', error)
    }
  }
}

module.exports = new MessageService() 