<!--pages/feedback/index.wxml-->
<view class="container">
  <form bindsubmit="handleSubmit">
    <!-- 反馈内容 -->
    <view class="feedback-card">
      <view class="section-title">反馈内容</view>
      <view class="content-section">
        <textarea
          class="content-textarea"
          placeholder="请详细描述您遇到的问题或建议..."
          value="{{formData.content}}"
          bindinput="onContentInput"
          maxlength="{{MAX_CONTENT_LENGTH}}"
          show-confirm-bar="{{false}}"
          auto-height
        ></textarea>
        <view class="char-count">{{formData.content.length}}/{{MAX_CONTENT_LENGTH}}</view>
      </view>
    </view>

    <!-- 图片上传 -->
    <view class="feedback-card">
      <view class="section-title">相关图片（可选）</view>
      <view class="upload-section">
        <van-uploader
          file-list="{{ fileList }}"
          bind:after-read="afterRead"
          bind:delete="onDeleteFile"
          max-count="{{ MAX_UPLOAD_COUNT }}"
          max-size="{{ 5 * 1024 * 1024 }}"
          bind:oversize="onOversize"
          upload-text="上传图片"
          preview-size="160rpx"
        />
        <view class="upload-tip">最多上传{{MAX_UPLOAD_COUNT}}张图片，每张不超过5MB</view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="feedback-card">
      <view class="section-title">联系方式（可选）</view>
      <view class="contact-section">
        <input
          class="contact-input"
          placeholder="请输入您的联系方式，便于我们回复"
          value="{{formData.contact}}"
          bindinput="onContactInput"
          maxlength="50"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        form-type="submit" 
        class="submit-btn"
        loading="{{ submitting }}"
        disabled="{{ !formValid }}"
      >提交反馈</button>
    </view>
  </form>
</view>
