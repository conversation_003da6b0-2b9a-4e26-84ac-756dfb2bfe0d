package com.fs.swap.admin.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 周边推荐审核DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class NearbyAuditDTO {
    
    /** 需要审核的ID列表 */
    @NotEmpty(message = "审核ID不能为空")
    private List<Long> ids;
    
    /** 审核状态 1:通过 2:拒绝 */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;
    
    /** 审核备注 */
    private String auditRemark;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    @Override
    public String toString() {
        return "NearbyAuditDTO{" +
                "ids=" + ids +
                ", auditStatus=" + auditStatus +
                ", auditRemark='" + auditRemark + '\'' +
                '}';
    }
} 