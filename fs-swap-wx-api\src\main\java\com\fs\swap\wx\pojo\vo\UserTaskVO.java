package com.fs.swap.wx.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 用户任务VO
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Data
public class UserTaskVO {
    
    /** 用户任务ID */
    private Long id;

    /** 任务编码 */
    private String taskCode;

    /** 任务名称 */
    private String taskName;

    /** 任务描述 */
    private String taskDesc;

    /** 任务类型 */
    private String taskType;

    /** 任务类型名称 */
    private String taskTypeName;

    /** 当前完成次数 */
    private Integer currentCount;

    /** 目标次数 */
    private Integer targetCount;

    /** 状态 */
    private String status;

    /** 状态名称 */
    private String statusName;

    /** 奖励碳豆数 */
    private Integer rewardSilver;

    /** 图标URL */
    private String icon;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rewardTime;

    /** 进度百分比 */
    private Integer progressPercent;

    /** 是否可领取 */
    private Boolean canClaim;

    /** 是否已完成 */
    private Boolean isCompleted;
} 