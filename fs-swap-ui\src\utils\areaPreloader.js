import areaDataCache from '@/utils/areaDataCache'
import request from '@/utils/request'

/**
 * 区域数据预加载工具
 * 用于在应用启动时提前加载区域相关数据
 */

// 存储全局加载Promise
let areaLoadPromise = null

// 标记数据是否已加载
let isDataLoaded = false

/**
 * 预加载所有区域数据到缓存
 * @returns {Promise} - 加载完成的Promise
 */
export function preloadAreaData() {
  if (areaLoadPromise) {
    return areaLoadPromise
  }

  // 创建一个全局Promise防止重复加载
  areaLoadPromise = new Promise(async (resolve) => {
    try {
      // 如果已经加载过，直接返回
      if (isDataLoaded) {
        resolve()
        return
      }

      // 1. 加载行政区域数据
      const regionRes = await request.get('/system/region/list', {
        params: {
          pageSize: 9999,
          pageNum: 1
        }
      })

      // 2. 加载社区数据
      const communityRes = await request.get('/system/community/list', {
        params: {
          pageSize: 9999,
          pageNum: 1
        }
      })

      // 3. 加载小区数据
      const residentialRes = await request.get('/system/area/list', {
        params: {
          pageSize: 9999,
          pageNum: 1
        }
      })

      // 4. 处理数据并写入缓存
      try {
        if (regionRes && regionRes.code === 200) {
          const regionCount = regionRes.rows && Array.isArray(regionRes.rows) ? regionRes.rows.length : 0;
          areaDataCache.initRegions(regionRes);
        }
      } catch (err) {
        // 处理区域数据错误
      }

      try {
        if (communityRes && communityRes.code === 200) {
          const communityCount = communityRes.rows && Array.isArray(communityRes.rows) ? communityRes.rows.length : 0;
          areaDataCache.initCommunities(communityRes);
        }
      } catch (err) {
        // 处理社区数据错误
      }

      try {
        if (residentialRes && residentialRes.code === 200) {
          const residentialCount = residentialRes.rows && Array.isArray(residentialRes.rows) ? residentialRes.rows.length : 0;
          areaDataCache.initResidentials(residentialRes);
        }
      } catch (err) {
        // 处理小区数据错误
      }

      isDataLoaded = true

      resolve()
    } catch (error) {
      // 即使失败也标记为已完成，允许后续组件自行加载数据
      resolve()
    }
  })

  return areaLoadPromise
}

/**
 * 获取当前的加载Promise
 * @returns {Promise|null} - 当前的加载Promise或null
 */
export function getAreaLoadPromise() {
  return areaLoadPromise
}

export default {
  preloadAreaData,
  getAreaLoadPromise
}