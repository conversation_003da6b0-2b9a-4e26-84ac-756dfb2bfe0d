/* 联系人选择页面样式 */
page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #3B7FFF;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  --border-radius: 16rpx;
  --transition: all 0.3s ease;
  --border-color: #eeeeee;
}

.contact-select-container {
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* 搜索栏 */
.search-section {
  background: var(--primary-color);
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: var(--secondary-color);
  border-radius: 40rpx;
  padding: 0 32rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 28rpx;
  color: var(--text-light);
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
  height: 80rpx;
  line-height: 80rpx;
}

/* 联系人列表 */
.contact-list {
  flex: 1;
  background: var(--background-color);
}

.contact-items {
  background: var(--primary-color);
  margin: 24rpx 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: var(--transition);
  background: var(--primary-color);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background-color: var(--secondary-color);
}

/* 头像区域 */
.avatar-wrapper {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: var(--secondary-color);
  border: 2rpx solid var(--primary-color);
}

.online-status {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 3rpx solid var(--primary-color);
}

/* 联系人信息 */
.contact-info {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
}

.contact-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-status {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 右侧箭头 */
.arrow-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.arrow-icon .iconfont {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  line-height: 1;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .search-section {
    padding: 20rpx 24rpx;
  }
  
  .contact-item {
    padding: 20rpx 24rpx;
  }
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
  }
  
  .contact-name {
    font-size: 30rpx;
  }
  
  .contact-status {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.contact-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 