<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务编码" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable>
          <el-option
            v-for="dict in dict.type.task_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="完成时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:user-task:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefreshTask"
          v-hasPermi="['operation:user-task:refresh']"
        >刷新任务</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userTaskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="userId" width="80" />
      <el-table-column label="用户名" align="center" prop="userName" width="120" />
      <el-table-column label="任务编码" align="center" prop="taskCode" width="150" />
      <el-table-column label="任务名称" align="center" prop="taskName" width="150" />
      <el-table-column label="任务类型" align="center" prop="taskType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_type" :value="scope.row.taskType"/>
        </template>
      </el-table-column>
      <el-table-column label="目标次数" align="center" prop="targetCount" width="100" />
      <el-table-column label="当前进度" align="center" prop="currentCount" width="100">
        <template slot-scope="scope">
          <el-progress
            :percentage="(scope.row.currentCount / scope.row.targetCount * 100).toFixed(0)"
            :stroke-width="15"
            :text-inside="true"
            :format="(percentage) => `${scope.row.currentCount}/${scope.row.targetCount}`"
          ></el-progress>
        </template>
      </el-table-column>
      <el-table-column label="奖励碳豆" align="center" prop="rewardSilver" width="100">
        <template slot-scope="scope">
          <span class="reward-silver">{{ scope.row.rewardSilver }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="completedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领取时间" align="center" prop="claimedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.claimedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" align="center" prop="expiredTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expiredTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['operation:user-task:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.status === '2'"
            size="mini"
            type="text"
            icon="el-icon-coin"
            @click="handleClaim(scope.row)"
            v-hasPermi="['operation:user-task:claim']"
          >领取</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 用户任务详情对话框 -->
    <el-dialog title="用户任务详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ viewForm.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ viewForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="任务编码">{{ viewForm.taskCode }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ viewForm.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <dict-tag :options="dict.type.task_type" :value="viewForm.taskType"/>
        </el-descriptions-item>
        <el-descriptions-item label="触发事件">
          <dict-tag :options="dict.type.task_event_type" :value="viewForm.triggerEvent"/>
        </el-descriptions-item>
        <el-descriptions-item label="目标次数">{{ viewForm.targetCount }}</el-descriptions-item>
        <el-descriptions-item label="当前进度">{{ viewForm.currentCount }}</el-descriptions-item>
        <el-descriptions-item label="奖励碳豆">
          <span class="reward-silver">{{ viewForm.rewardSilver }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.task_status" :value="viewForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ parseTime(viewForm.completedTime) }}</el-descriptions-item>
        <el-descriptions-item label="领取时间">{{ parseTime(viewForm.claimedTime) }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ parseTime(viewForm.expiredTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 刷新任务对话框 -->
    <el-dialog title="刷新任务" :visible.sync="refreshOpen" width="500px" append-to-body>
      <el-form ref="refreshForm" :model="refreshForm" :rules="refreshRules" label-width="100px">
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="refreshForm.taskType" placeholder="请选择要刷新的任务类型" style="width: 100%">
            <el-option label="全部类型" value="" />
            <el-option
              v-for="dict in dict.type.task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户范围" prop="userScope">
          <el-radio-group v-model="refreshForm.userScope">
            <el-radio label="all">所有用户</el-radio>
            <el-radio label="specific">指定用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="refreshForm.userScope === 'specific'" label="用户ID" prop="userId">
          <el-input v-model="refreshForm.userId" placeholder="请输入用户ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRefresh">确 定</el-button>
        <el-button @click="refreshOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserTask, getUserTask, claimTaskReward, refreshTask } from '@/api/operation/user-task'

export default {
  name: 'UserTask',
  dicts: ['task_type', 'task_status', 'task_event_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户任务表格数据
      userTaskList: [],
      // 是否显示详情弹出层
      viewOpen: false,
      // 是否显示刷新弹出层
      refreshOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        taskCode: null,
        taskType: null,
        status: null,
        beginTime: null,
        endTime: null
      },
      // 详情表单参数
      viewForm: {},
      // 刷新表单参数
      refreshForm: {
        taskType: '',
        userScope: 'all',
        userId: null
      },
      // 刷新表单校验
      refreshRules: {
        userScope: [
          { required: true, message: '用户范围不能为空', trigger: 'change' }
        ],
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    // 日期范围
    dateRange(val) {
      if (val === null) {
        this.queryParams.beginTime = null
        this.queryParams.endTime = null
      } else {
        this.queryParams.beginTime = val[0]
        this.queryParams.endTime = val[1]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询用户任务列表 */
    getList() {
      this.loading = true
      listUserTask(this.queryParams).then(response => {
        this.userTaskList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleView(row) {
      getUserTask(row.id).then(response => {
        this.viewForm = response.data
        this.viewOpen = true
      })
    },
    /** 领取按钮操作 */
    handleClaim(row) {
      this.$modal.confirm('确认要领取"' + row.taskName + '"的任务奖励吗？').then(function() {
        return claimTaskReward(row.userId, row.taskCode)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('领取成功')
      }).catch(() => {})
    },
    /** 刷新任务按钮操作 */
    handleRefreshTask() {
      this.refreshForm = {
        taskType: '',
        userScope: 'all',
        userId: null
      }
      this.refreshOpen = true
    },
    /** 提交刷新 */
    submitRefresh() {
      this.$refs['refreshForm'].validate(valid => {
        if (valid) {
          const params = {
            taskType: this.refreshForm.taskType || null,
            userId: this.refreshForm.userScope === 'specific' ? this.refreshForm.userId : null
          }
          refreshTask(params).then(response => {
            this.$modal.msgSuccess('刷新成功')
            this.refreshOpen = false
            this.getList()
          })
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/user-task/export', {
        ...this.queryParams
      }, `user_task_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.reward-silver {
  color: #ff9800;
  font-weight: bold;
}
</style> 