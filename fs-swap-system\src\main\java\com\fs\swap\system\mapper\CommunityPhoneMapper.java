package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.CommunityPhone;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 社区服务-常用电话Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface CommunityPhoneMapper 
{
    /**
     * 查询社区服务-常用电话
     * 
     * @param id 社区服务-常用电话主键
     * @return 社区服务-常用电话
     */
    public CommunityPhone selectCommunityPhoneById(Long id);

    /**
     * 查询社区服务-常用电话列表
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 社区服务-常用电话集合
     */
    public List<CommunityPhone> selectCommunityPhoneList(CommunityPhone communityPhone);

    /**
     * 新增社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    public int insertCommunityPhone(CommunityPhone communityPhone);

    /**
     * 修改社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    public int updateCommunityPhone(CommunityPhone communityPhone);

    /**
     * 删除社区服务-常用电话
     * 
     * @param id 社区服务-常用电话主键
     * @return 结果
     */
    public int deleteCommunityPhoneById(Long id);

    /**
     * 批量删除社区服务-常用电话
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunityPhoneByIds(Long[] ids);
    
    /**
     * 根据用户所在位置查询适用的电话列表
     * 如果参数为空，则返回所有可用的电话
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 电话列表
     */
    public List<CommunityPhone> selectCommunityPhoneListByLocation(@Param("regionId") Long regionId, 
                                                                  @Param("communityId") Long communityId, 
                                                                  @Param("residentialId") Long residentialId);
    
    /**
     * 根据提交用户ID查询电话列表
     *
     * @param submitUserId 提交用户ID
     * @return 电话列表
     */
    public List<CommunityPhone> selectCommunityPhoneListBySubmitUserId(@Param("submitUserId") Long submitUserId);
    
    /**
     * 审核电话
     *
     * @param id 电话ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditTime 审核时间
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditCommunityPhone(@Param("id") Long id, 
                                  @Param("auditStatus") Integer auditStatus, 
                                  @Param("auditUserId") Long auditUserId, 
                                  @Param("auditTime") java.util.Date auditTime, 
                                  @Param("auditRemark") String auditRemark);
    
    /**
     * 更新拨打次数
     *
     * @param id 电话ID
     * @return 结果
     */
    public int incrementCallCount(@Param("id") Long id);
    
    /**
     * 根据分类统计电话数量
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param residentialId 小区ID
     * @return 统计结果
     */
    public List<CommunityPhone> getPhoneStatsByCategory(@Param("regionId") Long regionId, 
                                                       @Param("communityId") Long communityId, 
                                                       @Param("residentialId") Long residentialId);
}
