<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.ConversationMapper">

    <resultMap type="Conversation" id="ConversationResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="avatar"    column="avatar"    />
        <result property="lastMessageId"    column="last_message_id"    />
        <result property="lastMessageContent"    column="last_message_content"    />
        <result property="lastMessageTime"    column="last_message_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!-- 会话列表DTO结果映射 -->
    <resultMap type="com.fs.swap.common.core.domain.dto.ConversationListDTO" id="ConversationListResult">
        <result property="conversationId"       column="conversationId"       />
        <result property="type"                 column="type"                 />
        <result property="title"                column="title"                />
        <result property="avatar"               column="avatar"               />
        <result property="lastMessageId"        column="lastMessageId"        />
        <result property="lastMessageContent"   column="lastMessageContent"   />
        <result property="lastMessageTime"      column="lastMessageTime"      />
        <result property="unreadCount"          column="unreadCount"          />
        <result property="isMuted"              column="isMuted"              />
        <result property="updateTime"           column="updateTime"           />
    </resultMap>

    <sql id="selectConversationVo">
        select id, type, title, avatar, last_message_id, last_message_content, last_message_time, create_time, update_time from conversation
    </sql>

    <select id="selectConversationList" parameterType="Conversation" resultMap="ConversationResult">
        <include refid="selectConversationVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="lastMessageId != null  and lastMessageId != ''"> and last_message_id = #{lastMessageId}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectConversationById" parameterType="String" resultMap="ConversationResult">
        <include refid="selectConversationVo"/>
        where id = #{id}
    </select>

    <!-- 获取用户会话列表（优化版本，一次查询获取所有数据） -->
    <select id="getUserConversations" parameterType="long" resultMap="ConversationListResult">
        SELECT 
            c.id as conversationId,
            c.type,
            CASE 
                WHEN c.type = 'single' THEN 
                    COALESCE(other_user.nickname, '用户')
                ELSE c.title 
            END as title,
            CASE 
                WHEN c.type = 'single' THEN 
                    COALESCE(other_user.avatar, '')
                ELSE c.avatar 
            END as avatar,
            c.last_message_id as lastMessageId,
            IFNULL(c.last_message_content, '') as lastMessageContent,
            IFNULL(c.last_message_time, c.update_time) as lastMessageTime,
            IFNULL(cm.unread_count, 0) as unreadCount,
            IFNULL(cm.is_muted, 0) as isMuted,
            c.update_time as updateTime
        FROM conversation c
        INNER JOIN conversation_member cm ON c.id = cm.conversation_id 
            AND cm.user_id = #{userId} 
            AND cm.status = 0
        LEFT JOIN conversation_member cm_other ON c.id = cm_other.conversation_id 
            AND cm_other.user_id != #{userId} 
            AND cm_other.status = 0
            AND c.type = 'single'
        LEFT JOIN user_info other_user ON cm_other.user_id = other_user.id
        ORDER BY c.update_time DESC
    </select>

    <insert id="insertConversation" parameterType="Conversation">
        insert into conversation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="title != null">title,</if>
            <if test="avatar != null">avatar,</if>
            <if test="lastMessageId != null">last_message_id,</if>
            <if test="lastMessageContent != null">last_message_content,</if>
            <if test="lastMessageTime != null">last_message_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="title != null">#{title},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="lastMessageId != null">#{lastMessageId},</if>
            <if test="lastMessageContent != null">#{lastMessageContent},</if>
            <if test="lastMessageTime != null">#{lastMessageTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateConversation" parameterType="Conversation">
        update conversation
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="lastMessageId != null">last_message_id = #{lastMessageId},</if>
            <if test="lastMessageContent != null">last_message_content = #{lastMessageContent},</if>
            <if test="lastMessageTime != null">last_message_time = #{lastMessageTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 更新会话最后消息信息 -->
    <update id="updateLastMessage">
        update conversation 
        set last_message_id = #{lastMessageId},
            last_message_content = #{lastMessageContent},
            last_message_time = #{lastMessageTime},
            update_time = #{lastMessageTime}
        where id = #{conversationId}
    </update>

    <delete id="deleteConversationById" parameterType="String">
        delete from conversation where id = #{id}
    </delete>

    <delete id="deleteConversationByIds" parameterType="String">
        delete from conversation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查会话是否存在 -->
    <select id="checkConversationExists" resultType="int">
        select count(1) from conversation where id = #{conversationId}
    </select>

    <!-- 根据参与者获取单聊会话ID -->
    <select id="getSingleConversationId" resultType="String">
        SELECT c.id 
        FROM conversation c
        INNER JOIN conversation_member cm1 ON c.id = cm1.conversation_id AND cm1.user_id = #{userId1} AND cm1.status = 0
        INNER JOIN conversation_member cm2 ON c.id = cm2.conversation_id AND cm2.user_id = #{userId2} AND cm2.status = 0
        WHERE c.type = 'single'
        LIMIT 1
    </select>

</mapper> 