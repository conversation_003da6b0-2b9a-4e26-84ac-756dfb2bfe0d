<view class="system-message-container">
  <!-- 消息列表 -->
  <scroll-view 
    class="message-list" 
    scroll-y 
    enhanced 
    show-scrollbar="{{false}}"
    refresher-enabled 
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onReachBottom"
  >
    <view class="message-items">
      <view 
        class="message-item {{item.isRead ? '' : 'unread'}}"
        wx:for="{{messageList}}" 
        wx:key="id"
        data-item="{{item}}"
        bindtap="onMessageTap"
      >
        <!-- 消息图标 -->
        <view class="message-icon">
          <image src="{{item.avatar || '/static/img/system-message.png'}}" class="icon-img"></image>
          <view wx:if="{{!item.isRead}}" class="unread-dot"></view>
        </view>
        
        <!-- 消息内容 -->
        <view class="message-content">
          <view class="content-header">
            <text class="message-title">{{item.title}}</text>
            <text class="message-time">{{item.timeText}}</text>
          </view>
          <view class="message-desc">{{item.content}}</view>
        </view>
        
        <!-- 消息操作 -->
        <view class="message-action" wx:if="{{item.actionText}}">
          <text class="action-text">{{item.actionText}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{messageList.length === 0 && !isLoading}}">
      <view class="empty-icon">📮</view>
      <text class="empty-text">暂无系统消息</text>
      <text class="empty-desc">系统消息会在这里显示</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{isLoading}}">
      <van-loading size="16px" color="#3B7FFF" />
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view> 