/* pages/community-phone-publish/index.wxss */
page {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

.container {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 20rpx 24rpx;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.form-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 24rpx 0;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  padding: 0 28rpx 20rpx;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.field-container {
  padding: 12rpx 28rpx;
  position: relative;
}

.field-label {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required::after {
  content: '*';
  color: #ee0a24;
  margin-left: 4px;
}

.submit-section {
  padding: 40rpx 32rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #3B7FFF;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(59, 127, 255, 0.25);
  letter-spacing: 1rpx;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
  box-shadow: none;
}

.submit-btn.disabled {
  background-color: #cccccc !important;
  color: #ffffff !important;
  box-shadow: none !important;
  opacity: 0.6 !important;
  pointer-events: none !important;
}

/* 修改van-field样式 */
.van-field__label {
  width: 180rpx !important;
  font-size: 28rpx !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.van-field__value {
  font-size: 28rpx !important;
}

.van-field__placeholder {
  font-size: 28rpx !important;
}

.van-field__body {
  font-size: 28rpx !important;
}

.van-field__input {
  font-size: 28rpx !important;
  color: #333 !important;
}

.field-input {
  font-size: 28rpx !important;
  color: #333 !important;
}

.van-field__right-icon {  margin-right: 8rpx !important;  color: #999 !important;}/* 错误消息样式 */.error-message {  font-size: 24rpx;  color: #ee0a24;  margin-top: 8rpx;  margin-left: 0;  line-height: 1.4;}
