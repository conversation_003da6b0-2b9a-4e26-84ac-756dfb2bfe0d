// pages/community-service/index.js
const util = require('../../utils/util.js')
const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo.js')
const userUtils = require('../../utils/user.js')
const app = getApp()

Page({
    data: {
    // 筛选相关
    nearbyFilter: 'all', // 周边筛选：all, business, entertainment, facility
    searchValue: '', // 搜索关键词
    sortByDistance: true, // 是否按距离排序（true: 距离排序, false: 默认排序）
    showListMode: true, // 是否显示列表模式

    // 分类数据（从后端字典接口获取）
    categories: [],

    // 列表交互相关
    listExpanded: false, // 列表是否展开
    showDetail: false, // 是否显示详情
    selectedService: null, // 当前选中的服务详情
    listHeight: 280, // 列表高度
    listTranslateY: 0, // 列表Y轴偏移
    listCollapsed: false, // 列表是否收起状态
    showExpandHint: false, // 是否显示"查看全部结果"提示
    
    // 拖拽相关
    dragStartY: 0, // 拖拽开始Y坐标
    dragStartHeight: 280, // 拖拽开始时的高度
    isDragging: false, // 是否正在拖拽
    lastScrollTop: 0, // 上次滚动位置
    scrollDirection: 'none', // 滚动方向: 'up', 'down', 'none'
    isAtTop: false, // 是否在列表顶部

    // 列表数据
    serviceList: [], // 原始服务列表
    filteredList: [], // 筛选后的服务列表

    // 加载状态
    loading: false,
    refreshing: false,

    // 地图相关
    mapCenter: {
      latitude: 31.3041,  // 默认坐标，会在获取用户小区后更新
      longitude: 120.5954
    },
    mapScale: 14, // 使用mapScaleConfig.default的值（3公里范围）
    
    // 地图缩放级别配置
    // 说明：
    // - default: 默认缩放级别，决定了小区周边显示范围（14适配3公里）
    // - expanded: 列表展开时的最小缩放级别，显示更大范围
    // - collapsed: 列表收起时的最大缩放级别，显示更详细区域
    // - selected: 选中服务时的缩放级别，确保服务清晰可见
    // - zoomStep: 手动缩放和自动调整的步长
    // - minScale/maxScale: 缩放级别的边界限制
    // 
    // 缩放级别参考：
    // 5-8: 几十公里范围    9-12: 几公里到十几公里
    // 13-15: 1-3公里范围   16-18: 几百米到1公里
    // 19-20: 几十米到几百米
    mapScaleConfig: {
      default: 14,        // 默认缩放级别（3公里范围）
      expanded: 13,       // 列表展开时的最小缩放级别
      collapsed: 16,      // 列表收起时的最大缩放级别
      selected: 15,       // 选中服务时的缩放级别
      zoomStep: 2,        // 缩放步长
      minScale: 5,        // 最小缩放级别
      maxScale: 20        // 最大缩放级别
    },
    
    markers: [], // 地图标注
    selectedId: null, // 当前选中的服务ID

    // 服务覆盖范围圆形区域
    serviceCircles: [],
    
    // 小区中心点（会从用户小区信息获取）
    serviceCenterPoint: {
      latitude: 31.3041,
      longitude: 120.5954,
      name: '默认小区'
    },
    
    // 服务覆盖半径（公里）- 默认2公里
    serviceRadius: 2,

    // 是否显示服务覆盖范围 - 默认显示
    showServiceArea: true,

    // 地图实例
    mapContext: null,

    // 分类名称映射（从后端字典接口获取）
    categoryNames: {},

    // 用户小区信息
    userResidential: null,

    // 服务信息卡片相关
    showTransportCard: false, // 是否显示服务信息卡片

    // 服务覆盖范围统计
    servicesInArea: 0,
    categoriesInArea: {},

    // 推荐服务相关
    recommendedServices: [],
    popularServices: [],
    
    // 字典数据
    availableTags: [], // 可用标签列表
    auditStatusList: [], // 审核状态列表
  },

  // 地图缩放级别辅助方法
  getMapScale: function(type = 'default') {
    const config = this.data.mapScaleConfig
    switch (type) {
      case 'default':
        return config.default
      case 'expanded':
        return Math.max(this.data.mapScale - 1, config.expanded)
      case 'collapsed':
        return Math.min(this.data.mapScale + config.zoomStep, config.collapsed)
      case 'selected':
        return Math.max(this.data.mapScale, config.selected)
      case 'zoomIn':
        return Math.min(this.data.mapScale + config.zoomStep, config.maxScale)
      case 'zoomOut':
        return Math.max(this.data.mapScale - config.zoomStep, config.minScale)
      default:
        return config.default
    }
  },

    onLoad: function () {
        // 设置地图缩放级别为配置中的默认值
        this.setData({
          mapScale: this.data.mapScaleConfig.default
        })
        
        // 获取地图实例
        this.mapContext = wx.createMapContext('map', this)
        
        // 先加载分类数据，然后加载服务数据
        this.loadCategories().then(() => {
          // 获取用户小区信息
          this.getUserResidential()
          
          // 加载服务数据
          this.loadServiceData()
          
          // 加载推荐服务
          this.loadRecommendedServices()
        }).catch(error => {
          console.error('加载分类数据失败:', error)
          // 即使分类加载失败，也继续加载其他数据
          this.getUserResidential()
          this.loadServiceData()
          this.loadRecommendedServices()
        })
    },

  onShow: function () {
    // 页面显示时刷新数据（避免重复调用）
    // this.loadServiceData()
  },

  // 加载分类数据（使用统一的系统信息服务）
  loadCategories: async function() {
    try {
      // 使用统一的系统信息服务获取格式化的分类数据
      const categories = await systemInfoService.getFormattedCategories()
      const categoryNames = await systemInfoService.getCategoryNameMap()
      const availableTags = await systemInfoService.getCommunityServiceTags()
      const auditStatusList = await systemInfoService.getAuditStatusList()
      
      this.setData({
        categories: categories,
        categoryNames: categoryNames,
        availableTags: availableTags,
        auditStatusList: auditStatusList
      })
      
      return { categories, categoryNames, availableTags, auditStatusList }
    } catch (error) {
      console.error('加载分类数据失败:', error)
      
      // 设置默认的分类数据，确保页面能正常工作
      const defaultCategories = [
        { id: 'all', name: '全部' }
      ]
      
      const defaultCategoryNames = {}
      
      this.setData({
        categories: defaultCategories,
        categoryNames: defaultCategoryNames,
        availableTags: [],
        auditStatusList: []
      })
      
      // 显示错误提示，但不影响页面功能
      wx.showToast({
        title: '分类加载失败，请重试',
        icon: 'none',
        duration: 2000
      })
      
      throw error // 重新抛出错误，让调用者知道加载失败
    }
  },

  // 将标签的dictValue转换为dictLabel
  convertTagsToLabels: function(tags) {
    if (!tags || !Array.isArray(tags) || tags.length === 0) {
      return [];
    }
    
    return tags.map(tagValue => {
      // 首先尝试在标签数据中查找
      const tag = this.data.availableTags.find(item => item.dictValue === tagValue);
      if (tag) {
        return tag.dictLabel;
      }
      
      // 如果标签数据中没找到，尝试在分类数据中查找
      const categoryLabel = this.data.categoryNames[tagValue];
      if (categoryLabel) {
        return categoryLabel;
      }
      
      // 都没找到，返回原值
      return tagValue;
    });
  },

  // 加载服务数据
  loadServiceData: async function() {
    this.setData({ loading: true })

    try {
      // 获取当前小区ID
      const localResidentialService = require('../../services/localResidential')
      localResidentialService.init()
      const residentialId = localResidentialService.getCurrentResidentialId()

      // 调用API获取周边服务数据，传递小区参数
      const params = residentialId ? { residentialId } : {}
      const res = await api.getCommunityNearbyList(params)
      
      if (res.code === 200 && res.data) {
        let serviceList = res.data
        
        // 如果是分页数据结构，取rows
        if (res.rows) {
          serviceList = res.rows
        }

        // 使用统一的系统信息服务获取文件服务器URL
        const fileUrl = await systemInfoService.getFileUrl()
        
        // 处理数据格式转换
        serviceList = await Promise.all(serviceList.map(async item => {
          // 解析位置信息
          const residentialService = require('../../services/residential.js');
          const coords = residentialService.parseLocation(item.location);

          // 使用系统信息服务处理图片URL
          const imageResult = await systemInfoService.processImageUrls(item.images)
          
          return {
            id: item.id,
            name: item.name,
            address: item.address,
	          viewCount:item.viewCount,
            category: item.category,
            location: item.location, // 保留原始location字段
            latitude: coords ? coords.latitude : null,
            longitude: coords ? coords.longitude : null,
            ...this.processDistanceData(item), // 统一处理距离数据
            tags: item.tags ? this.convertTagsToLabels(item.tags.split(',')) : [],
            originalTags: item.tags ? item.tags.split(',') : [], // 保留原始标签值用于搜索
            images: imageResult.images,
            imageUrl: imageResult.imageUrl, // 第一张图片URL
            description: item.description || '',
            contactPhone: item.contactPhone || '',
            businessHours: item.businessHours || '',
            hasValidLocation: coords !== null && coords.latitude && coords.longitude
          }
        }))

        this.setData({
          serviceList: serviceList,
          loading: false
        })

        // 统计位置信息
        const totalCount = serviceList.length;
        const validLocationCount = serviceList.filter(item => item.hasValidLocation).length;
        
        // 应用筛选
        this.applyFilters()
        
        // 生成地图标注
        this.generateMarkers()
        
        // 更新服务覆盖范围统计
        this.updateServiceAreaStats()
        
      } else {
        throw new Error(res.msg || '获取数据失败')
      }
    } catch (error) {
      console.error('加载服务数据失败:', error)
      
      // 检查是否是未选择小区的错误
      if (error.message && error.message.includes('请先选择您所在的小区')) {
        wx.showModal({
          title: '提示',
          content: '使用社区服务需要先选择您所在的小区',
          confirmText: '去选择',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 跳转到小区选择页面
              wx.navigateTo({
                url: '/pages/residential/index'
              })
            }
          }
        })
      } else {
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      }
      
      this.setData({ loading: false })
    }
  },

  // 格式化时间 - 使用统一的工具方法
  formatTime: function(dateStr) {
    return util.formatTimeUnified(dateStr, { 
      type: 'relative', 
      suffix: '更新' 
    });
  },

  // 应用筛选条件
  applyFilters: function() {
    let filteredList = [...this.data.serviceList]
    
    // 分类筛选
    if (this.data.nearbyFilter !== 'all') {
      filteredList = filteredList.filter(item => item.category === this.data.nearbyFilter)
    }
    
    // 搜索筛选
    if (this.data.searchValue) {
      const keyword = this.data.searchValue.toLowerCase()
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.address.toLowerCase().includes(keyword) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(keyword))) ||
        (item.originalTags && item.originalTags.some(tag => tag.toLowerCase().includes(keyword)))
      )
    }
    
    // 排序
    if (this.data.sortByDistance) {
      filteredList.sort((a, b) => a.distanceValue - b.distanceValue)
    }

    this.setData({
      filteredList: filteredList
    })
    
    // 重新生成地图标注
    this.generateMarkers()
  },

  // 生成地图标注
  generateMarkers: function() {
    const markers = this.data.filteredList.map((item, index) => {
      const isSelected = Number(item.id) === Number(this.data.selectedId)
      
      // 检查是否有有效的经纬度
      if (!item.latitude || !item.longitude || !item.hasValidLocation) {
        console.warn('跳过无效位置的项目:', {
          id: item.id,
          name: item.name,
          location: item.location
        });
        return null;
      }
      
      return {
        id: Number(item.id),
        latitude: item.latitude,
        longitude: item.longitude,
        iconPath: isSelected ? '/static/images/icons/map-marker-selected.png' : '/static/images/icons/map-marker.png',
        width: isSelected ? 22 : 22,
        height: isSelected ? 26 : 20,
        anchor: {
          x: 0.5,
          y: 1
        },
        callout: {
          content: item.name,
          fontSize: isSelected ? 12 : 10,
          borderRadius: isSelected ? 5 : 4,
          padding: isSelected ? 5 : 4,
          display: 'ALWAYS',
          textAlign: 'center',
          borderWidth: 1,
          borderColor: isSelected ? 'rgba(59, 127, 255, 1)' : 'rgba(0, 0, 0, 0.2)',
          anchorY: 0,
          anchorX: 0
        }
      }
    }).filter(marker => marker !== null);

    // 添加小区中心点
    const centerMarker = {
      id: 0,
      latitude: this.data.serviceCenterPoint.latitude,
      longitude: this.data.serviceCenterPoint.longitude,
      iconPath: '/static/images/icons/home-marker.png',
      width: 20,
      height: 20,
      anchor: {
        x: 0.5,
        y: 0.5
      },
      callout: {
        content: '🏠 我的小区',
        fontSize: 10,
        borderRadius: 4,
        padding: 4,
        display: 'ALWAYS',
        textAlign: 'center',
        borderWidth: 1,
        borderColor: 'rgba(255, 107, 53, 1)',
        anchorY: 0,
        anchorX: 0
      }
    }

    markers.push(centerMarker)

    this.setData({ markers })
  },

  // 获取标注图标
  getMarkerIcon: function(category, isSelected) {
    const iconMap = {
      business: isSelected ? '/static/img/marker-business-active.png' : '/static/img/marker-business.png',
      entertainment: isSelected ? '/static/img/marker-entertainment-active.png' : '/static/img/marker-entertainment.png',
      facility: isSelected ? '/static/img/marker-facility-active.png' : '/static/img/marker-facility.png'
    }
    return iconMap[category] || '/static/img/marker-default.png'
  },

  // 分类筛选变化
  onNearbyFilterChange: function (event) {
    const filter = event.currentTarget.dataset.filter
    this.setData({
      nearbyFilter: filter,
      selectedId: null
    })
    this.applyFilters()
  },

  // 搜索变化
  onSearchChange: function (event) {
    this.setData({
      searchValue: event.detail
    })
    // 直接应用筛选，无需防抖
    this.applyFilters()
  },

  // 切换排序方式
  toggleSort: function() {
    this.setData({
      sortByDistance: !this.data.sortByDistance
    })
    this.applyFilters()
  },

  // 地图标注点击
  onMarkerTap: function(event) {
    const residentialService = require('../../services/residential.js');
    const markerId = event.detail.markerId
    
    // 如果点击的是小区中心点，不做处理
    if (markerId === 0) {
      return
    }
    
    const item = this.data.filteredList.find(item => Number(item.id) === markerId)
    
    if (item) {
      // 解析位置信息
      const coords = residentialService.parseLocation(item.location);
      if (!coords) {
        console.warn('无法解析位置信息:', item.location);
        return;
      }
      
      const { longitude, latitude } = coords;
      
      // 设置选中的服务信息卡片（完全复制列表项点击的逻辑）
      this.setData({
        selectedId: item.id,
        selectedService: {
          ...item,
          ...this.processDistanceData(item), // 统一处理距离数据
          visitorCount: item.visitorCount || Math.floor(Math.random() * 50) + 20,
          businessHours: item.isOpen ? '07:00-21:30' : '已打烊'
        },
        showTransportCard: true,
        showDetail: false,
        listHeight: 0, // 隐藏列表
        listCollapsed: true,
        showExpandHint: false,
        mapCenter: {
          latitude,
          longitude
        },
        mapScale: this.getMapScale('selected')
      })
      
      // 重新生成标注
      this.generateMarkers()

      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      })
    }
  },

  // 服务项点击
  onServiceItemTap: function(event) {
    const residentialService = require('../../services/residential.js');
    const item = event.currentTarget.dataset.item
    
    // 解析位置信息
    const coords = residentialService.parseLocation(item.location);
    if (!coords) {
      console.warn('无法解析位置信息:', item.location);
      return;
    }
    
    const { longitude, latitude } = coords;
    
    // 设置选中的服务信息卡片
    this.setData({
      selectedId: item.id,
      selectedService: {
        ...item,
        ...this.processDistanceData(item), // 统一处理距离数据
        visitorCount: item.visitorCount || Math.floor(Math.random() * 50) + 20,
        businessHours: item.isOpen ? '08:00-17:00' : '已打烊'
      },
      showTransportCard: true,
      showDetail: false,
      listHeight: 0, // 隐藏列表
      listCollapsed: true,
      showExpandHint: false,
      mapCenter: {
        latitude,
        longitude
      },
      mapScale: this.getMapScale('selected')
    })
    
    // 重新生成标注
    this.generateMarkers()

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    })
  },

  // 拨打服务电话
  onCallService: function (event) {
    const item = event.currentTarget.dataset.item
    const phoneNumber = item.phone || '138-0000-0000'

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    })

    this.ensureLogin(() => {
      wx.showModal({
        title: '拨打电话',
        content: `确定要拨打 ${item.name} 的电话 ${phoneNumber} 吗？`,
        confirmText: '拨打',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: phoneNumber,
              success: () => {
                // 记录用户行为
                this.recordUserAction('call', item)
              },
              fail: (err) => {
                console.error('拨打电话失败:', err)
                wx.showToast({
                  title: '拨打失败，请手动拨号',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    })
  },

  // 导航到服务位置
  onNavigateToService: function (event) {
    const residentialService = require('../../services/residential.js');
    const item = event.currentTarget.dataset.item

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    })

    this.ensureLogin(() => {
      // 解析位置信息
      const coords = residentialService.parseLocation(item.location);
      if (!coords) {
        wx.showToast({
          title: '位置信息无效',
          icon: 'none'
        });
        return;
      }
      
      const { longitude, latitude } = coords;

      wx.openLocation({
        latitude,
        longitude,
        name: item.name,
        address: item.address,
        scale: 18,
        success: () => {
          // 记录用户行为
          this.recordUserAction('navigate', item)
        }
      })
    })
  },

  // 服务基本信息区域点击 - 跳转到详情页面
  onServiceBasicInfoTap: function(event) {
    const item = event.currentTarget.dataset.item

    if (!item || !item.id) {
      wx.showToast({
        title: '服务信息无效',
        icon: 'none'
      })
      return
    }

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    })

    // 检查登录状态，查看详情需要登录
    this.ensureLogin(() => {
      // 登录成功后跳转到服务详情页面
      wx.navigateTo({
        url: `/pages/community-service-detail/index?id=${item.id}`,
        success: () => {
          // 记录用户行为
          this.recordUserAction('view_detail', item)
        },
        fail: (error) => {
          console.error('跳转到服务详情页面失败:', error)
          wx.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          })
        }
      })
    })
  },

  // 定位按钮点击 - 回到小区中心位置
  onLocationTap: function() {
    const centerPoint = this.data.serviceCenterPoint
    
    // 计算向上偏移，地图中心向下移动，让小区标记在屏幕上显示得更靠上
    const mapScale = this.data.mapScaleConfig.default // 使用默认缩放级别
    const offsetFactor = Math.max(0.008, 0.015 / Math.pow(mapScale / 10, 1.0))
    const adjustedLatitude = centerPoint.latitude - offsetFactor // 使用减法
    
    // 直接设置地图中心点并重置状态到初始状态
    this.setData({
      mapCenter: {
        latitude: adjustedLatitude,
        longitude: centerPoint.longitude // 保持原始经度，确保左右居中
      },
      mapScale: this.getMapScale('default'),
      // 重置所有状态到初始状态
      selectedId: null,
      showTransportCard: false,
      selectedService: null,
      showDetail: false,
      listExpanded: false,
      listCollapsed: false,
      showExpandHint: false,
      listHeight: 280,
      nearbyFilter: 'all',
      searchValue: ''
    })
    
    // 重新应用筛选和生成标记
    this.applyFilters()
    this.generateMarkers()
  },

  // 地图区域变化
  onRegionChange: function(event) {
    if (event.type === 'end') {
      // 可以在这里处理地图移动后的逻辑
      }
  },

  // 计算两点间距离（公里）
  calculateDistance: function(lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0
    const radLat2 = lat2 * Math.PI / 180.0
    const a = radLat1 - radLat2
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
      Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)))
    s = s * 6378.137 // 地球半径
    return s
  },

  // 添加按钮点击事件
  onAddButtonTap: function () {
    // 检查小区认证状态
    userUtils.ensureResidentialAuth(this, () => {
      // 小区认证通过，跳转到发布页面
      wx.navigateTo({
        url: '/pages/community-nearby-publish/index'
      })
    })
  },

  // 查看我的提交历史
  onMySubmissionsButtonTap: function () {
    // 检查登录状态，查看提交记录需要登录
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/community-my-submissions/index'
      })
    })
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '社区服务 - 便民地图',
      path: '/pages/community-service/index',
    }
  },

  onShareTimeline: function () {
    return {
      title: '社区服务 - 便民地图',
    }
  },

  // 页面卸载时清理定时器
  onUnload: function () {
    if (this.mapTapTimer) {
      clearTimeout(this.mapTapTimer)
    }
  },

  // 搜索确认
  onSearch: function(event) {
    this.setData({
      searchValue: event.detail.value
    })
    this.applyFilters()
    
    // 记录搜索历史
    this.addSearchHistory(event.detail.value)
  },

  // 添加搜索历史
  addSearchHistory: function(keyword) {
    if (!keyword || keyword.trim() === '') return
    
    try {
      let history = wx.getStorageSync('searchHistory') || []
      const trimmedKeyword = keyword.trim()
      
      // 移除重复项
      history = history.filter(item => item !== trimmedKeyword)
      
      // 添加到开头
      history.unshift(trimmedKeyword)
      
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }
      
      wx.setStorageSync('searchHistory', history)
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  // 切换列表模式
  toggleListMode: function() {
    this.setData({
      showListMode: !this.data.showListMode
    })
  },

  // 地图缩放
  zoomIn: function() {
    const newScale = this.getMapScale('zoomIn')
    this.setData({
      mapScale: newScale
    })
  },

  zoomOut: function() {
    const newScale = this.getMapScale('zoomOut')
    this.setData({
      mapScale: newScale
    })
  },

  // 刷新数据
  refreshData: function() {
    if (this.data.refreshing) return
    
    this.setData({ 
      refreshing: true,
      selectedId: null
    })
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    })
    
    // 模拟刷新延迟
    setTimeout(() => {
      this.loadServiceData()
      this.setData({ refreshing: false })
      
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  // 记录用户行为
  recordUserAction: function(action, item) {
    try {
      const actionData = {
        action: action,
        serviceId: item.id,
        serviceName: item.name,
        category: item.category,
        timestamp: new Date().getTime()
      }
      
      // 发送到后端统计
      if (action === 'call') {
        api.recordCall(item.id).then(res => {
          }).catch(error => {
          console.error('记录拨打电话行为失败:', error)
        })
      } else if (action === 'navigate') {
        api.recordNavigate(item.id).then(res => {
          }).catch(error => {
          console.error('记录导航行为失败:', error)
        })
      }
      
      // 本地存储最近访问（用于离线展示）
      let recentActions = wx.getStorageSync('recentActions') || []
      recentActions.unshift(actionData)
      
      // 限制记录数量
      if (recentActions.length > 50) {
        recentActions = recentActions.slice(0, 50)
      }
      
      wx.setStorageSync('recentActions', recentActions)
    } catch (error) {
      console.error('记录用户行为失败:', error)
    }
  },

  // 搜索获得焦点
  onSearchFocus: function() {
    // 可以在这里处理搜索获得焦点的逻辑
  },

  // 搜索失去焦点
  onSearchBlur: function() {
    // 可以在这里处理搜索失去焦点的逻辑
  },

  // 清除所有筛选
  clearAllFilters: function() {
    this.setData({
      nearbyFilter: 'all',
      searchValue: '',
      selectedId: null
    })
    
    // 重新应用筛选
    this.applyFilters()
    
    wx.showToast({
      title: '已重置筛选条件',
      icon: 'success',
      duration: 1500
    })
  },

  // 拖拽开始
  onDragStart: function(event) {
    const touch = event.touches[0]
    const currentTime = Date.now()
    
    this.setData({
      dragStartY: touch.clientY,
      dragStartHeight: this.data.listHeight,
      isDragging: true
    })
    
    // 记录拖拽开始信息
    this.dragStartTime = currentTime
    this.lastTouchY = touch.clientY
    this.lastTouchTime = currentTime
    this.dragVelocity = 0
  },

  // 拖拽移动
  onDragMove: function(event) {
    if (!this.data.isDragging) return
    
    const touch = event.touches[0]
    const currentTime = Date.now()
    const deltaY = this.data.dragStartY - touch.clientY
    const windowHeight = util.getWindowInfo().windowHeight
    
    // 计算拖拽速度
    if (this.lastTouchTime && this.lastTouchY) {
      const timeDiff = currentTime - this.lastTouchTime
      const yDiff = touch.clientY - this.lastTouchY
      this.dragVelocity = timeDiff > 0 ? yDiff / timeDiff : 0
    }
    
    this.lastTouchY = touch.clientY
    this.lastTouchTime = currentTime
    
    // 计算新高度
    let newHeight = this.data.dragStartHeight + deltaY
    
    // 定义三个状态的高度
    const COLLAPSED_HEIGHT = 80
    const DEFAULT_HEIGHT = 280
    const EXPANDED_HEIGHT = windowHeight * 0.7
    
    // 添加阻尼效果
    const MIN_HEIGHT = 40
    const MAX_HEIGHT = windowHeight * 0.85
    
    if (newHeight < MIN_HEIGHT) {
      const overflow = MIN_HEIGHT - newHeight
      newHeight = MIN_HEIGHT - overflow * 0.2
    } else if (newHeight > MAX_HEIGHT) {
      const overflow = newHeight - MAX_HEIGHT
      newHeight = MAX_HEIGHT + overflow * 0.2
    }
    
    newHeight = Math.max(30, Math.min(newHeight, windowHeight * 0.9))
    
    // 更新状态
    this.setData({
      listHeight: newHeight,
      scrollDirection: deltaY > 0 ? 'up' : 'down',
      listExpanded: newHeight > EXPANDED_HEIGHT * 0.8,
      listCollapsed: newHeight < COLLAPSED_HEIGHT * 1.5,
      showExpandHint: newHeight < COLLAPSED_HEIGHT * 1.5
    })
  },

  // 拖拽结束 - 重构为三段式状态
  onDragEnd: function(event) {
    if (!this.data.isDragging) return
    
    const currentHeight = this.data.listHeight
    const windowHeight = util.getWindowInfo().windowHeight
    const dragStartHeight = this.data.dragStartHeight
    const dragDistance = currentHeight - dragStartHeight
    const velocity = this.dragVelocity || 0
    
    // 定义三个状态
    const COLLAPSED_HEIGHT = 80
    const DEFAULT_HEIGHT = 280
    const EXPANDED_HEIGHT = windowHeight * 0.7
    
    let targetHeight = DEFAULT_HEIGHT
    let expanded = false
    let collapsed = false
    let showHint = false
    let newMapScale = this.getMapScale('default')
    
    // 速度阈值
    const VELOCITY_THRESHOLD = 0.8
    
    // 优先根据速度判断
    if (Math.abs(velocity) > VELOCITY_THRESHOLD) {
      if (velocity < -VELOCITY_THRESHOLD) {
        // 快速向上滑动 → 展开
        targetHeight = EXPANDED_HEIGHT
        expanded = true
        newMapScale = this.getMapScale('expanded')
      } else if (velocity > VELOCITY_THRESHOLD) {
        // 快速向下滑动 → 根据当前状态决定
        if (dragStartHeight >= EXPANDED_HEIGHT * 0.8) {
          // 从展开状态快速向下 → 回到默认
          targetHeight = DEFAULT_HEIGHT
        } else if (dragStartHeight >= DEFAULT_HEIGHT * 0.8) {
          // 从默认状态快速向下 → 收起
          targetHeight = COLLAPSED_HEIGHT
          collapsed = true
          showHint = true
          newMapScale = this.getMapScale('collapsed')
        } else {
          // 从收起状态快速向下 → 保持收起
          targetHeight = COLLAPSED_HEIGHT
          collapsed = true
          showHint = true
          newMapScale = this.getMapScale('collapsed')
        }
      }
    } else {
      // 根据拖拽距离和当前高度判断
      const upThreshold = 60
      const downThreshold = -60
      
      if (dragDistance > upThreshold) {
        // 向上拖拽足够距离
        if (dragStartHeight < DEFAULT_HEIGHT * 0.8) {
          // 从收起/较低状态 → 默认
          targetHeight = DEFAULT_HEIGHT
        } else {
          // 从默认/较高状态 → 展开
          targetHeight = EXPANDED_HEIGHT
          expanded = true
          newMapScale = this.getMapScale('expanded')
        }
      } else if (dragDistance < downThreshold) {
        // 向下拖拽足够距离
        if (dragStartHeight > EXPANDED_HEIGHT * 0.8) {
          // 从展开状态 → 默认
          targetHeight = DEFAULT_HEIGHT
        } else if (dragStartHeight > DEFAULT_HEIGHT * 0.8) {
          // 从默认状态 → 收起
          targetHeight = COLLAPSED_HEIGHT
          collapsed = true
          showHint = true
          newMapScale = this.getMapScale('collapsed')
        } else {
          // 从收起状态 → 保持收起
          targetHeight = COLLAPSED_HEIGHT
          collapsed = true
          showHint = true
          newMapScale = this.getMapScale('collapsed')
        }
      } else {
        // 拖拽距离不够，回到最近的稳定状态
        const distanceToCollapsed = Math.abs(currentHeight - COLLAPSED_HEIGHT)
        const distanceToDefault = Math.abs(currentHeight - DEFAULT_HEIGHT)
        const distanceToExpanded = Math.abs(currentHeight - EXPANDED_HEIGHT)
        
        if (distanceToCollapsed < distanceToDefault && distanceToCollapsed < distanceToExpanded) {
          targetHeight = COLLAPSED_HEIGHT
          collapsed = true
          showHint = true
          newMapScale = this.getMapScale('collapsed')
        } else if (distanceToExpanded < distanceToDefault) {
          targetHeight = EXPANDED_HEIGHT
          expanded = true
          newMapScale = this.getMapScale('expanded')
        } else {
          targetHeight = DEFAULT_HEIGHT
        }
      }
    }
    
    this.setData({
      listHeight: targetHeight,
      listExpanded: expanded,
      listCollapsed: collapsed,
      showExpandHint: showHint,
      isDragging: false,
      scrollDirection: 'none',
      mapScale: newMapScale
    })
    
    // 清理
    this.dragVelocity = 0
    this.lastTouchY = null
    this.lastTouchTime = null
    this.dragStartTime = null
  },

  // 重构列表滚动事件
  onListScroll: function(event) {
    if (this.data.isDragging || this.data.showDetail) return
    
    const scrollTop = event.detail.scrollTop
    const direction = scrollTop > this.data.lastScrollTop ? 'down' : 'up'
    const windowHeight = util.getWindowInfo().windowHeight
    
    // 检查内容是否足够
    const listItemCount = this.data.filteredList.length
    const estimatedContentHeight = listItemCount * 80 + 100
    const currentListHeight = this.data.listHeight
    
    // 内容不足时不处理滚动展开
    if (estimatedContentHeight < currentListHeight * 0.8) {
      this.setData({
        lastScrollTop: scrollTop,
        scrollDirection: direction
      })
      return
    }
    
    // 定义状态高度
    const DEFAULT_HEIGHT = 280
    const EXPANDED_HEIGHT = windowHeight * 0.7
    
    if (scrollTop === 0) {
      // 滚动到顶部
      if (!this.data.isAtTop) {
        this.setData({ isAtTop: true })
      }
    } else {
      // 离开顶部
      if (this.data.isAtTop) {
        this.setData({ isAtTop: false })
      }
      
      // 向上滚动时自动展开（仅在默认状态且内容足够时）
      if (direction === 'up' && scrollTop > 30 && 
          !this.data.listExpanded && !this.data.listCollapsed &&
          this.data.listHeight <= DEFAULT_HEIGHT * 1.1) {
        this.setData({
          listHeight: EXPANDED_HEIGHT,
          listExpanded: true,
          mapScale: this.getMapScale('expanded')
        })
      }
    }
    
    this.setData({
      lastScrollTop: scrollTop,
      scrollDirection: direction
    })
  },

  // 优化列表触摸事件 - 实现顶部下拉收起
  onListTouchStart: function(event) {
    if (this.data.isDragging || this.data.showDetail) return
    
    this.listTouchStartY = event.touches[0].clientY
    this.listTouchStartTime = Date.now()
    this.listTouchStartScrollTop = this.data.lastScrollTop || 0
  },

  onListTouchMove: function(event) {
    if (this.data.isDragging || this.data.showDetail || !this.listTouchStartY) return
    
    const currentY = event.touches[0].clientY
    const deltaY = currentY - this.listTouchStartY
    const currentTime = Date.now()
    
    // 只在顶部且向下滑动时处理
    if (this.data.isAtTop && deltaY > 0) {
      const timeDiff = currentTime - this.listTouchStartTime
      
      // 检测向下滑动手势
      if (deltaY > 20 && timeDiff < 800) {
        if (this.data.listExpanded) {
          // 从展开状态 → 默认状态
          this.setListState('default', { vibrate: true })
        } else if (this.data.listHeight >= 280 * 0.8 && deltaY > 40) {
          // 从默认状态继续向下 → 收起状态
          this.setListState('collapsed', { vibrate: true })
          this.setData({ isAtTop: false })
        }
        
        // 重置触摸状态
        this.listTouchStartY = null
        this.listTouchStartTime = null
      }
    }
  },

  onListTouchEnd: function(event) {
    this.listTouchStartY = null
    this.listTouchStartTime = null
    this.listTouchStartScrollTop = null
  },

  // 统一的列表状态切换方法
  setListState: function(state, options = {}) {
    const windowHeight = util.getWindowInfo().windowHeight
    const { animated = true, vibrate = false } = options
    
    let targetHeight, expanded, collapsed, showHint, mapScale
    
    switch (state) {
      case 'collapsed':
        targetHeight = 80
        expanded = false
        collapsed = true
        showHint = true
        mapScale = this.getMapScale('collapsed')
        break
      case 'default':
        targetHeight = 280
        expanded = false
        collapsed = false
        showHint = false
        mapScale = this.getMapScale('default')
        break
      case 'expanded':
        targetHeight = windowHeight * 0.7
        expanded = true
        collapsed = false
        showHint = false
        mapScale = this.getMapScale('expanded')
        break
      default:
        console.warn('未知的列表状态:', state)
        return
    }
    
    this.setData({
      listHeight: targetHeight,
      listExpanded: expanded,
      listCollapsed: collapsed,
      showExpandHint: showHint,
      mapScale: mapScale
    })
    
    if (vibrate) {
      wx.vibrateShort({ type: 'light' })
    }
    
    },

  // 展开列表（点击查看全部结果）
  expandList: function() {
    this.setListState('expanded', { vibrate: true })
  },

  // 收起列表
  collapseList: function() {
    this.setListState('collapsed', { vibrate: true })
  },

  // 关闭详情
  closeDetail: function() {
    this.setData({
      showDetail: false,
      selectedService: null,
      selectedId: null,
      listExpanded: false,
      listCollapsed: false,
      showExpandHint: false,
      listHeight: 280,
      mapScale: this.getMapScale('default') // 回到默认缩放级别
    })
  },

  // 点击地图或其他区域时关闭详情和收起列表
  onMapTap: function() {
    // 防止快速重复点击
    if (this.mapTapTimer) {
      clearTimeout(this.mapTapTimer)
    }
    
    this.mapTapTimer = setTimeout(() => {
      if (this.data.showTransportCard) {
        // 如果显示服务信息卡片，关闭卡片并显示列表
        this.setData({
          showTransportCard: false,
          selectedService: null,
          selectedId: null,
          listHeight: 280, // 恢复列表显示
          listCollapsed: false,
          listExpanded: false,
          showExpandHint: false,
          mapScale: this.getMapScale('default') // 回到默认缩放级别
        })
        
        // 重新生成标注（移除选中状态）
        this.generateMarkers()
      } else if (this.data.showDetail) {
        // 如果显示详情，关闭详情回到默认状态
        this.setData({
          showDetail: false,
          selectedService: null,
          selectedId: null,
          listExpanded: false,
          listCollapsed: false,
          showExpandHint: false,
          listHeight: 280,
          mapScale: this.getMapScale('default') // 回到默认缩放级别
        })
        
        // 重新生成标注（移除选中状态）
        this.generateMarkers()
      } else if (this.data.listExpanded) {
        // 如果列表展开，收起到默认状态
        this.setData({
          listHeight: 280,
          listExpanded: false,
          listCollapsed: false,
          showExpandHint: false,
          mapScale: this.getMapScale('default') // 回到默认缩放级别
        })
      }
    }, 100) // 100ms防抖
  },

  // 关闭服务信息卡片
  closeTransportCard: function() {
    this.setData({
      showTransportCard: false,
      selectedId: null,
      selectedService: null,
      listHeight: 280, // 恢复列表显示
      listCollapsed: false,
      listExpanded: false,
      showExpandHint: false,
      mapScale: this.getMapScale('default') // 回到默认缩放级别
    });
    
    // 重新生成标注（移除选中状态）
    this.generateMarkers();
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 初始化服务覆盖区域
  initServiceArea: function() {
    if (!this.data.showServiceArea) {
      this.setData({ serviceCircles: [] })
      return
    }

    const circles = [{
      latitude: this.data.serviceCenterPoint.latitude,
      longitude: this.data.serviceCenterPoint.longitude,
      radius: this.data.serviceRadius * 1000, // 转换为米
      color: '#1890FF08', // 专业蓝色透明填充
      fillColor: '#1890FF15', // 稍微深一点的蓝色填充
      strokeWidth: 2, // 边框宽度
      strokeColor: '#1890FF', // 蓝色边框
      level: 'aboveroads' // 显示层级
    }]
    
    this.setData({
      serviceCircles: circles
    })
    
    },

  // 更新服务覆盖范围统计
  updateServiceAreaStats: function() {
    if (!this.data.serviceCenterPoint || !this.data.serviceList.length) return
    
    const centerLat = this.data.serviceCenterPoint.latitude
    const centerLng = this.data.serviceCenterPoint.longitude
    const radius = this.data.serviceRadius
    
    let servicesInArea = 0
    const categoriesInArea = {}
    
    this.data.serviceList.forEach(service => {
      const distance = this.calculateDistance(
        centerLat, centerLng,
        service.latitude, service.longitude
      )
      
      if (distance <= radius) {
        servicesInArea++
        categoriesInArea[service.category] = (categoriesInArea[service.category] || 0) + 1
      }
    })
    
    this.setData({
      servicesInArea: servicesInArea,
      categoriesInArea: categoriesInArea
    })
    
    },

  // 获取用户小区信息
  getUserResidential: function() {
    // 调用后端接口获取用户当前小区信息
    // 小区参数会通过请求拦截器自动添加
    api.getCurrentUserResidential().then(res => {
      if (res.code === 200 && res.data) {
        // 设置用户小区信息
        this.setData({
          userResidential: res.data
        })
        
        // 根据小区信息更新地图中心点
        this.updateMapCenterFromResidential(res.data)
        
        // 初始化服务覆盖区域
        this.initServiceArea()
      } else {
        console.warn('获取用户小区信息失败:', res.msg || '未知错误')
        // 使用默认配置
        this.initServiceArea()
      }
    }).catch(error => {
      console.error('获取用户小区信息失败:', error)
      // 网络错误或其他异常，使用默认配置
      this.initServiceArea()
    })
  },

  // 根据小区信息更新地图中心点
  updateMapCenterFromResidential: function(residential) {
    const residentialService = require('../../services/residential.js');
    if (residential && residential.location) {
      // 使用统一的位置解析方法
      const coords = residentialService.parseLocation(residential.location);
      if (coords) {
        const { longitude, latitude } = coords;
        // 验证坐标有效性
        if (latitude && longitude && 
            latitude >= -90 && latitude <= 90 && 
            longitude >= -180 && longitude <= 180) {
          
          // 计算向上偏移，让小区中心在可视区域中更靠上
          // 地图中心向下移动，让小区标记在屏幕上显示得更靠上
          const mapScale = this.data.mapScale || this.data.mapScaleConfig.default
          // 大幅增加偏移量，让小区中心点在屏幕上更靠上
          const offsetFactor = Math.max(0.008, 0.015 / Math.pow(mapScale / 10, 1.0))
          const adjustedLatitude = latitude - offsetFactor // 使用减法
          
          this.setData({
            mapCenter: { 
              latitude: adjustedLatitude,
              longitude: longitude // 保持原始经度，确保左右居中
            },
            serviceCenterPoint: {
              latitude: latitude, // 保持原始坐标用于标记和计算
              longitude: longitude,
              name: residential.name || '当前小区'
            }
          })
          
          } else {
          console.error('坐标无效，使用默认坐标:', { latitude, longitude })
          // 使用默认坐标
          this.useDefaultLocation(residential.name)
        }
      } else {
        console.warn('位置信息解析失败，使用默认坐标:', residential.location)
        this.useDefaultLocation(residential.name)
      }
    } else {
      console.warn('小区没有坐标信息，使用默认坐标')
      this.useDefaultLocation(residential?.name)
    }
  },

  // 使用默认位置
  useDefaultLocation: function(residentialName) {
    const defaultLat = 31.3041  // 默认苏州坐标
    const defaultLng = 120.5954
    
    // 计算向上偏移，与初始化保持一致
    const mapScale = this.data.mapScale || this.data.mapScaleConfig.default
    const offsetFactor = Math.max(0.008, 0.015 / Math.pow(mapScale / 10, 1.0))
    const adjustedLatitude = defaultLat - offsetFactor // 使用减法
    
    this.setData({
      mapCenter: { 
        latitude: adjustedLatitude,
        longitude: defaultLng // 保持原始经度，确保左右居中
      },
      serviceCenterPoint: {
        latitude: defaultLat, // 保持原始坐标用于标记和计算
        longitude: defaultLng,
        name: residentialName || '默认小区'
      }
    })
    },

  // 加载推荐服务
  loadRecommendedServices: function() {
    api.getRecommendedNearby(5).then(res => {
      if (res.code === 200 && res.data) {
        // 可以在界面上展示推荐服务，这里先不实现UI
        this.setData({
          recommendedServices: res.data
        })
      }
    }).catch(error => {
      console.error('获取推荐服务失败:', error)
    })
  },

  // 获取热门服务
  loadPopularServices: function(category = null) {
    api.getPopularNearby({ 
      category: category,
      limit: 10 
    }).then(res => {
      if (res.code === 200 && res.data) {
        // 可以在界面上展示热门服务
        this.setData({
          popularServices: res.data
        })
      }
    }).catch(error => {
      console.error('获取热门服务失败:', error)
    })
  },

  // 统一处理距离数据
  processDistanceData: function(item) {
    const distance = item.distance || 0
    return {
      distance: distance, // 原始距离值（米）
      distanceValue: distance, // 用于排序的数值
      distanceText: this.formatDistance(distance) // 格式化显示文本
    }
  },

  // 格式化距离
  formatDistance: function(distance) {
    if (distance < 1000) {
      return '距离小区' + Math.round(distance) + '米'
    } else {
      return '距离小区' + (distance / 1000).toFixed(2) + '公里'
    }
  },

  // 图片加载错误处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;

    // 获取当前服务列表
    const filteredList = this.data.filteredList;
    if (filteredList[index]) {
      // 更新当前服务的图片为空，让模板使用默认图片
      const updatedList = [...filteredList];
      updatedList[index] = {
        ...updatedList[index],
        imageUrl: '', // 清空图片URL，让模板使用默认图片
        images: ''    // 同时清空images字段
      };

      // 更新数据，触发视图更新
      this.setData({
        filteredList: updatedList
      });
    }
  },

  // ========== 系统信息管理方法 ==========

  // 强制刷新系统信息
  forceRefreshSystemInfo: async function() {
    wx.showToast({
      title: '正在刷新配置...',
      icon: 'loading',
      duration: 2000
    })
    
    try {
      // 使用系统信息服务强制刷新
      await systemInfoService.forceRefresh()
      
      // 重新加载分类数据
      await this.loadCategories()
      
      wx.showToast({
        title: '配置已更新',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('刷新系统信息失败:', error)
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 显示系统信息缓存状态（调试用）
  showCacheStatus: function() {
    const status = systemInfoService.getCacheStatus()
    
    let content = `缓存状态: ${status.message}\n`
    if (status.hasCache) {
      content += `缓存时间: ${status.cacheTime}\n`
      content += `缓存年龄: ${status.cacheAge}分钟\n`
      content += `最大年龄: ${status.maxAge}分钟`
      if (status.isDefault) {
        content += '\n⚠️ 当前使用默认配置'
      }
    }
    
    wx.showModal({
      title: '系统信息缓存状态',
      content: content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: status.hasCache ? '强制刷新' : '确定',
      success: (res) => {
        if (res.confirm && status.hasCache) {
          this.forceRefreshSystemInfo()
        }
      }
    })
  },

  // 显示登录组件
  showLoginComponent() {
    userUtils.showLoginComponent(this, true)
  },

  // 隐藏登录组件
  hideLoginComponent() {
    userUtils.hideLoginComponent(this)
  },

  // 确保登录并执行回调的辅助方法
  ensureLogin(callback) {
    return userUtils.ensureLogin(this, callback)
  },

  // 登录成功回调
  onLoginSuccess() {
    console.log('周边推荐页面：用户登录成功')
    // 登录成功后可以进行一些操作，比如刷新数据等
    // 这里暂时不需要特殊处理
  },

  // 列表容器触摸移动事件处理
  onListContainerTouchMove: function(e) {
    // 阻止事件冒泡，防止触发地图的触摸事件
    // 这个方法主要用于阻止事件传播，不需要特殊处理
    return false
  },

  // 获取按钮点击事件
  onGetServiceTap: function(event) {
    const residentialService = require('../../services/residential.js');
    const item = event.currentTarget.dataset.item
    
    // 解析位置信息
    const coords = residentialService.parseLocation(item.location);
    if (!coords) {
      console.warn('无法解析位置信息:', item.location);
      return;
    }
    
    const { longitude, latitude } = coords;
    
    // 设置选中的服务信息卡片
    this.setData({
      selectedId: item.id,
      selectedService: {
        ...item,
        ...this.processDistanceData(item), // 统一处理距离数据
        visitorCount: item.visitorCount || Math.floor(Math.random() * 50) + 20,
        businessHours: item.isOpen ? '07:00-21:30' : '已打烊'
      },
      showTransportCard: true,
      showDetail: false,
      listHeight: 0, // 隐藏列表
      listCollapsed: true,
      showExpandHint: false,
      mapCenter: {
        latitude,
        longitude
      },
      mapScale: this.getMapScale('selected')
    })
    
    // 重新生成标注
    this.generateMarkers()

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    })
  }
})
