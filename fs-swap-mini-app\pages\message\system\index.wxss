/* 系统消息页面样式 */
page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #3B7FFF;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  --border-radius: 16rpx;
  --transition: all 0.3s ease;
  --border-color: #eeeeee;
}

.system-message-container {
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* 消息列表 */
.message-list {
  flex: 1;
  background: var(--background-color);
  padding-top: 24rpx;
}

.message-items {
  background: var(--primary-color);
  margin: 0 24rpx;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: var(--transition);
  position: relative;
  background: var(--primary-color);
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:active {
  background-color: var(--secondary-color);
}

.message-item.unread {
  background-color: #fafbff;
}

.message-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 60rpx;
  background: var(--accent-color);
  border-radius: 3rpx;
}

/* 消息图标 */
.message-icon {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--secondary-color);
  border: 2rpx solid var(--primary-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid var(--primary-color);
}

/* 消息内容 */
.message-content {
  flex: 1;
  min-width: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.message-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: var(--text-light);
  white-space: nowrap;
}

.message-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 消息操作 */
.message-action {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.action-text {
  font-size: 24rpx;
  color: var(--accent-color);
  padding: 8rpx 16rpx;
  border: 1rpx solid var(--accent-color);
  border-radius: 20rpx;
  font-weight: 500;
  transition: var(--transition);
}

.action-text:active {
  background-color: var(--accent-color);
  color: var(--primary-color);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: var(--primary-color);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin: 0 24rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
  line-height: 1;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-light);
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--text-light);
  opacity: 0.7;
  display: block;
}

/* 加载状态 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: var(--primary-color);
  border-radius: var(--border-radius);
  margin: 0 24rpx;
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-light);
  margin-left: 16rpx;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .message-items {
    margin: 0 16rpx;
  }
  
  .message-item {
    padding: 20rpx 24rpx;
  }
  
  .icon-img {
    width: 72rpx;
    height: 72rpx;
  }
  
  .empty-state {
    margin: 0 16rpx;
  }
} 