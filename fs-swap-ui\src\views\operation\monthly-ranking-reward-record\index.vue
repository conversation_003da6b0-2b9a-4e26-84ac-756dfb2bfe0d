<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- 小区选择器 -->
      <area-chain-selector
        :regionId="queryParams.regionId"
        :communityId="queryParams.communityId"
        :residentialId="queryParams.residentialId"
        @update:regionId="val => queryParams.regionId = val"
        @update:communityId="val => queryParams.communityId = val"
        @update:residentialId="val => queryParams.residentialId = val"
        :labels="{
          region: '行政区域',
          community: '社区',
          residential: '小区'
        }"
        :showCommunity="true"
        :showResidential="true"
        :inlineLayout="true"
        ref="queryAreaSelector"
      />
      <el-form-item label="年月" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="请选择年月"
          clearable
        />
      </el-form-item>
      <el-form-item label="发放状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择发放状态" clearable>
          <el-option label="待发放" value="PENDING" />
          <el-option label="已发放" value="SUCCESS" />
          <el-option label="发放失败" value="FAILED" />
        </el-select>
      </el-form-item>
      <el-form-item label="奖励类型" prop="rewardType">
        <el-select v-model="queryParams.rewardType" placeholder="请选择奖励类型" clearable>
          <el-option
            v-for="dict in dict.type.ranking_reward_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleGenerate"
          v-hasPermi="['operation:monthly-ranking-reward-record:generate']"
        >生成奖励</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-money"
          size="mini"
          @click="handleBatchIssue"
          v-hasPermi="['operation:monthly-ranking-reward-record:issue']"
        >批量发放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDeleteByMonth"
          v-hasPermi="['operation:monthly-ranking-reward-record:remove']"
        >清理记录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:monthly-ranking-reward-record:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <span class="statistic-label">总记录数</span>
            <span class="statistic-value">{{ statistics.total }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <span class="statistic-label">待发放</span>
            <span class="statistic-value pending">{{ statistics.pending }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <span class="statistic-label">已发放</span>
            <span class="statistic-value success">{{ statistics.success }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="statistic-item">
            <span class="statistic-label">发放失败</span>
            <span class="statistic-value failed">{{ statistics.failed }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="rewardRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户信息" align="center" width="200">
        <template slot-scope="scope">
          <div class="user-info">
            <image-preview :src="scope.row.avatar || defaultAvatar" :width="40" :height="40"/>
            <div class="user-details">
              <div>{{ scope.row.nickname }}</div>
              <div class="user-id">ID: {{ scope.row.userId }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排名" align="center" prop="rankPosition" width="80">
        <template slot-scope="scope">
          <el-tag :type="getRankTagType(scope.row.rankPosition)" size="small">
            第{{ scope.row.rankPosition }}名
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickname" />
      <el-table-column label="奖励名称" align="center" prop="rewardName" />
      <el-table-column label="奖励数量" align="center" prop="rewardAmount" />
      <el-table-column label="奖励类型" align="center" prop="rewardType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ranking_reward_type" :value="scope.row.rewardType"/>
        </template>
      </el-table-column>
      <el-table-column label="发放状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.reward_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="发放时间" align="center" prop="issuedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.issuedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === 'PENDING' || scope.row.status === 'FAILED'"
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleIssue(scope.row)"
            v-hasPermi="['operation:monthly-ranking-reward-record:issue']"
          >发放</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 生成奖励对话框 -->
    <el-dialog title="生成奖励记录" :visible.sync="generateDialogVisible" width="400px" append-to-body>
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="100px">
        <el-form-item label="年月" prop="yearMonth">
          <el-date-picker
            v-model="generateForm.yearMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择年月"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmGenerate" :loading="generateLoading">确定生成</el-button>
      </div>
    </el-dialog>

    <!-- 清理记录对话框 -->
    <el-dialog title="清理奖励记录" :visible.sync="deleteDialogVisible" width="400px" append-to-body>
      <div style="text-align: center;">
        <i class="el-icon-warning" style="font-size: 50px; color: #f56c6c;"></i>
        <p style="margin: 20px 0;">确定要清理指定月份的奖励记录吗？</p>
        <p style="color: #909399; font-size: 14px;">此操作将删除该月份的所有奖励记录，不可恢复！</p>
      </div>
      <el-form ref="deleteForm" :model="deleteForm" :rules="deleteRules" label-width="100px">
        <el-form-item label="年月" prop="yearMonth">
          <el-date-picker
            v-model="deleteForm.yearMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择年月"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">确定清理</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRewardRecord, generateRewards, batchIssueRewards, issueReward, deleteRewardsByMonth } from '@/api/operation/monthly-ranking-reward-record'
import AreaChainSelector from '@/components/AreaChainSelector'

export default {
  name: 'MonthlyRankingRewardRecord',
  components: {
    AreaChainSelector
  },
  dicts: ['reward_status', 'ranking_reward_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 奖励记录表格数据
      rewardRecordList: [],
      // 默认头像
      defaultAvatar: require('@/assets/images/profile.jpg'),
      // 统计信息
      statistics: {
        total: 0,
        pending: 0,
        success: 0,
        failed: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        yearMonth: null,
        regionId: null,
        communityId: null,
        residentialId: null,
        status: null,
        rewardType: null
      },
      // 生成奖励对话框
      generateDialogVisible: false,
      generateLoading: false,
      generateForm: {
        yearMonth: null
      },
      generateRules: {
        yearMonth: [
          { required: true, message: '年月不能为空', trigger: 'change' }
        ]
      },
      // 删除记录对话框
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteForm: {
        yearMonth: null
      },
      deleteRules: {
        yearMonth: [
          { required: true, message: '年月不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 默认查询当前月份
    const now = new Date()
    this.queryParams.yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    this.getList()
  },
  methods: {
    /** 查询奖励记录列表 */
    getList() {
      this.loading = true
      listRewardRecord(this.queryParams).then(response => {
        this.rewardRecordList = response.rows
        this.total = response.total
        this.updateStatistics()
        this.loading = false
      })
    },
    /** 更新统计信息 */
    updateStatistics() {
      this.statistics.total = this.rewardRecordList.length
      this.statistics.pending = this.rewardRecordList.filter(item => item.status === 'PENDING').length
      this.statistics.success = this.rewardRecordList.filter(item => item.status === 'SUCCESS').length
      this.statistics.failed = this.rewardRecordList.filter(item => item.status === 'FAILED').length
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.$refs.queryAreaSelector.reset()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 生成奖励按钮操作 */
    handleGenerate() {
      this.generateForm.yearMonth = null
      this.generateDialogVisible = true
    },
    /** 确认生成奖励 */
    confirmGenerate() {
      this.$refs['generateForm'].validate(valid => {
        if (valid) {
          this.generateLoading = true
          generateRewards(this.generateForm.yearMonth).then(response => {
            this.$modal.msgSuccess(response.msg)
            this.generateDialogVisible = false
            this.getList()
          }).finally(() => {
            this.generateLoading = false
          })
        }
      })
    },
    /** 批量发放奖励 */
    handleBatchIssue() {
      this.$modal.confirm('确认要批量发放所有待发放的奖励吗？').then(() => {
        batchIssueRewards().then(response => {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        })
      })
    },
    /** 发放单个奖励 */
    handleIssue(row) {
      this.$modal.confirm('确认要发放该奖励吗？').then(() => {
        issueReward(row.id).then(response => {
          this.$modal.msgSuccess(response.msg)
          this.getList()
        })
      })
    },
    /** 清理记录按钮操作 */
    handleDeleteByMonth() {
      this.deleteForm.yearMonth = null
      this.deleteDialogVisible = true
    },
    /** 确认清理记录 */
    confirmDelete() {
      this.$refs['deleteForm'].validate(valid => {
        if (valid) {
          this.deleteLoading = true
          deleteRewardsByMonth(this.deleteForm.yearMonth).then(response => {
            this.$modal.msgSuccess(response.msg)
            this.deleteDialogVisible = false
            this.getList()
          }).finally(() => {
            this.deleteLoading = false
          })
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/monthly-ranking-reward-record/export', {
        ...this.queryParams
      }, `reward_record_${new Date().getTime()}.xlsx`)
    },
    /** 获取排名标签类型 */
    getRankTagType(rank) {
      if (rank === 1) return 'danger'
      if (rank === 2) return 'warning'
      if (rank === 3) return 'success'
      return 'info'
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger'
      }
      return statusMap[status] || 'info'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待发放',
        'SUCCESS': '已发放',
        'FAILED': '发放失败'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statistic-item {
  text-align: center;
}

.statistic-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.statistic-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.statistic-value.pending {
  color: #E6A23C;
}

.statistic-value.success {
  color: #67C23A;
}

.statistic-value.failed {
  color: #F56C6C;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-details {
  text-align: left;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.reward-amount {
  font-weight: bold;
  color: #E6A23C;
}
</style>
