package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.fs.swap.common.core.domain.entity.CommunityPhone;
import com.fs.swap.system.service.ICommunityPhoneService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.AdminBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;

/**
 * 社区服务-常用电话Controller
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/operation/phone")
public class CommunityPhoneController extends AdminBaseController
{
    @Autowired
    private ICommunityPhoneService communityPhoneService;

    /**
     * 查询社区服务-常用电话列表
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityPhone communityPhone)
    {
        startPage();
        List<CommunityPhone> list = communityPhoneService.selectCommunityPhoneList(communityPhone);
        return getDataTable(list);
    }

    /**
     * 导出社区服务-常用电话列表
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:export')")
    @Log(title = "社区服务-常用电话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityPhone communityPhone)
    {
        List<CommunityPhone> list = communityPhoneService.selectCommunityPhoneList(communityPhone);
        ExcelUtil<CommunityPhone> util = new ExcelUtil<CommunityPhone>(CommunityPhone.class);
        util.exportExcel(response, list, "社区服务-常用电话数据");
    }

    /**
     * 获取社区服务-常用电话详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityPhoneService.selectCommunityPhoneById(id));
    }

    /**
     * 新增社区服务-常用电话
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:add')")
    @Log(title = "社区服务-常用电话", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityPhone communityPhone)
    {
        communityPhone.setCreateBy(getUsername());
        return toAjax(communityPhoneService.insertCommunityPhone(communityPhone));
    }

    /**
     * 修改社区服务-常用电话
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:edit')")
    @Log(title = "社区服务-常用电话", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityPhone communityPhone)
    {
        communityPhone.setUpdateBy(getUsername());
        return toAjax(communityPhoneService.updateCommunityPhone(communityPhone));
    }

    /**
     * 删除社区服务-常用电话
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:remove')")
    @Log(title = "社区服务-常用电话", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityPhoneService.deleteCommunityPhoneByIds(ids));
    }
    
    /**
     * 审核社区服务-常用电话
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:audit')")
    @Log(title = "社区服务-常用电话审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{id}")
    public AjaxResult audit(@PathVariable Long id, 
                           @RequestParam Integer auditStatus, 
                           @RequestParam(required = false) String auditRemark)
    {
        return toAjax(communityPhoneService.auditCommunityPhone(id, auditStatus, getUserId(), auditRemark));
    }
    
    /**
     * 批量审核社区服务-常用电话
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:audit')")
    @Log(title = "社区服务-常用电话批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/batch")
    public AjaxResult batchAudit(@RequestParam List<Long> ids, 
                                @RequestParam Integer auditStatus, 
                                @RequestParam(required = false) String auditRemark)
    {
        return toAjax(communityPhoneService.batchAuditCommunityPhone(ids, auditStatus, getUserId(), auditRemark));
    }
    
    /**
     * 获取电话统计信息
     */
    @PreAuthorize("@ss.hasPermi('operation:phone:stats')")
    @GetMapping("/stats")
    public AjaxResult getStats(@RequestParam(required = false) Long regionId,
                              @RequestParam(required = false) Long communityId,
                              @RequestParam(required = false) Long residentialId)
    {
        List<CommunityPhone> stats = communityPhoneService.getPhoneStatsByCategory(regionId, communityId, residentialId);
        return success(stats);
    }
} 