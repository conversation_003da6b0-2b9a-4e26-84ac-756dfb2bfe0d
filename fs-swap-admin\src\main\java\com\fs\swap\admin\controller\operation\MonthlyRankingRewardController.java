package com.fs.swap.admin.controller.operation;

import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardRecord;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.system.service.IMonthlyRankingRewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月度排行榜奖励发放Controller
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@RestController
@RequestMapping("/operation/monthly-ranking-reward-record")
public class MonthlyRankingRewardController extends BaseController {
    
    @Autowired
    private IMonthlyRankingRewardService monthlyRankingRewardService;

    /**
     * 查询月度排行榜奖励发放记录列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyRankingRewardRecord monthlyRankingRewardRecord) {
        startPage();
        List<MonthlyRankingRewardRecord> list = monthlyRankingRewardService.selectMonthlyRankingRewardRecordList(monthlyRankingRewardRecord);
        return getDataTable(list);
    }

    /**
     * 查询指定年月的奖励发放记录（带用户信息）
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:list')")
    @GetMapping("/records/{yearMonth}")
    public TableDataInfo getRewardRecords(@PathVariable String yearMonth) {
        startPage();
        List<MonthlyRankingRewardRecord> list = monthlyRankingRewardService.selectRewardRecordWithUserInfo(yearMonth, null);
        return getDataTable(list);
    }

    /**
     * 查询用户的奖励历史记录
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:list')")
    @GetMapping("/user/{userId}")
    public TableDataInfo getUserRewardHistory(@PathVariable Long userId) {
        startPage();
        List<MonthlyRankingRewardRecord> list = monthlyRankingRewardService.selectUserRewardHistory(userId);
        return getDataTable(list);
    }

    /**
     * 导出月度排行榜奖励发放记录列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:export')")
    @Log(title = "月度排行榜奖励发放记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyRankingRewardRecord monthlyRankingRewardRecord) {
        List<MonthlyRankingRewardRecord> list = monthlyRankingRewardService.selectMonthlyRankingRewardRecordList(monthlyRankingRewardRecord);
        ExcelUtil<MonthlyRankingRewardRecord> util = new ExcelUtil<MonthlyRankingRewardRecord>(MonthlyRankingRewardRecord.class);
        util.exportExcel(response, list, "月度排行榜奖励发放记录数据");
    }

    /**
     * 获取月度排行榜奖励发放记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(monthlyRankingRewardService.selectMonthlyRankingRewardRecordById(id));
    }

    /**
     * 生成指定月份的奖励记录
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:generate')")
    @Log(title = "生成月度奖励记录", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateRewards(@RequestParam String yearMonth) {
        try {
            int count = monthlyRankingRewardService.generateMonthlyRewards(yearMonth);
            return AjaxResult.success("成功生成 " + count + " 条奖励记录");
        } catch (Exception e) {
            return AjaxResult.error("生成奖励记录失败: " + e.getMessage());
        }
    }

    /**
     * 生成上个月的奖励记录
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:generate')")
    @Log(title = "生成上月奖励记录", businessType = BusinessType.INSERT)
    @PostMapping("/generate/last-month")
    public AjaxResult generateLastMonthRewards() {
        try {
            String lastMonth = DateUtils.dateTimeNow("yyyy-MM");
            // 获取上个月
            String[] parts = lastMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            
            if (month == 1) {
                year--;
                month = 12;
            } else {
                month--;
            }
            
            String yearMonth = String.format("%04d-%02d", year, month);
            int count = monthlyRankingRewardService.generateMonthlyRewards(yearMonth);
            return AjaxResult.success("成功生成上月(" + yearMonth + ")奖励记录 " + count + " 条");
        } catch (Exception e) {
            return AjaxResult.error("生成上月奖励记录失败: " + e.getMessage());
        }
    }

    /**
     * 批量发放待发放的奖励
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:issue')")
    @Log(title = "批量发放奖励", businessType = BusinessType.UPDATE)
    @PostMapping("/batchIssue")
    public AjaxResult batchIssueRewards() {
        try {
            int count = monthlyRankingRewardService.batchIssueRewards();
            return AjaxResult.success("成功发放 " + count + " 条奖励");
        } catch (Exception e) {
            return AjaxResult.error("批量发放奖励失败: " + e.getMessage());
        }
    }

    /**
     * 发放单个奖励
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:issue')")
    @Log(title = "发放奖励", businessType = BusinessType.UPDATE)
    @PostMapping("/issue/{id}")
    public AjaxResult issueReward(@PathVariable Long id) {
        try {
            MonthlyRankingRewardRecord record = monthlyRankingRewardService.selectMonthlyRankingRewardRecordById(id);
            if (record == null) {
                return AjaxResult.error("奖励记录不存在");
            }
            
            boolean success = monthlyRankingRewardService.issueReward(record);
            if (success) {
                return AjaxResult.success("奖励发放成功");
            } else {
                return AjaxResult.error("奖励发放失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("发放奖励失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定年月的奖励记录
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward:remove')")
    @Log(title = "删除月度奖励记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByMonth")
    public AjaxResult deleteRewards(@RequestParam String yearMonth) {
        try {
            int count = monthlyRankingRewardService.deleteRewardsByYearMonth(yearMonth);
            return AjaxResult.success("成功删除 " + count + " 条奖励记录");
        } catch (Exception e) {
            return AjaxResult.error("删除奖励记录失败: " + e.getMessage());
        }
    }
} 