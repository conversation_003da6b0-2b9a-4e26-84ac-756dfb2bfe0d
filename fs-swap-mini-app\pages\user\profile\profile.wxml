<view class="container">
  <view class="avatar-section">
    <button open-type="chooseAvatar" class="avatar-button" plain bindchooseavatar="chooseAvatar">
      <image class="avatar" mode="aspectFill" src="{{userInfo.avatar}}" />
    </button>
  </view>

  <view class="section-title">基本信息</view>
  <view class="info-card">
    <van-cell value="{{userInfo.nickname}}" bind:click="nicknameClick" required size="large" is-link>
      <view slot="title">昵称</view>
    </van-cell>
    <van-cell required value="{{genderText}}" bind:click="genderClick" size="large" is-link="{{userInfo.gender === 0}}">
      <view slot="title">性别</view>
      <view wx:if="{{userInfo.gender !== 0}}" class="gender-locked" slot="right-icon">
        <van-icon name="lock" size="14px" color="#999"/>
      </view>
    </van-cell>
    <van-cell required value="{{userInfo.birthday}}" bind:click="birthdayClick" size="large" is-link>
      <view slot="title">生日</view>
    </van-cell>
    <van-cell value="{{userInfo.id}}" size="large">
      <view slot="title">用户ID</view>
    </van-cell>
  </view>

  <view class="section-title">联系方式</view>
  <view class="info-card">
    <van-cell bind:click="wechatQrClick" size="large">
      <view slot="title" class="contact-row">
        <text class="contact-label">微信码</text>
        <view class="contact-value">
          <text wx:if="{{!contactInfo.wechatQr}}">点击上传二维码</text>
          <image wx:else class="qr-code-preview" src="{{contactInfo.wechatQr}}" mode="aspectFit"></image>
        </view>
      </view>
      <view class="contact-visibility" slot="right-icon" wx:if="{{contactInfo.wechatQr}}">
        <van-switch checked="{{wechatQrVisible}}" size="24px" bind:change="onContactVisibilityChange" data-type="{{ContactType.WECHAT_QR.code}}" catch:tap="preventTap" />
      </view>
    </van-cell>
    <van-cell bind:click="wechatIdClick" size="large">
      <view slot="title" class="contact-row">
        <text class="contact-label">微信号</text>
        <text class="contact-value">{{contactInfo.wechatId || '点击设置'}}</text>
      </view>
      <view class="contact-visibility" slot="right-icon" wx:if="{{contactInfo.wechatId}}">
        <van-switch checked="{{wechatVisible}}" size="24px" bind:change="onContactVisibilityChange" data-type="{{ContactType.WECHAT_ID.code}}" catch:tap="preventTap" />
      </view>
    </van-cell>
    <van-cell bind:click="mobileClick" size="large">
      <view slot="title" class="contact-row">
        <text class="contact-label">手机号</text>
        <text class="contact-value">{{contactInfo.mobile || '点击设置'}}</text>
      </view>
      <view class="contact-visibility" slot="right-icon">
        <van-switch checked="{{mobileVisible}}" size="24px" bind:change="onContactVisibilityChange" data-type="{{ContactType.MOBILE.code}}" catch:tap="preventTap" />
      </view>
    </van-cell>
  
   
  </view>

  <van-dialog
    use-slot
    title="修改昵称"
    show="{{ nicknameShow }}"
    show-cancel-button
    bind:close="onNicknameClose"
    bind:confirm="nicknameConfirm"
  >
    <van-field
      model:value="{{ nicknameField }}"
      placeholder="请输入昵称"
      type="nickname" bind:nicknamereview="nicknamereview"
      border="{{ false }}"
    />
    <view class="nickname-tips">请设置2-14个字符，不包括@＜＞/等无效字符</view>
  </van-dialog>

  <van-action-sheet show="{{ birthdayShow }}" bind:close="birthdayCancel" title="请选择生日">
    <view>
      <van-datetime-picker value="{{ currentDate }}" max-date="{{ maxDate }}" min-date="{{ minDate }}" type="date" value="{{ birthdayPicker }}" bind:confirm="birthdayConfirm" bind:cancel="birthdayCancel" />
    </view>
  </van-action-sheet>

  <van-action-sheet show="{{ genderShow }}" bind:close="genderCancel" title="请选择性别">
    <view>
      <van-picker show-toolbar columns="{{ genderColumns }}" default-index="{{ 1 }}" bind:cancel="genderCancel" bind:confirm="genderConfirm" />
    </view>
  </van-action-sheet>

  <van-dialog
    use-slot
    title="修改微信号"
    show="{{ wechatIdShow }}"
    show-cancel-button
    bind:close="onWechatIdClose"
    bind:confirm="wechatIdConfirm"
  >
    <van-field
      model:value="{{ wechatIdField }}"
      placeholder="请输入微信号"
      border="{{ false }}"
    />
    <view class="nickname-tips">微信号为6-20个字符，只能包含字母、数字、下划线和减号</view>
  </van-dialog>

  <van-dialog
    use-slot
    title="修改手机号"
    show="{{ mobileShow }}"
    show-cancel-button
    bind:close="onMobileClose"
    bind:confirm="mobileConfirm"
  >
    <van-field
      model:value="{{ mobileField }}"
      placeholder="请输入手机号"
      type="number"
      maxlength="11"
      border="{{ false }}"
    />
    <view class="nickname-tips">请输入11位有效手机号码</view>
  </van-dialog>

  <van-dialog
    use-slot
    title="上传微信二维码"
    show="{{ wechatQrShow }}"
    show-cancel-button
    bind:close="onWechatQrClose"
    bind:confirm="wechatQrConfirm"
  >
    <view class="qr-upload-container">
      <view class="qr-preview-wrapper" bindtap="chooseQrCode">
        <image wx:if="{{tempQrCodePath}}" class="qr-preview" src="{{tempQrCodePath}}" mode="aspectFit"></image>
        <view wx:else class="qr-placeholder">
          <van-icon name="photograph" size="48px" color="#ddd" />
          <text class="qr-placeholder-text">点击选择图片</text>
        </view>
      </view>
      <view class="nickname-tips">请上传清晰的微信二维码图片，建议使用微信中的"我的二维码"</view>
    </view>
  </van-dialog>
</view>