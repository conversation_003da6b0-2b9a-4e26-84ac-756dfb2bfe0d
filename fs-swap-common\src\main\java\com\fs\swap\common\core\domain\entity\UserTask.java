package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户任务对象 user_task
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户任务ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 任务编码 */
    @Excel(name = "任务编码")
    private String taskCode;

    /** 当前完成次数 */
    @Excel(name = "当前完成次数")
    private Integer currentCount;

    /** 目标次数 */
    @Excel(name = "目标次数")
    private Integer targetCount;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=进行中,2=已完成,3=已领取,4=已过期")
    private String status;

    /** 任务日期(每日任务用) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date taskDate;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date rewardTime;

    /** 删除标志 */
    private Integer deleted;
} 