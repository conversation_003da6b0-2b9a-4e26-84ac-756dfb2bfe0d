<!--pages/community-phone-publish/index.wxml-->
<view class="container">
  <form bindsubmit="handleSubmit">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">
        <van-icon name="info-o" size="16px" color="#3B7FFF" />
        <text>基本信息</text>
      </view>

      <van-field
        value="{{ formData.name }}"
        label="名称"
        placeholder="请输入电话名称（最多50字）"
        required
        bind:change="onNameChange"
        input-class="field-input"
        error="{{ nameError }}"
        error-message="{{ nameError }}"
      />

      <van-field
        value="{{ formData.phoneNumber }}"
        label="电话号码"
        placeholder="请输入手机号或固话"
        type="number"
        required
        bind:change="onPhoneNumberChange"
        input-class="field-input"
        error="{{ phoneError }}"
        error-message="{{ phoneError }}"
      />

      <van-field
        value="{{ formData.description }}"
        label="描述"
        placeholder="请输入电话描述（可选，最多200字）"
        type="textarea"
        autosize
        maxlength="200"
        show-word-limit
        bind:change="onDescriptionChange"
        input-class="field-input"
      />

      <van-field
        value="{{ formData.categoryName }}"
        label="分类"
        placeholder="请选择分类"
        required
        readonly
        bindtap="showCategoryPicker"
        right-icon="arrow"
        input-class="field-input"
      />
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{ submitting ? 'disabled' : '' }}"
        form-type="submit"
        loading="{{ submitting }}"
        disabled="{{ !formValid || submitting }}"
      >{{ submitting ? '提交中...' : '提交' }}</button>
    </view>
  </form>

  <!-- 分类选择器 -->
  <van-popup
    show="{{ showCategoryPicker }}"
    position="bottom"
    bind:close="hideCategoryPicker"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-picker
      columns="{{ categories }}"
      value-key="text"
      show-toolbar
      title="选择分类"
      bind:confirm="onCategoryConfirm"
      bind:cancel="hideCategoryPicker"
    />
  </van-popup>
</view>
