<template>
  <div class="region-chain-selector">
    <!-- 行政区域级联选择器 -->
    <el-form-item :label="label" :prop="prop">
      <el-cascader
        v-model="selectedRegionPath"
        :options="regionOptions"
        :props="cascaderProps"
        :placeholder="placeholder"
        :disabled="disabled"
        :clearable="true"
        :filterable="true"
        :show-all-levels="true"
        :loading="loading"
        @change="handleRegionChange"
      >
        <template slot="empty">
          <div v-if="loading" class="el-cascader-menu__empty-text">
            <i class="el-icon-loading"></i> 加载中...
          </div>
          <div v-else class="el-cascader-menu__empty-text">
            暂无数据
          </div>
        </template>
      </el-cascader>
    </el-form-item>

    <!-- 错误提示 -->
    <div v-if="error" class="region-selector-error">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 刷新按钮 -->
    <div v-if="showRefreshButton" class="region-selector-refresh">
      <el-button
        type="text"
        size="mini"
        icon="el-icon-refresh"
        @click="refreshData"
        :loading="loading"
      >
        刷新数据
      </el-button>
    </div>
  </div>
</template>

<script>
import areaDataService from '@/services/areaDataService';

export default {
  name: 'RegionChainSelector',

  props: {
    // 外部传入的值
    regionId: {
      type: [Number, String],
      default: null
    },

    // 表单属性名
    prop: {
      type: String,
      default: 'regionId'
    },

    // 标签
    label: {
      type: String,
      default: '行政区域'
    },

    // 占位符
    placeholder: {
      type: String,
      default: '请选择行政区域'
    },

    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },

    // 是否正在加载表单数据
    isLoading: {
      type: Boolean,
      default: false
    },

    // 是否显示刷新按钮
    showRefreshButton: {
      type: Boolean,
      default: true
    },

    // 最大级别（默认为4，即省市区街道四级）
    maxLevel: {
      type: Number,
      default: 4
    }
  },

  data() {
    return {
      // 选中的区域路径
      selectedRegionPath: [],

      // 加载状态
      loading: false,
      error: null,

      // 内部状态
      internalLoading: false,

      // 区域数据
      regionList: [],

      // 级联选择器配置
      cascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: false,
        emitPath: true,
        expandTrigger: 'click',
        lazy: false,
        multiple: false
      }
    };
  },

  computed: {
    // 区域选项（级联选择器数据源）
    regionOptions() {
      // 如果数据未加载，返回空数组
      if (!this.regionList || this.regionList.length === 0) {
        return [];
      }

      // 获取省级区域
      const provinces = this.regionList
        .filter(item => item.type === 1)
        .map(province => {
          // 获取该省下的市级区域
          const cities = this.regionList
            .filter(item => item.type === 2 && item.pid === province.id)
            .map(city => {
              // 获取该市下的区/县级区域
              const districts = this.regionList
                .filter(item => item.type === 3 && item.pid === city.id)
                .map(district => {
                  // 如果最大级别为3，则区县级别没有子节点
                  if (this.maxLevel === 3) {
                    return {
                      id: district.id,
                      name: district.name,
                      leaf: true
                    };
                  }

                  // 获取该区/县下的街道级区域
                  const streets = this.regionList
                    .filter(item => item.type === 4 && item.pid === district.id)
                    .map(street => ({
                      id: street.id,
                      name: street.name,
                      leaf: true
                    }));

                  return {
                    id: district.id,
                    name: district.name,
                    children: streets.length > 0 ? streets : null,
                    leaf: streets.length === 0
                  };
                });

              return {
                id: city.id,
                name: city.name,
                children: districts.length > 0 ? districts : null,
                leaf: districts.length === 0
              };
            });

          return {
            id: province.id,
            name: province.name,
            children: cities.length > 0 ? cities : null,
            leaf: cities.length === 0
          };
        });

      return provinces;
    }
  },

  watch: {
    // 监听外部传入的值变化
    regionId(newVal) {
      if (!this.isLoading && newVal !== this.getSelectedRegionId()) {
        this.initFromRegionId(newVal);
      }
    },

    // 监听内部选择值变化，同步到外部
    selectedRegionPath(newVal) {
      if (!this.internalLoading) {
        this.updateRegionId();
      }
    }
  },

  created() {
    // 加载区域数据
    this.loadData();
  },

  methods: {
    /**
     * 获取当前选中的区域ID
     * @returns {Number|null} 区域ID
     */
    getSelectedRegionId() {
      if (!this.selectedRegionPath || this.selectedRegionPath.length === 0) {
        return null;
      }
      // 返回路径中的最后一个ID
      return this.selectedRegionPath[this.selectedRegionPath.length - 1];
    },

    /**
     * 更新区域ID
     */
    updateRegionId() {
      const regionId = this.getSelectedRegionId();
      this.$emit('update:regionId', regionId);
      this.$emit('change', regionId);
    },

    /**
     * 处理区域选择变更
     * @param {Array} value 选中的区域路径
     */
    handleRegionChange() {
      if (this.isLoading) return;
    },

    /**
     * 从区域ID初始化选择器
     * @param {Number|String} regionId 区域ID
     */
    async initFromRegionId(regionId) {
      if (!regionId) {
        this.selectedRegionPath = [];
        return;
      }

      try {
        // 标记为内部加载状态，防止触发更新
        this.internalLoading = true;

        // 检查数据是否已加载，如果未加载则加载
        if (this.regionList.length === 0) {
          await this.loadData(false); // 不强制刷新
        }

        // 获取区域路径
        const numRegionId = Number(regionId);
        const path = areaDataService.getRegionPath(numRegionId);

        if (!path || path.length === 0) {
          // 尝试直接从区域列表中查找
          const region = this.regionList.find(r => r.id === numRegionId);
          if (region) {
            this.selectedRegionPath = [region.id];
            return;
          }
          return;
        }

        // 设置选中路径
        const idPath = path.map(item => item.id);
        this.selectedRegionPath = idPath;
      } catch (error) {
        // 错误处理
      } finally {
        // 延迟恢复状态，确保DOM已更新
        setTimeout(() => {
          this.internalLoading = false;
        }, 300);
      }
    },

    /**
     * 加载区域数据
     * @param {boolean} forceRefresh 是否强制刷新
     * @returns {Promise} 加载完成的Promise
     */
    async loadData(forceRefresh = false) {
      if (this.loading && !forceRefresh) {
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        // 加载区域数据
        await areaDataService.loadRegions(forceRefresh);

        // 更新本地数据
        this.regionList = areaDataService.getRegions();

        return true;
      } catch (err) {
        this.error = '加载区域数据失败，请稍后重试';
        return false;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      return this.loadData(true);
    },

    /**
     * 强制更新值
     * @param {Array} path 区域路径
     */
    forceUpdateValue(path) {
      this.internalLoading = true;
      this.selectedRegionPath = path;
      setTimeout(() => {
        this.internalLoading = false;
      }, 300);
    }
  }
};
</script>

<style scoped>
.region-chain-selector {
  position: relative;
}

.region-selector-error {
  margin-top: 10px;
  margin-bottom: 10px;
}

.region-selector-refresh {
  position: absolute;
  right: 0;
  top: -30px;
  font-size: 12px;
}

/* 添加过渡效果 */
.el-select-dropdown__item {
  transition: background-color 0.2s ease;
}

/* 自定义下拉菜单样式 */
:deep(.el-select-dropdown__item) {
  padding: 8px 20px;
}
</style>
