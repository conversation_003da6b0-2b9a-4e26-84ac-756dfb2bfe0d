package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 月度排行榜奖励配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface MonthlyRankingRewardConfigMapper {
    
    /**
     * 查询月度排行榜奖励配置
     * 
     * @param id 月度排行榜奖励配置主键
     * @return 月度排行榜奖励配置
     */
    public MonthlyRankingRewardConfig selectMonthlyRankingRewardConfigById(Long id);

    /**
     * 查询月度排行榜奖励配置列表
     * 
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 月度排行榜奖励配置集合
     */
    public List<MonthlyRankingRewardConfig> selectMonthlyRankingRewardConfigList(MonthlyRankingRewardConfig monthlyRankingRewardConfig);

    /**
     * 查询启用的奖励配置列表
     * 
     * @return 启用的奖励配置集合
     */
    public List<MonthlyRankingRewardConfig> selectActiveRewardConfigs();

    /**
     * 根据排名查询匹配的奖励配置
     * 
     * @param rankPosition 排名位置
     * @return 奖励配置
     */
    public MonthlyRankingRewardConfig selectRewardConfigByRank(@Param("rankPosition") Integer rankPosition);

    /**
     * 新增月度排行榜奖励配置
     * 
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 结果
     */
    public int insertMonthlyRankingRewardConfig(MonthlyRankingRewardConfig monthlyRankingRewardConfig);

    /**
     * 修改月度排行榜奖励配置
     * 
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 结果
     */
    public int updateMonthlyRankingRewardConfig(MonthlyRankingRewardConfig monthlyRankingRewardConfig);

    /**
     * 删除月度排行榜奖励配置
     * 
     * @param id 月度排行榜奖励配置主键
     * @return 结果
     */
    public int deleteMonthlyRankingRewardConfigById(Long id);

    /**
     * 批量删除月度排行榜奖励配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyRankingRewardConfigByIds(Long[] ids);
} 