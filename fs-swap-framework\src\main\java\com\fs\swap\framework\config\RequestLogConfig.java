package com.fs.swap.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 请求日志配置
 */
@Configuration
@ConfigurationProperties(prefix = "request.log")
public class RequestLogConfig {

    /**
     * 是否启用请求日志记录
     */
    private boolean enabled = true;

    /**
     * 是否记录请求参数
     */
    private boolean logParams = true;

    /**
     * 是否记录响应结果
     */
    private boolean logResponse = true;

    /**
     * 参数日志最大长度
     */
    private int maxParamLength = 500;

    /**
     * 响应日志最大长度
     */
    private int maxResponseLength = 1000;

    /**
     * 是否记录IP地址
     */
    private boolean logIp = true;

    /**
     * 是否记录执行时间
     */
    private boolean logExecutionTime = true;

    /**
     * 慢请求阈值（毫秒），超过此时间的请求会标记为慢请求
     */
    private long slowRequestThreshold = 1000;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isLogParams() {
        return logParams;
    }

    public void setLogParams(boolean logParams) {
        this.logParams = logParams;
    }

    public boolean isLogResponse() {
        return logResponse;
    }

    public void setLogResponse(boolean logResponse) {
        this.logResponse = logResponse;
    }

    public int getMaxParamLength() {
        return maxParamLength;
    }

    public void setMaxParamLength(int maxParamLength) {
        this.maxParamLength = maxParamLength;
    }

    public int getMaxResponseLength() {
        return maxResponseLength;
    }

    public void setMaxResponseLength(int maxResponseLength) {
        this.maxResponseLength = maxResponseLength;
    }

    public boolean isLogIp() {
        return logIp;
    }

    public void setLogIp(boolean logIp) {
        this.logIp = logIp;
    }

    public boolean isLogExecutionTime() {
        return logExecutionTime;
    }

    public void setLogExecutionTime(boolean logExecutionTime) {
        this.logExecutionTime = logExecutionTime;
    }

    public long getSlowRequestThreshold() {
        return slowRequestThreshold;
    }

    public void setSlowRequestThreshold(long slowRequestThreshold) {
        this.slowRequestThreshold = slowRequestThreshold;
    }
} 