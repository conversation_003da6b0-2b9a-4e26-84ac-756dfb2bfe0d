<!--pages/activity/list/index.wxml-->
<view class="container">
  <!-- 顶部筛选区域 -->
  <view class="filter-header">
    <!-- 搜索框 -->
    <view class="search-bar">
      <van-search
        value="{{ searchValue }}"
        placeholder="搜索活动"
        bind:change="onSearchChange"
        bind:search="onSearch"
        shape="round"
        background="transparent"
        clearable
        left-icon="search"
      />
    </view>

    <!-- 分类标签 -->
    <view class="category-section">
      <view class="category-tabs">
        <view
          wx:for="{{categories}}"
          wx:key="value"
          class="category-tab {{activeTab === index ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-index="{{index}}"
        >
          {{item.text}}
        </view>
      </view>
    </view>
  </view>

  <!-- 活动列表布局 -->
  <view class="activity-container">

    <!-- 活动列表 -->
    <view class="activity-list">
      <view
        wx:for="{{activities}}"
        wx:key="id"
        class="activity-card"
        bindtap="onActivityTap"
        data-id="{{item.id}}"
      >
        <view class="activity-image-container">
          <image
            src="{{item.imageUrl || '/static/img/activity.png'}}"
            mode="aspectFill"
            class="activity-image"
            binderror="onImageError"
            data-index="{{index}}"
            lazy-load="true"
          />
          <view class="activity-category">{{item.categoryName}}</view>
        </view>
        <view class="activity-info">
          <view class="activity-title">{{item.title}}</view>
          <view class="activity-time">
            <van-icon name="clock-o" size="14px" />
            <text>{{item.formattedStartTime}}</text>
          </view>
          <view class="activity-address">
            <van-icon name="location-o" size="14px" />
            <text>{{item.address}}</text>
          </view>
          <view class="activity-footer">
            <view class="activity-organizer">
              <image src="{{item.avatar || '/static/img/default_avatar.png'}}" class="organizer-avatar" />
              <text>{{item.nickname}}</text>
            </view>
            <view class="activity-status">
              <van-tag type="{{item.status === '1' ? 'primary' : 'warning'}}">{{item.statusName}}</van-tag>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载中提示 -->
      <view wx:if="{{loading && activities.length > 0}}" class="loading-more">
        <van-loading size="24px" color="#3B7FFF">加载中...</van-loading>
      </view>

      <!-- 没有更多数据提示 -->
      <view wx:if="{{!hasMore && activities.length > 0}}" class="no-more">
        没有更多活动了
      </view>

      <!-- 空状态提示 -->
      <view wx:if="{{!loading && activities.length === 0}}" class="empty-state">
        <van-empty description="暂无活动" />
      </view>
    </view>
  </view>

  <!-- 发布活动按钮 -->
  <view class="publish-btn" bindtap="onPublishActivity">
    <van-icon name="plus" size="24px" color="#ffffff" />
  </view>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess"></login-action>

  <!-- 小区认证组件 -->
  <residential-auth id="residentialAuth" />

</view>
