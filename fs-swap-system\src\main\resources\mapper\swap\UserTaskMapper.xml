<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.UserTaskMapper">
    
    <resultMap type="UserTask" id="UserTaskResult">
        <result property="id"             column="id"               />
        <result property="userId"         column="user_id"          />
        <result property="taskCode"       column="task_code"        />
        <result property="currentCount"   column="current_count"    />
        <result property="targetCount"    column="target_count"     />
        <result property="status"         column="status"           />
        <result property="taskDate"       column="task_date"        />
        <result property="completeTime"   column="complete_time"    />
        <result property="rewardTime"     column="reward_time"      />
        <result property="deleted"        column="deleted"          />
        <result property="createTime"     column="create_time"      />
        <result property="updateTime"     column="update_time"      />
    </resultMap>

    <sql id="selectUserTaskVo">
        select id, user_id, task_code, current_count, target_count, status, task_date, 
               complete_time, reward_time, deleted, create_time, update_time
        from user_task
    </sql>

    <select id="selectUserTaskList" parameterType="UserTask" resultMap="UserTaskResult">
        <include refid="selectUserTaskVo"/>
        <where>  
            deleted = 0
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="taskDate != null"> and task_date = #{taskDate}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectUserTaskById" parameterType="Long" resultMap="UserTaskResult">
        <include refid="selectUserTaskVo"/>
        where id = #{id} and deleted = 0
    </select>

    <select id="selectUserTaskByUserAndCode" resultMap="UserTaskResult">
        <include refid="selectUserTaskVo"/>
        <where>
            user_id = #{userId} and task_code = #{taskCode} and deleted = 0
            <if test="taskDate != null">
                and task_date = #{taskDate}
            </if>
            <if test="taskDate == null">
                and task_date is null
            </if>
        </where>
        limit 1
    </select>

    <select id="selectUserTasksByUserId" parameterType="Long" resultMap="UserTaskResult">
        <include refid="selectUserTaskVo"/>
        where user_id = #{userId} and deleted = 0
        order by create_time desc
    </select>

    <select id="selectUserTasksToClaimByUserId" parameterType="Long" resultMap="UserTaskResult">
        <include refid="selectUserTaskVo"/>
        where user_id = #{userId} and status = '2' and deleted = 0
        order by complete_time desc
    </select>
        
    <insert id="insertUserTask" parameterType="UserTask" useGeneratedKeys="true" keyProperty="id">
        insert into user_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="currentCount != null">current_count,</if>
            <if test="targetCount != null">target_count,</if>
            <if test="status != null">status,</if>
            <if test="taskDate != null">task_date,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="rewardTime != null">reward_time,</if>
            deleted,
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="currentCount != null">#{currentCount},</if>
            <if test="targetCount != null">#{targetCount},</if>
            <if test="status != null">#{status},</if>
            <if test="taskDate != null">#{taskDate},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="rewardTime != null">#{rewardTime},</if>
            0,
            now()
         </trim>
    </insert>

    <update id="updateUserTask" parameterType="UserTask">
        update user_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="currentCount != null">current_count = #{currentCount},</if>
            <if test="targetCount != null">target_count = #{targetCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="taskDate != null">task_date = #{taskDate},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="rewardTime != null">reward_time = #{rewardTime},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserTaskById" parameterType="Long">
        update user_task set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteUserTaskByIds" parameterType="String">
        update user_task set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 