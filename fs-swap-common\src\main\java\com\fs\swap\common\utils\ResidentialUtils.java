package com.fs.swap.common.utils;

import com.fs.swap.common.core.domain.entity.ResidentialArea;
import com.fs.swap.common.core.domain.entity.UserHome;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.enums.ErrorType;
import org.springframework.util.StringUtils;

/**
 * 小区参数处理工具类
 * 用于统一处理小区相关的参数获取和验证逻辑
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public class ResidentialUtils {

    /**
     * 从请求参数中获取小区ID
     * 优先级：用户绑定的小区 > 请求参数中的小区ID
     *
     * @param userHome             用户绑定的小区信息（可为null）
     * @param requestResidentialId 请求参数中的小区ID（可为null）
     *
     */
    public static Long getResidentialId(UserHome userHome, Long requestResidentialId) {
        // 优先使用用户绑定的小区
        if (userHome != null && userHome.getResidentialId() != null) {
            return userHome.getResidentialId();
        }
        if (requestResidentialId == null) {
            throw new ServiceException(ErrorType.E_5013);
        }

        // 其次使用请求参数中的小区ID
        return requestResidentialId;
    }

    /**
     * 获取小区ID，如果为空则抛出异常
     *
     * @param userHome             用户绑定的小区信息（可为null）
     * @param requestResidentialId 请求参数中的小区ID（可为null）
     * @return 小区ID
     * @throws ServiceException 如果小区ID为空
     */
    public static Long getRequiredResidentialId(UserHome userHome, Long requestResidentialId) {
        Long residentialId = getResidentialId(userHome, requestResidentialId);
        if (residentialId == null) {
            throw new ServiceException(ErrorType.E_5013); // 需要在ErrorType中添加此错误码
        }
        return residentialId;
    }

    /**
     * 检查是否有有效的小区ID
     *
     * @param userHome             用户绑定的小区信息（可为null）
     * @param requestResidentialId 请求参数中的小区ID（可为null）
     * @return 是否有有效的小区ID
     */
    public static boolean hasValidResidentialId(UserHome userHome, Long requestResidentialId) {
        return getResidentialId(userHome, requestResidentialId) != null;
    }

    /**
     * 获取小区的区域和社区信息
     *
     * @param residentialArea 小区信息
     * @return 包含区域ID和社区ID的数组 [regionId, communityId]
     */
    public static Long[] getLocationIds(ResidentialArea residentialArea) {
        if (residentialArea == null) {
            return new Long[]{null, null};
        }
        return new Long[]{residentialArea.getRegionId(), residentialArea.getCommunityId()};
    }

    /**
     * 验证小区ID是否有效
     *
     * @param residentialId 小区ID
     * @return 是否有效
     */
    public static boolean isValidResidentialId(Long residentialId) {
        return residentialId != null && residentialId > 0;
    }

    /**
     * 构建小区相关的查询条件
     * 用于在Service层统一设置小区相关的查询参数
     *
     * @param entity        实体对象
     * @param residentialId 小区ID
     * @param <T>           实体类型
     */
    public static <T> void setResidentialCondition(T entity, Long residentialId) {
        if (entity == null || residentialId == null) {
            return;
        }

        try {
            // 使用反射设置residentialId字段
            java.lang.reflect.Field field = entity.getClass().getDeclaredField("residentialId");
            field.setAccessible(true);
            field.set(entity, residentialId);
        } catch (Exception e) {
            // 如果字段不存在或设置失败，静默处理
            // 这样可以兼容不同的实体类
        }
    }

    /**
     * 为实体对象设置小区ID（通用方法）
     *
     * @param entity        实体对象
     * @param residentialId 小区ID
     * @param fieldName     字段名称
     * @param <T>           实体类型
     */
    public static <T> void setResidentialField(T entity, Long residentialId, String fieldName) {
        if (entity == null || residentialId == null || !StringUtils.hasText(fieldName)) {
            return;
        }

        try {
            java.lang.reflect.Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, residentialId);
        } catch (Exception e) {
            // 静默处理异常
        }
    }

    /**
     * 检查用户是否有权限访问指定小区的数据
     *
     * @param userResidentialId    用户绑定的小区ID
     * @param requestResidentialId 请求的小区ID
     * @return 是否有权限
     */
    public static boolean hasPermissionToAccess(Long userResidentialId, Long requestResidentialId) {
        // 如果用户没有绑定小区，允许访问任何小区的数据
        if (userResidentialId == null) {
            return true;
        }

        // 如果请求的小区ID为空，使用用户绑定的小区
        if (requestResidentialId == null) {
            return true;
        }

        // 检查是否为同一个小区
        return userResidentialId.equals(requestResidentialId);
    }

    /**
     * 获取错误提示信息
     *
     * @return 小区相关的错误提示
     */
    public static String getResidentialRequiredMessage() {
        return "请先选择您的小区";
    }

    /**
     * 获取权限不足的错误提示信息
     *
     * @return 权限不足的错误提示
     */
    public static String getPermissionDeniedMessage() {
        return "您没有权限访问该小区的数据";
    }
}
