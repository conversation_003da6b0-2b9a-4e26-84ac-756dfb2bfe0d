package com.fs.swap.system.service.impl;

import com.fs.swap.common.constant.CacheConstants;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardConfig;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.MonthlyRankingRewardConfigMapper;
import com.fs.swap.system.service.IMonthlyRankingRewardConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 月度排行榜奖励配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class MonthlyRankingRewardConfigServiceImpl implements IMonthlyRankingRewardConfigService {

    private static final Logger log = LoggerFactory.getLogger(MonthlyRankingRewardConfigServiceImpl.class);

    @Autowired
    private MonthlyRankingRewardConfigMapper monthlyRankingRewardConfigMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询月度排行榜奖励配置
     * 
     * @param id 月度排行榜奖励配置主键
     * @return 月度排行榜奖励配置
     */
    @Override
    public MonthlyRankingRewardConfig selectMonthlyRankingRewardConfigById(Long id) {
        return monthlyRankingRewardConfigMapper.selectMonthlyRankingRewardConfigById(id);
    }

    /**
     * 查询月度排行榜奖励配置列表
     * 
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 月度排行榜奖励配置
     */
    @Override
    public List<MonthlyRankingRewardConfig> selectMonthlyRankingRewardConfigList(MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        return monthlyRankingRewardConfigMapper.selectMonthlyRankingRewardConfigList(monthlyRankingRewardConfig);
    }

    /**
     * 查询启用的奖励配置列表（优先从Redis获取）
     *
     * @return 启用的奖励配置集合
     */
    @Override
    public List<MonthlyRankingRewardConfig> selectActiveRewardConfigs() {
        String cacheKey = CacheConstants.MONTHLY_RANKING_REWARD_CONFIG_LIST_KEY;

        // 1. 先从Redis获取
        List<MonthlyRankingRewardConfig> cachedConfigs = redisCache.getCacheObject(cacheKey);
        if (cachedConfigs != null && !cachedConfigs.isEmpty()) {
            return cachedConfigs;
        }

        // 2. Redis没有，从数据库查询
        List<MonthlyRankingRewardConfig> dbConfigs = monthlyRankingRewardConfigMapper.selectActiveRewardConfigs();
        if (dbConfigs != null && !dbConfigs.isEmpty()) {
            // 3. 查到数据后缓存到Redis（永久缓存，通过管理端操作主动清除）
            redisCache.setCacheObject(cacheKey, dbConfigs);
        }

        return dbConfigs;
    }

    /**
     * 根据排名查询匹配的奖励配置（优先从Redis获取）
     *
     * @param rankPosition 排名位置
     * @return 奖励配置
     */
    @Override
    public MonthlyRankingRewardConfig selectRewardConfigByRank(Integer rankPosition) {
        // 优化：直接从统一的奖励配置列表中查找匹配的配置
        List<MonthlyRankingRewardConfig> allConfigs = selectActiveRewardConfigs();
        if (allConfigs == null || allConfigs.isEmpty()) {
            return null;
        }

        // 查找匹配的奖励配置
        for (MonthlyRankingRewardConfig config : allConfigs) {
            if (isRankMatched(rankPosition, config)) {
                return config;
            }
        }

        return null;
    }

    /**
     * 判断排名是否匹配奖励配置
     */
    private boolean isRankMatched(Integer rankPosition, MonthlyRankingRewardConfig config) {
        if (rankPosition == null || config == null) {
            return false;
        }

        Integer startRank = config.getRankStart();
        Integer endRank = config.getRankEnd();

        if (startRank == null) {
            return false;
        }

        // 如果endRank为null，表示只匹配startRank这一个排名
        if (endRank == null) {
            return rankPosition.equals(startRank);
        }

        // 匹配排名范围
        return rankPosition >= startRank && rankPosition <= endRank;
    }

    /**
     * 新增月度排行榜奖励配置
     *
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 结果
     */
    @Override
    public int insertMonthlyRankingRewardConfig(MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        monthlyRankingRewardConfig.setCreateTime(DateUtils.getNowDate());
        int result = monthlyRankingRewardConfigMapper.insertMonthlyRankingRewardConfig(monthlyRankingRewardConfig);

        if (result > 0) {
            // 清除相关缓存
            clearRewardConfigCache();
            log.info("新增奖励配置成功，已清除相关缓存");
        }

        return result;
    }

    /**
     * 修改月度排行榜奖励配置
     *
     * @param monthlyRankingRewardConfig 月度排行榜奖励配置
     * @return 结果
     */
    @Override
    public int updateMonthlyRankingRewardConfig(MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        monthlyRankingRewardConfig.setUpdateTime(DateUtils.getNowDate());
        int result = monthlyRankingRewardConfigMapper.updateMonthlyRankingRewardConfig(monthlyRankingRewardConfig);

        if (result > 0) {
            // 清除相关缓存
            clearRewardConfigCache();
            log.info("修改奖励配置成功，已清除相关缓存");
        }

        return result;
    }

    /**
     * 批量删除月度排行榜奖励配置
     *
     * @param ids 需要删除的月度排行榜奖励配置主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyRankingRewardConfigByIds(Long[] ids) {
        int result = monthlyRankingRewardConfigMapper.deleteMonthlyRankingRewardConfigByIds(ids);

        if (result > 0) {
            // 清除相关缓存
            clearRewardConfigCache();
            log.info("删除奖励配置成功，已清除相关缓存");
        }

        return result;
    }

    /**
     * 清除奖励配置相关缓存
     */
    private void clearRewardConfigCache() {
        try {
            // 只需要清除奖励配置列表缓存，因为现在所有奖励配置都统一存储在一个key中
            redisCache.deleteObject(CacheConstants.MONTHLY_RANKING_REWARD_CONFIG_LIST_KEY);

            log.info("已清除奖励配置缓存");
        } catch (Exception e) {
            log.error("清除奖励配置缓存失败", e);
        }
    }

    /**
     * 预加载奖励配置缓存
     */
    public void preloadRewardConfigCache() {
        try {
            log.info("开始预加载奖励配置缓存");

            // 预加载启用的奖励配置列表
            selectActiveRewardConfigs();

            log.info("奖励配置缓存预加载完成");
        } catch (Exception e) {
            log.error("预加载奖励配置缓存失败", e);
        }
    }

    /**
     * 删除月度排行榜奖励配置信息
     * 
     * @param id 月度排行榜奖励配置主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyRankingRewardConfigById(Long id) {
        return monthlyRankingRewardConfigMapper.deleteMonthlyRankingRewardConfigById(id);
    }
} 