package com.fs.swap.wx.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 消息免打扰设置DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
@Data
public class MessageMuteDTO {
    
    /**
     * 消息类型
     */
    @NotBlank(message = "消息类型不能为空")
    private String type;
    
    /**
     * 是否免打扰
     */
    @NotNull(message = "免打扰设置不能为空")
    private Boolean mute;
} 