# 页面迁移到新的全局组件管理方式示例

## index/index.js 迁移示例

### 迁移前的代码

```javascript
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
const app = getApp()
const userUtils = require('../../utils/user')

Page({
  data: {
    hasLogin: false
  },

  // 显示登录组件
  showLoginComponent() {
    userUtils.showLoginComponent(this, true)
  },

  // 隐藏登录组件  
  hideLoginComponent() {
    userUtils.hideLoginComponent(this)
  },

  // 确保登录并执行回调
  ensureLogin(callback) {
    return userUtils.ensureLogin(this, callback)
  },

  // 显示小区认证弹框
  showResidentialAuth: function() {
    const authDialog = this.selectComponent('#residentialAuth')
    if (authDialog) {
      authDialog.open()
    }
  },

  // 确认小区认证
  onConfirmResidentialAuth: function(e) {
    setTimeout(() => {
      this.updateResidentialName()
    }, 100)
  },

  // 关闭小区认证弹框
  onCloseResidentialAuth: function() {
    const authDialog = this.selectComponent('#residentialAuth')
    if (authDialog) {
      authDialog.close()
    }
  }
})
```

### 迁移后的代码

```javascript
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
const app = getApp()
const userUtils = require('../../utils/user')
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  data: {
    hasLogin: false
  },

  // 注意：showLogin 和 hideLogin 方法已通过 globalComponentsMixin 提供

  // 确保登录并执行回调的辅助方法
  ensureLogin(callback) {
    const app = getApp()
    const hasLogin = app.safeGetGlobalData('hasLogin', false)
    
    if (hasLogin) {
      // 已登录，直接执行回调
      if (typeof callback === 'function') {
        callback()
      }
      return true
    } else {
      // 未登录，显示登录组件
      this.showLogin(true) // 使用混入的方法
      return false
    }
  },

  // 重写混入的小区认证确认方法，添加页面特定逻辑
  onConfirmResidentialAuth: function(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onConfirmResidentialAuth.call(this, e)
    
    // 添加页面特定的处理逻辑：认证成功后更新小区名称
    setTimeout(() => {
      this.updateResidentialName()
    }, 100)
  },

  // 注意：showResidentialAuth 和 onCloseResidentialAuth 方法已通过 globalComponentsMixin 提供

  // 重写混入的登录成功方法，添加页面特定逻辑
  onLoginSuccess: function() {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginSuccess.call(this)
    
    // 添加页面特定的处理逻辑
    console.log('首页：登录成功，更新页面状态')
    
    // 更新登录状态
    this.setData({
      hasLogin: true
    })
    
    // 更新小区名称显示
    this.updateResidentialName()
    
    // 重新加载数据
    this.loadAllData()
  },

  // 重写混入的登录失败方法，添加页面特定逻辑
  onLoginFail: function(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginFail.call(this, e)
    
    // 添加页面特定的处理逻辑
    console.log('首页：登录失败')
  }

}, globalComponents.globalComponentsMixin))
```

## 迁移要点

### 1. 引入全局组件工具

```javascript
const globalComponents = require('../../utils/globalComponents')
```

### 2. 使用混入对象

```javascript
Page(Object.assign({
  // 页面方法
}, globalComponents.globalComponentsMixin))
```

### 3. 可用的混入方法

- `showLogin(autoClose)` - 显示登录组件
- `hideLogin()` - 隐藏登录组件
- `showResidentialAuth()` - 显示小区认证组件
- `hideResidentialAuth()` - 隐藏小区认证组件
- `onLoginSuccess()` - 登录成功处理
- `onLoginFail(e)` - 登录失败处理
- `onConfirmResidentialAuth(e)` - 小区认证确认处理
- `onCloseResidentialAuth()` - 小区认证关闭处理

### 4. 重写混入方法

如果需要添加页面特定的逻辑，可以重写混入方法：

```javascript
onLoginSuccess: function() {
  // 调用父类方法（可选）
  globalComponents.globalComponentsMixin.onLoginSuccess.call(this)
  
  // 添加页面特定的处理逻辑
  console.log('页面特定的登录成功处理')
  this.loadPageData()
}
```

### 5. 简化的使用方式

```javascript
// 显示登录组件
this.showLogin()

// 显示小区认证组件
this.showResidentialAuth()

// 确保登录后执行操作
if (this.ensureLogin(() => {
  // 登录后的操作
  this.doSomething()
})) {
  // 已登录，操作已执行
}
```

## 其他页面迁移模板

### 简单页面（只需要登录功能）

```javascript
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  data: {
    // 页面数据
  },

  // 页面方法
  someMethod() {
    // 使用混入的方法
    this.showLogin()
  }

}, globalComponents.globalComponentsMixin))
```

### 复杂页面（需要自定义处理）

```javascript
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  data: {
    // 页面数据
  },

  // 重写登录成功方法
  onLoginSuccess() {
    globalComponents.globalComponentsMixin.onLoginSuccess.call(this)
    // 页面特定处理
    this.refreshData()
  },

  // 重写小区认证确认方法
  onConfirmResidentialAuth(e) {
    globalComponents.globalComponentsMixin.onConfirmResidentialAuth.call(this, e)
    // 页面特定处理
    this.updateUI()
  }

}, globalComponents.globalComponentsMixin))
```
