/* pages/community-service-detail/index.wxss */

.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  transition: background-color 0.2s ease;
}

.nav-left:active, .nav-right:active {
  background-color: #f8f9fa;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.error-text {
  font-size: 16px;
  color: #666666;
  text-align: center;
  margin-bottom: 8px;
}

.error-actions {
  display: flex;
  gap: 12px;
}

.error-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-radius: 20px;
  font-size: 14px;
  color: #666666;
  transition: background-color 0.2s ease;
}

.error-btn:active {
  background-color: #e9ecef;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 20px;
}

/* 图片轮播区域 */
.image-section {
  position: relative;
  height: 250px;
  background-color: #ffffff;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-count {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 默认图片 */
.default-image-section {
  height: 200px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-service-image {
  width: 80px;
  height: 80px;
  opacity: 0.3;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  margin: 12px 16px;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.info-card:first-child {
  margin-top: 0;
}

/* 卡片标题 */
.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f7ff;
}

/* 服务头部信息 */
.service-header {
  gap: 12px;
}

.service-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.service-name {
  font-size: 20px;
  font-weight: 700;
  color: #333333;
  line-height: 1.3;
  flex: 1;
  margin-right: 12px;
}

.service-badges {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.badge.official {
  background-color: #fff3cd;
  color: #856404;
}

.badge.recommended {
  background-color: #d1ecf1;
  color: #0c5460;
}

.service-category {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
}

.category-text {
  font-size: 14px;
  color: #666666;
}

.service-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 12px;
  color: #999999;
}

/* 位置信息 */
.location-info {
  margin-bottom: 16px;
}

.address-row {
  margin-bottom: 6px;
}

.address-text {
  font-size: 15px;
  color: #333333;
  line-height: 1.4;
}

.distance-row {
  margin-bottom: 8px;
}

.distance-text {
  font-size: 13px;
  color: #666666;
}

/* 小地图 */
.mini-map-container {
  position: relative;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.mini-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(59, 127, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: background-color 0.2s ease;
}

.map-overlay:active {
  background-color: rgba(59, 127, 255, 0.2);
}

.map-overlay-text {
  font-size: 12px;
  color: #3B7FFF;
  font-weight: 500;
}

/* 联系信息 */
.contact-info {
  gap: 12px;
}

.contact-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.contact-row:last-child {
  border-bottom: none;
}

.contact-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666666;
}

.contact-value {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.phone-number {
  color: #3B7FFF;
  text-decoration: underline;
  cursor: pointer;
}

.phone-number:active {
  opacity: 0.7;
}

/* 服务描述 */
.description-content {
  line-height: 1.6;
}

.description-text {
  font-size: 14px;
  color: #333333;
  white-space: pre-wrap;
}

/* 服务标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 6px 12px;
  background-color: #f0f7ff;
  color: #3B7FFF;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e0f0ff;
}

/* 提交信息 */
.submit-info {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.submit-details {
  gap: 8px;
}

.submit-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.submit-label {
  font-size: 13px;
  color: #666666;
}

.submit-value {
  font-size: 13px;
  color: #333333;
  font-weight: 500;
}

.submit-value.status-0 {
  color: #f39c12;
}

.submit-value.status-1 {
  color: #27ae60;
}

.submit-value.status-2 {
  color: #e74c3c;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background-color: #3B7FFF;
  color: #ffffff;
}

.action-btn.primary:active {
  background-color: #2563EB;
  transform: scale(0.98);
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #333333;
  border: 1px solid #e9ecef;
}

.action-btn.secondary:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .service-name {
    font-size: 18px;
  }
  
  .info-card {
    margin: 8px 12px;
    padding: 12px;
  }
  
  .bottom-actions {
    padding: 10px 12px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .nav-bar, .info-card, .bottom-actions {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .nav-title, .service-name, .address-text, .contact-value, .description-text {
    color: #ffffff;
  }
  
  .category-text, .contact-label, .submit-label {
    color: #cccccc;
  }
  
  .stat-text, .distance-text, .submit-value {
    color: #999999;
  }
} 