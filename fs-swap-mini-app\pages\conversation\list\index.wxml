<view class="conversation-container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">消息</text>
    <view class="actions">
      <view class="add-btn" bindtap="onStartNewChat">
        <text class="iconfont icon-add"></text>
      </view>
    </view>
  </view>

  <!-- 会话列表 -->
  <scroll-view 
    class="conversation-list" 
    scroll-y 
    enhanced 
    show-scrollbar="{{false}}"
    refresher-enabled 
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
  >
    <view class="conversation-items">
      <view 
        class="conversation-item"
        wx:for="{{conversationList}}" 
        wx:key="conversationId"
        data-conversation="{{item}}"
        bindtap="onConversationTap"
      >
        <!-- 头像 -->
        <view class="avatar-wrapper">
          <image src="{{item.avatar || '/static/img/default-avatar.png'}}" class="avatar"></image>
          <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">
            {{item.unreadCount > 99 ? '99+' : item.unreadCount}}
          </view>
        </view>
        
        <!-- 会话信息 -->
        <view class="conversation-info">
          <view class="info-header">
            <text class="name">{{item.title}}</text>
            <text class="time">{{item.lastMessageTimeText}}</text>
          </view>
          <view class="last-message">{{item.lastMessageContent}}</view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{conversationList.length === 0 && !isLoading}}">
      <view class="empty-icon">💬</view>
      <text class="empty-text">还没有聊天记录</text>
      <text class="empty-desc">开始你的第一次对话吧</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <van-loading size="16px" color="#3B7FFF" />
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view> 