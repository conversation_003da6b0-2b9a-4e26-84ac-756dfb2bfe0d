const messageService = require('../../../services/message')
const { formatTime } = require('../../../utils/dateUtil')

Page({
  data: {
    messageList: [],
    isLoading: false,
    isRefreshing: false,
    pageNum: 1,
    pageSize: 20,
    hasMore: true
  },

  onLoad() {
    this.loadMessageList()
  },

  onShow() {
    // 标记系统消息为已读
    this.markAllAsRead()
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  /**
   * 下拉刷新
   */
  async onRefresh() {
    this.setData({ 
      isRefreshing: true,
      pageNum: 1,
      hasMore: true
    })
    await this.loadMessageList()
    this.setData({ isRefreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (!this.data.hasMore || this.data.isLoading) return
    
    this.setData({
      pageNum: this.data.pageNum + 1,
      isLoading: true
    })
    
    try {
      await this.loadMessageList(false)
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 加载系统消息列表
   */
  async loadMessageList(isRefresh = true) {
    const { pageNum, pageSize } = this.data
    
    try {
      if (isRefresh) {
        this.setData({ isLoading: true })
      }
      
      const result = await messageService.getMessageList({
        type: 'system',
        pageNum,
        pageSize
      })
      
      const newList = result.list.map(item => ({
        ...item,
        timeText: formatTime(item.createTime)
      }))
      
      this.setData({
        messageList: isRefresh ? newList : [...this.data.messageList, ...newList],
        hasMore: newList.length === pageSize
      })
      
    } catch (error) {
      console.error('加载系统消息失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 点击消息项
   */
  async onMessageTap(e) {
    const { item } = e.currentTarget.dataset
    
    try {
      // 标记为已读
      if (!item.isRead) {
        await messageService.markAsRead([item.id])
        // 更新本地状态
        this.updateMessageReadStatus(item.id)
      }
      
      // 根据消息类型进行跳转
      this.handleMessageNavigation(item)
      
    } catch (error) {
      console.error('处理消息点击失败:', error)
    }
  },

  /**
   * 处理消息跳转
   */
  handleMessageNavigation(message) {
    const { jumpType, jumpUrl, relatedId } = message
    
    switch (jumpType) {
      case 'page':
        wx.navigateTo({ url: jumpUrl })
        break
      case 'tabBar':
        wx.switchTab({ url: jumpUrl })
        break
      case 'order':
        wx.navigateTo({
          url: `/pages/order/detail?id=${relatedId}`
        })
        break
      case 'activity':
        wx.navigateTo({
          url: `/pages/activity/detail/index?id=${relatedId}`
        })
        break
      default:
        // 默认展示消息详情
        this.showMessageDetail(message)
    }
  },

  /**
   * 显示消息详情
   */
  showMessageDetail(message) {
    wx.showModal({
      title: message.title,
      content: message.content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 全部标为已读
   */
  async markAllAsRead() {
    try {
      await messageService.markAllAsRead('system')
      
      // 更新本地状态
      const updatedList = this.data.messageList.map(item => ({
        ...item,
        isRead: true
      }))
      
      this.setData({
        messageList: updatedList
      })
      
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  },

  /**
   * 更新单条消息已读状态
   */
  updateMessageReadStatus(messageId) {
    const updatedList = this.data.messageList.map(item => {
      if (item.id === messageId) {
        return { ...item, isRead: true }
      }
      return item
    })
    
    this.setData({
      messageList: updatedList
    })
  }
}) 