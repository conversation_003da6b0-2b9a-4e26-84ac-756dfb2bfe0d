package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务完成记录对象 task_complete_log
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskCompleteLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 任务编码 */
    @Excel(name = "任务编码")
    private String taskCode;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String eventType;

    /** 业务ID(如商品ID、订单ID) */
    @Excel(name = "业务ID")
    private String businessId;

    /** 获得碳豆 */
    @Excel(name = "获得碳豆")
    private Integer rewardSilver;

    /** 删除标志 */
    private Integer deleted;
} 