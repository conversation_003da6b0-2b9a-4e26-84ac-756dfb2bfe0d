/**
 * 时间工具函数
 */

/**
 * 规范化时间格式，确保iOS兼容性
 * @param {number|string|Date} time - 时间戳或时间字符串
 * @returns {Date} 规范化后的Date对象
 */
function normalizeDate(time) {
  if (!time) return new Date()
  
  if (time instanceof Date) {
    return time
  }
  
  if (typeof time === 'number') {
    return new Date(time)
  }
  
  if (typeof time === 'string') {
    // 处理 "YYYY-MM-DD HH:mm:ss" 格式，转换为 "YYYY/MM/DD HH:mm:ss" 以兼容iOS
    const normalizedTime = time.replace(/(\d{4})-(\d{2})-(\d{2})(\s+\d{2}:\d{2}:\d{2})?/, (match, year, month, day, timePart) => {
      return `${year}/${month}/${day}${timePart || ''}`
    })
    return new Date(normalizedTime)
  }
  
  return new Date(time)
}

/**
 * 格式化时间显示
 * @param {number|string|Date} time - 时间戳或时间字符串
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(time) {
  if (!time) return ''
  
  const date = normalizeDate(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟显示"刚刚"
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时显示"XX分钟前"
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  }
  
  // 小于1天显示"XX小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }
  
  // 同一年显示"MM-DD"
  if (date.getFullYear() === now.getFullYear()) {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}-${day}`
  }
  
  // 不同年显示"YYYY-MM-DD"
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 格式化完整时间
 * @param {number|string|Date} time - 时间戳或时间字符串
 * @returns {string} 格式化后的完整时间字符串
 */
function formatFullTime(time) {
  if (!time) return ''
  
  const date = normalizeDate(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

/**
 * 判断是否为同一天
 * @param {number|string|Date} time1 - 时间1
 * @param {number|string|Date} time2 - 时间2
 * @returns {boolean} 是否为同一天
 */
function isSameDay(time1, time2) {
  if (!time1 || !time2) return false
  
  const date1 = normalizeDate(time1)
  const date2 = normalizeDate(time2)
  
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate()
}

/**
 * 获取友好的时间显示
 * @param {number|string|Date} time - 时间戳或时间字符串
 * @returns {string} 友好的时间显示
 */
function getFriendlyTime(time) {
  if (!time) return ''

  const date = normalizeDate(time)
  const now = new Date()

  // 今天显示时间
  if (isSameDay(date, now)) {
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `今天 ${hours}:${minutes}`
  }

  // 昨天显示"昨天"
  const yesterday = new Date(now.getTime() - 86400000)
  if (isSameDay(date, yesterday)) {
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `昨天 ${hours}:${minutes}`
  }

  // 一周内显示星期
  const weekDiff = Math.floor((now.getTime() - date.getTime()) / 86400000)
  if (weekDiff < 7) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${weekdays[date.getDay()]} ${hours}:${minutes}`
  }

  // 超过一周显示完整日期
  return formatFullTime(time)
}

/**
 * 格式化时间戳为日期字符串（只显示到天）
 * @param {number|string|Date} time - 时间戳、时间字符串或Date对象
 * @param {string} defaultValue - 当时间无效时的默认值
 * @returns {string} 格式化后的日期字符串 YYYY-MM-DD
 */
function formatToDateOnly(time, defaultValue = '') {
  if (!time) {
    return defaultValue
  }

  try {
    const date = normalizeDate(time)

    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的时间格式:', time)
      return defaultValue
    }

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('格式化日期失败:', error, time)
    return defaultValue
  }
}

/**
 * 将日期设置为当天的结束时间（23:59:59）
 * @param {number|string|Date} time - 时间戳、时间字符串或Date对象
 * @returns {number} 当天结束时间的时间戳
 */
function setToEndOfDay(time) {
  try {
    const date = normalizeDate(time)

    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      throw new Error('无效的时间格式')
    }

    // 设置为当天的23:59:59
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
    return endOfDay.getTime()
  } catch (error) {
    console.error('设置结束时间失败:', error, time)
    throw error
  }
}

/**
 * 检查日期是否有效
 * @param {any} time - 要检查的时间值
 * @returns {boolean} 是否为有效日期
 */
function isValidDate(time) {
  if (!time) {
    return false
  }

  try {
    const date = normalizeDate(time)
    return !isNaN(date.getTime())
  } catch (error) {
    return false
  }
}

module.exports = {
  formatTime,
  formatFullTime,
  isSameDay,
  getFriendlyTime,
  normalizeDate,
  formatToDateOnly,
  setToEndOfDay,
  isValidDate
}