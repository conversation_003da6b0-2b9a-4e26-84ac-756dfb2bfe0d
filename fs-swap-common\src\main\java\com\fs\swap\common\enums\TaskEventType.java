package com.fs.swap.common.enums;

/**
 * 任务事件类型枚举
 */
public enum TaskEventType implements BaseEnum {
    /**
     * 发布商品
     */
    PRODUCT_PUBLISH("PRODUCT_PUBLISH", "发布商品"),

    /**
     * 购买商品
     */
    PRODUCT_BUY("PRODUCT_BUY", "购买商品"),

    /**
     * 卖出商品
     */
    PRODUCT_SELL("PRODUCT_SELL", "卖出商品"),

    /**
     * 收藏商品
     */
    PRODUCT_COLLECT("PRODUCT_COLLECT", "收藏商品"),

    /**
     * 发布活动
     */
    ACTIVITY_PUBLISH("ACTIVITY_PUBLISH", "发布活动"),

    /**
     * 参加活动
     */
    ACTIVITY_JOIN("ACTIVITY_JOIN", "参加活动"),

    /**
     * 每日登录
     */
    DAILY_LOGIN("DAILY_LOGIN", "每日登录"),

    /**
     * 邀请好友
     */
    INVITE_FRIEND("INVITE_FRIEND", "邀请好友"),

    /**
     * 分享商品
     */
    SHARE_PRODUCT("SHARE_PRODUCT", "分享商品");

    private final String code;
    private final String info;

    TaskEventType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据编码获取枚举
     */
    public static TaskEventType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskEventType type : TaskEventType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 