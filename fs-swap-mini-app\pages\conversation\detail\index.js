const messageService = require('../../../services/message')
const systemInfoService = require('../../../services/systemInfo')
const { formatTime } = require('../../../utils/dateUtil')

Page({
  data: {
    conversationId: '',
    title: '',
    messageList: [],
    inputText: '',
    isLoading: false,
    isSending: false
  },

  // 简单定时器配置
  refreshTimer: null,
  refreshInterval: 15000, // 15秒刷新一次
  lastMessageId: null, // 记录最后一条消息ID，用于增量更新

  onLoad(options) {
    let { conversationId, title, userId, nickname, orderId, sellerId, productId } = options
    
    // 如果传入的是sellerId（从商品详情页面跳转）
    if (!conversationId && sellerId) {
      const currentUserId = this.getCurrentUserId()
      // 生成会话ID格式：conv_小ID_大ID
      const userIds = [parseInt(currentUserId), parseInt(sellerId)].sort((a, b) => a - b)
      conversationId = `conv_${userIds[0]}_${userIds[1]}`
      title = '与卖家聊天'
      
      // 存储相关信息
      this.setData({
        sellerId: sellerId,
        productId: productId
      })
    }
    // 如果传入的是userId（从订单页面跳转），需要生成conversationId
    else if (!conversationId && userId) {
      const currentUserId = this.getCurrentUserId()
      // 生成会话ID格式：conv_小ID_大ID
      const userIds = [parseInt(currentUserId), parseInt(userId)].sort((a, b) => a - b)
      conversationId = `conv_${userIds[0]}_${userIds[1]}`
      title = nickname || '聊天'
    }
    
    this.setData({
      conversationId: conversationId || '',
      title: title || '聊天'
    })
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: title || '聊天'
    })
    
    // 只有当conversationId存在时才加载消息
    if (conversationId) {
      this.loadMessages()
    } else {
      console.error('缺少必要的会话参数')
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
    }
  },

  onShow() {
    // 页面显示时刷新消息
    this.loadMessages()
    // 启动自动刷新
    this.startAutoRefresh()
    // 标记会话中的消息为已读
    this.markConversationAsRead()
  },

  onHide() {
    // 页面隐藏时停止自动刷新
    this.stopAutoRefresh()
  },

  onUnload() {
    // 页面销毁时停止自动刷新
    this.stopAutoRefresh()
  },

  /**
   * 启动自动刷新
   */
  startAutoRefresh() {
    this.stopAutoRefresh() // 先停止之前的定时器
    
    this.refreshTimer = setInterval(() => {
      this.silentRefreshMessages()
    }, this.refreshInterval)
    
    },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
      }
  },

  /**
   * 静默刷新消息（定时器专用）
   */
  async silentRefreshMessages() {
    try {
      const result = await messageService.getConversationMessages(this.data.conversationId)
      
      // 获取当前用户信息
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      const currentUserId = this.getCurrentUserId()
      
      const processedList = await Promise.all(result.list.map(async item => {
        const isMine = item.fromUserId === currentUserId
        
        let avatar = null
        let myAvatar = null
        let nickname = null
        let myNickname = null
        
        if (isMine) {
          // 我的消息：处理我的头像和昵称
          if (currentUserInfo.avatar) {
            myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
          } else {
            myAvatar = '/static/img/default_avatar.png'
          }
          myNickname = currentUserInfo.nickname || '我'
        } else {
          // 别人的消息：处理对方头像和昵称
          if (item.avatar || item.fromUserAvatar) {
            avatar = await systemInfoService.processImageUrl(item.avatar || item.fromUserAvatar)
          } else {
            avatar = '/static/img/default_avatar.png'
          }
          nickname = item.nickname || item.fromUserNickname || '用户'
        }
        
        return {
          ...item,
          timeText: formatTime(item.createTime),
          isMine: isMine,
          avatar: avatar,
          myAvatar: myAvatar,
          nickname: nickname,
          myNickname: myNickname
        }
      }))
      
      // 检查是否有新消息
      const currentMessages = this.data.messageList
      const hasNewMessages = processedList.length > currentMessages.length
      
      // 如果有新消息或消息内容有变化，更新界面
      if (hasNewMessages || JSON.stringify(currentMessages) !== JSON.stringify(processedList)) {
        
        // 记录当前滚动位置
        const shouldScrollToBottom = this.shouldAutoScroll()
        
        this.setData({
          messageList: processedList
        })
        
        // 如果有新消息且用户在底部，自动滚动到底部
        if (hasNewMessages && shouldScrollToBottom) {
          this.scrollToBottom()
          
          // 轻微振动提示有新消息
          wx.vibrateShort({
            type: 'light'
          })
        }
        
        // 更新最后消息ID
        if (processedList.length > 0) {
          this.lastMessageId = processedList[processedList.length - 1].id
        }
        
        // 如果有新消息，标记为已读
        if (hasNewMessages) {
          setTimeout(() => {
            this.markConversationAsRead()
          }, 200)
        }
      }
      
    } catch (error) {
      console.error('静默刷新消息失败:', error)
    }
  },

  /**
   * 判断是否应该自动滚动到底部
   * 如果用户正在查看底部附近的消息，则自动滚动
   */
  shouldAutoScroll() {
    // 这里可以根据实际情况判断
    // 简单实现：总是自动滚动（在实际项目中可以根据滚动位置判断）
    return true
  },

  /**
   * 发送消息后立即刷新
   */
  refreshAfterSend() {
    // 发送消息后，立即刷新一次获取最新状态
    setTimeout(() => {
      this.silentRefreshMessages()
    }, 500)
  },

  /**
   * 加载聊天消息
   */
  async loadMessages() {
    try {
      this.setData({ isLoading: true })
      
      const result = await messageService.getConversationMessages(this.data.conversationId)
      // 获取当前用户信息
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      const currentUserId = this.getCurrentUserId()
      const processedList = await Promise.all(result.list.map(async item => {
        const isMine = item.fromUserId === currentUserId
        
        let avatar = null
        let myAvatar = null
        let nickname = null
        let myNickname = null
        
        if (isMine) {
          // 我的消息：处理我的头像和昵称
          if (currentUserInfo.avatar) {
            myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
          } else {
            myAvatar = '/static/img/default_avatar.png'
          }
          myNickname = currentUserInfo.nickname || '我'
        } else {
          // 别人的消息：处理对方头像和昵称
          if (item.avatar || item.fromUserAvatar) {
            avatar = await systemInfoService.processImageUrl(item.avatar || item.fromUserAvatar)
          } else {
            avatar = '/static/img/default_avatar.png'
          }
          nickname = item.nickname || item.fromUserNickname || '用户'
        }
        
        const processedItem = {
          ...item,
          timeText: formatTime(item.createTime),
          isMine: isMine,
          avatar: avatar,
          myAvatar: myAvatar,
          nickname: nickname,
          myNickname: myNickname
        }
        
        return processedItem
      }))
      
      this.setData({
        messageList: processedList
      })
      
      // 滚动到底部
      this.scrollToBottom()
      
      // 标记新的未读消息为已读
      setTimeout(() => {
        this.markConversationAsRead()
      }, 100)
      
    } catch (error) {
      console.error('加载消息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  /**
   * 发送消息
   */
  async sendMessage() {
    const { inputText, conversationId, isSending } = this.data
    
    if (!inputText.trim() || isSending) {
      return
    }
    
    try {
      this.setData({ isSending: true })
      
      // 先添加消息到本地列表（优化用户体验）
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      
      // 处理我的头像
      let myAvatar = '/static/img/default_avatar.png'
      if (currentUserInfo.avatar) {
        myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
      }
      
      const tempMessage = {
        id: 'temp_' + Date.now(),
        content: inputText.trim(),
        isMine: true,
        timeText: '发送中...',
        fromUserId: this.getCurrentUserId(),
        sending: true,
        myAvatar: myAvatar,
        myNickname: currentUserInfo.nickname || '我'
      }
      
      this.setData({
        messageList: [...this.data.messageList, tempMessage],
        inputText: ''
      })
      
      this.scrollToBottom()
      
      // 发送消息到服务器
      await messageService.sendUserMessage({
        toUserId: this.getTargetUserId(),
        content: inputText.trim(),
        conversationId: conversationId
      })
      
      // 发送成功后，立即刷新获取最新消息
      this.refreshAfterSend()
      
      // 重新加载消息列表
      await this.loadMessages()
      
    } catch (error) {
      console.error('发送消息失败:', error)
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      })
      
      // 移除发送失败的临时消息
      const messageList = this.data.messageList.filter(item => !item.sending)
      this.setData({ messageList })
      
    } finally {
      this.setData({ isSending: false })
    }
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    setTimeout(() => {
      this.setData({
        scrollToView: 'message-bottom'
      })
    }, 100)
  },

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    const app = getApp()
    const userInfo = app.safeGetGlobalData('userInfo')
    const userId = userInfo ? userInfo.id : 0

    if (!userId) {
      console.error('无法获取当前用户ID，请检查登录状态')
    }

    return userId
  },

  /**
   * 获取目标用户ID（从会话ID中解析）
   */
  getTargetUserId() {
    const { conversationId } = this.data
    const currentUserId = this.getCurrentUserId()
    
    // 安全检查：确保conversationId存在
    if (!conversationId) {
      console.error('conversationId为空，无法获取目标用户ID')
      return 0
    }
    
    // 解析会话ID：conv_userId1_userId2
    const parts = conversationId.split('_')
    if (parts.length >= 3) {
      const userId1 = parseInt(parts[1])
      const userId2 = parseInt(parts[2])
      return userId1 === currentUserId ? userId2 : userId1
    }
    
    console.error('conversationId格式错误:', conversationId)
    return 0
  },

  /**
   * 长按消息（预留功能）
   */
  onMessageLongPress(e) {
    const { message } = e.currentTarget.dataset
    
    if (message.isMine && !message.sending) {
      wx.showActionSheet({
        itemList: ['撤回消息'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.recallMessage(message.id)
          }
        }
      })
    }
  },

  /**
   * 撤回消息（预留功能）
   */
  async recallMessage(messageId) {
    // 预留功能
    },

  /**
   * 头像加载错误处理
   */
  async onAvatarError(e) {
    const { type, index } = e.currentTarget.dataset
    
    // 更新对应消息的头像为默认头像
    const messageList = [...this.data.messageList]
    if (messageList[index]) {
      if (type === 'other') {
        messageList[index].avatar = '/static/img/default_avatar.png'
      } else if (type === 'mine') {
        messageList[index].myAvatar = '/static/img/default_avatar.png'
      }
      
      this.setData({
        messageList: messageList
      })
    }
  },

  /**
   * 标记会话中的消息为已读
   */
  async markConversationAsRead() {
    try {
      const { messageList } = this.data
      const currentUserId = this.getCurrentUserId()
      
      // 找出所有未读的对方消息
      const unreadMessages = messageList.filter(item => 
        !item.isMine && !item.isRead && item.fromUserId !== currentUserId
      )
      
      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map(item => item.id)
        await messageService.markAsRead(messageIds)
        
        // 更新本地消息状态
        this.updateMessagesReadStatus(messageIds)
      }
    } catch (error) {
      console.error('标记会话消息已读失败:', error)
    }
  },

  /**
   * 更新本地消息已读状态
   */
  updateMessagesReadStatus(messageIds) {
    const updatedList = this.data.messageList.map(item => {
      if (messageIds.includes(item.id)) {
        return { ...item, isRead: true }
      }
      return item
    })
    
    this.setData({
      messageList: updatedList
    })
  }
}) 