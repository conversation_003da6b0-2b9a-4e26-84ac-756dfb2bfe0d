package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.CommunityHelp;
import java.util.List;

/**
 * 邻里互助Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface CommunityHelpMapper
{
    /**
     * 查询邻里互助
     *
     * @param id 邻里互助主键
     * @return 邻里互助
     */
    public CommunityHelp selectCommunityHelpById(Long id);

    /**
     * 查询邻里互助列表
     *
     * @param communityHelp 邻里互助
     * @return 邻里互助集合
     */
    public List<CommunityHelp> selectCommunityHelpList(CommunityHelp communityHelp);

    /**
     * 新增邻里互助
     *
     * @param communityHelp 邻里互助
     * @return 结果
     */
    public int insertCommunityHelp(CommunityHelp communityHelp);

    /**
     * 修改邻里互助
     *
     * @param communityHelp 邻里互助
     * @return 结果
     */
    public int updateCommunityHelp(CommunityHelp communityHelp);

    /**
     * 删除邻里互助
     *
     * @param id 邻里互助主键
     * @return 结果
     */
    public int deleteCommunityHelpById(Long id);

    /**
     * 批量删除邻里互助
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunityHelpByIds(Long[] ids);

    /**
     * 增加浏览次数
     *
     * @param id 邻里互助主键
     * @return 结果
     */
    public int incrementViewCount(Long id);
}