package com.fs.swap.system.service;

import java.util.List;
import com.fs.swap.common.core.domain.entity.ConversationMember;

/**
 * 会话成员Service接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface IConversationMemberService 
{
    /**
     * 查询会话成员
     * 
     * @param id 会话成员主键
     * @return 会话成员
     */
    public ConversationMember selectConversationMemberById(Long id);

    /**
     * 查询会话成员列表
     * 
     * @param conversationMember 会话成员
     * @return 会话成员集合
     */
    public List<ConversationMember> selectConversationMemberList(ConversationMember conversationMember);

    /**
     * 根据会话ID和用户ID查询会话成员
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 会话成员
     */
    public ConversationMember selectByConversationAndUser(String conversationId, Long userId);

    /**
     * 查询会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 会话成员集合
     */
    public List<ConversationMember> selectMembersByConversation(String conversationId);

    /**
     * 查询用户参与的所有会话
     * 
     * @param userId 用户ID
     * @return 会话成员集合
     */
    public List<ConversationMember> selectConversationsByUser(Long userId);

    /**
     * 新增会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    public int insertConversationMember(ConversationMember conversationMember);

    /**
     * 批量新增会话成员
     * 
     * @param members 会话成员列表
     * @return 结果
     */
    public int batchInsertConversationMembers(List<ConversationMember> members);

    /**
     * 修改会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    public int updateConversationMember(ConversationMember conversationMember);

    /**
     * 更新未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param unreadCount 未读数量
     * @return 结果
     */
    public int updateUnreadCount(String conversationId, Long userId, Integer unreadCount);

    /**
     * 增加未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param increment 增加数量
     * @return 结果
     */
    public int incrementUnreadCount(String conversationId, Long userId, Integer increment);

    /**
     * 清零未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param lastReadMessageId 最后已读消息ID
     * @return 结果
     */
    public int clearUnreadCount(String conversationId, Long userId, String lastReadMessageId);

    /**
     * 设置会话免打扰
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param isMuted 是否免打扰
     * @return 结果
     */
    public int updateMuteStatus(String conversationId, Long userId, Integer isMuted);

    /**
     * 批量删除会话成员
     * 
     * @param ids 需要删除的会话成员主键
     * @return 结果
     */
    public int deleteConversationMemberByIds(Long[] ids);

    /**
     * 删除会话成员信息
     * 
     * @param id 会话成员主键
     * @return 结果
     */
    public int deleteConversationMemberById(Long id);

    /**
     * 删除会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    public int deleteByConversationId(String conversationId);

    /**
     * 用户离开会话（软删除）
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    public int leaveConversation(String conversationId, Long userId);

    /**
     * 检查用户是否在会话中
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkMemberExists(String conversationId, Long userId);

    /**
     * 添加用户到会话
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    public int addMemberToConversation(String conversationId, Long userId);

    /**
     * 批量添加用户到会话
     * 
     * @param conversationId 会话ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    public int addMembersToConversation(String conversationId, List<Long> userIds);
} 