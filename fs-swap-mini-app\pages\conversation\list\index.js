const messageService = require('../../../services/message')
const { formatTime } = require('../../../utils/dateUtil')

Page({
  data: {
    conversationList: [],
    isLoading: false,
    isRefreshing: false
  },

  onLoad() {
    this.loadConversationList()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadConversationList()
  },

  onPullDownRefresh() {
    this.onRefresh()
  },

  /**
   * 下拉刷新
   */
  async onRefresh() {
    this.setData({ isRefreshing: true })
    await this.loadConversationList()
    this.setData({ isRefreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 加载会话列表
   */
  async loadConversationList() {
    try {
      this.setData({ isLoading: true })
      
      const result = await messageService.getConversationList()
      
      const processedList = result.list.map(item => ({
        ...item,
        lastMessageTimeText: formatTime(item.lastMessageTime)
      }))
      
      this.setData({
        conversationList: processedList
      })
      
    } catch (error) {
      console.error('加载会话列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 点击会话项
   */
  onConversationTap(e) {
    const { conversation } = e.currentTarget.dataset
    
    wx.navigateTo({
      url: `/pages/conversation/detail/index?conversationId=${conversation.conversationId}&title=${conversation.title}`
    })
  },

  /**
   * 开始新聊天
   */
  onStartNewChat() {
    // 测试功能：创建一个示例聊天
    this.createTestConversation()
  },

  /**
   * 创建测试会话（仅用于演示）
   */
  async createTestConversation() {
    try {
      wx.showLoading({ title: '创建会话...' })
      
      // 这里使用一个测试用户ID，实际应用中应该从用户选择界面获取
      const targetUserId = 2 // 测试用户ID
      
      const result = await messageService.createConversation(targetUserId)
      const conversationId = result.data.conversationId
      
      wx.hideLoading()
      
      // 跳转到聊天页面
      wx.navigateTo({
        url: `/pages/conversation/detail/index?conversationId=${conversationId}&title=测试用户`
      })
      
    } catch (error) {
      wx.hideLoading()
      console.error('创建测试会话失败:', error)
      wx.showToast({
        title: '创建会话失败',
        icon: 'none'
      })
    }
  }
}) 