package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.CommunityPhone;

import java.util.List;

/**
 * 社区服务-常用电话Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface ICommunityPhoneService 
{
    /**
     * 查询社区服务-常用电话
     * 
     * @param id 社区服务-常用电话主键
     * @return 社区服务-常用电话
     */
    public CommunityPhone selectCommunityPhoneById(Long id);

    /**
     * 查询社区服务-常用电话列表
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 社区服务-常用电话集合
     */
    public List<CommunityPhone> selectCommunityPhoneList(CommunityPhone communityPhone);

    /**
     * 新增社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    public int insertCommunityPhone(CommunityPhone communityPhone);

    /**
     * 修改社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    public int updateCommunityPhone(CommunityPhone communityPhone);

    /**
     * 批量删除社区服务-常用电话
     * 
     * @param ids 需要删除的社区服务-常用电话主键集合
     * @return 结果
     */
    public int deleteCommunityPhoneByIds(Long[] ids);

    /**
     * 删除社区服务-常用电话信息
     * 
     * @param id 社区服务-常用电话主键
     * @return 结果
     */
    public int deleteCommunityPhoneById(Long id);
    
    /**
     * 根据用户所在位置查询适用的电话列表
     * 如果参数为空，则返回所有可用的电话
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 电话列表
     */
    public List<CommunityPhone> selectCommunityPhoneListByLocation(Long regionId, Long communityId, Long residentialId);
    
    /**
     * 根据提交用户ID查询电话列表
     *
     * @param submitUserId 提交用户ID
     * @return 电话列表
     */
    public List<CommunityPhone> selectCommunityPhoneListBySubmitUserId(Long submitUserId);
    
    /**
     * 审核电话
     *
     * @param id 电话ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditCommunityPhone(Long id, Integer auditStatus, Long auditUserId, String auditRemark);
    
    /**
     * 批量审核电话
     *
     * @param ids 电话ID列表
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int batchAuditCommunityPhone(List<Long> ids, Integer auditStatus, Long auditUserId, String auditRemark);
    
    /**
     * 用户提交电话信息
     *
     * @param communityPhone 电话信息
     * @param submitUserId 提交用户ID
     * @return 结果
     */
    public int submitCommunityPhone(CommunityPhone communityPhone, Long submitUserId);
    
    /**
     * 更新拨打次数
     *
     * @param id 电话ID
     * @return 结果
     */
    public int incrementCallCount(Long id);
    
    /**
     * 根据分类统计电话数量
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param residentialId 小区ID
     * @return 统计结果
     */
    public List<CommunityPhone> getPhoneStatsByCategory(Long regionId, Long communityId, Long residentialId);
}
