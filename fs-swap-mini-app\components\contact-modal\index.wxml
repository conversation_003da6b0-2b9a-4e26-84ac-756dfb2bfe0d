<!-- components/contact-modal/index.wxml -->
<!-- 联系方式弹窗 -->
<view class="contact-modal" wx:if="{{show}}">
  <view class="contact-modal-mask" bindtap="closeModal"></view>
  <view class="contact-modal-content">
    <view class="contact-modal-header">
      <text class="contact-modal-title">{{targetNickname || '用户'}}</text>
      <view class="contact-modal-close" bindtap="closeModal">×</view>
    </view>
    <view class="contact-modal-body">
      <block wx:if="{{contacts && contacts.length > 0}}">
        <block wx:for="{{contacts}}" wx:key="type">
          <view class="contact-item" wx:if="{{item.type === 1}}">
            <text class="contact-label">手机号：</text>
            <text class="contact-value">{{item.value}}</text>
            <view class="contact-call" data-phone="{{item.value}}" bindtap="onCallPhone">
              <van-icon name="phone-o" size="18px" />
            </view>
          </view>
          <view class="contact-item" wx:elif="{{item.type === 2}}">
            <text class="contact-label">微信号：</text>
            <text class="contact-value">{{item.value}}</text>
            <view class="contact-copy" data-type="{{item.type}}" data-content="{{item.value}}" bindtap="onCopyContact">
              <van-icon name="description-o" size="16px" />
            </view>
          </view>
          <view class="contact-item" wx:elif="{{item.type === 3}}">
            <text class="contact-label">微信二维码：</text>
            <text class="contact-value">点击查看</text>
            <view class="contact-preview" data-type="{{item.type}}" data-content="{{item.value}}" bindtap="onCopyContact">
              <van-icon name="photo-o" size="18px" color="#1989fa" />
            </view>
          </view>
        </block>
      </block>
      <view wx:else class="empty-contacts">
        该用户未设置联系方式
      </view>
    </view>
    <view class="contact-modal-footer">
      <button class="contact-modal-btn" bindtap="closeModal">关闭</button>
    </view>
  </view>
</view>

<!-- 微信二维码弹窗 -->
<view class="qrcode-modal" wx:if="{{showQrcodeModal}}">
  <view class="qrcode-modal-mask" bindtap="closeQrcodeModal"></view>
  <view class="qrcode-modal-content">
    <view class="qrcode-modal-header">
      <text class="qrcode-modal-title">微信二维码</text>
      <view class="qrcode-modal-close" bindtap="closeQrcodeModal">×</view>
    </view>
    <view class="qrcode-modal-body">
      <image class="qrcode-image" src="{{qrcodeImageUrl}}" mode="aspectFit" show-menu-by-longpress="true"></image>
      <view class="qrcode-tip">长按二维码图片添加微信好友</view>
    </view>
  </view>
</view>
