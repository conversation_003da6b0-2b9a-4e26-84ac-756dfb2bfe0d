import { listAllRegion } from '@/api/system/region';
import { listAllCommunity } from '@/api/system/community';
import { listAllArea } from '@/api/system/area';

// 缓存版本控制
const CACHE_VERSION = '1.0.0';
const CACHE_VERSION_KEY = 'area_data_cache_version';

// 缓存键名
const CACHE_KEYS = {
  REGIONS: 'cached_regions',
  COMMUNITIES: 'cached_communities',
  RESIDENTIALS: 'cached_residentials',
  TIMESTAMP: 'area_data_timestamp',
};

// 缓存过期时间（毫秒）- 默认1小时
const CACHE_EXPIRATION = 60 * 60 * 1000;

/**
 * 区域数据服务
 * 负责加载、缓存和管理区域、社区、小区数据
 */
class AreaDataService {
  constructor() {
    // 加载状态
    this.loading = {
      regions: false,
      communities: false,
      residentials: false,
      all: false
    };

    // 数据缓存
    this.cache = {
      regions: [],
      communities: [],
      residentials: [],
      regionMap: new Map(),
      communityMap: new Map(),
      residentialMap: new Map()
    };

    // 加载Promise
    this.loadPromises = {
      regions: null,
      communities: null,
      residentials: null,
      all: null
    };

    // 初始化
    this.init();
  }

  /**
   * 初始化服务
   */
  init() {
    // 检查缓存版本
    this.checkCacheVersion();

    // 从localStorage加载缓存数据
    this.loadCachedData();
  }

  /**
   * 检查缓存版本，如果版本不匹配则清除缓存
   */
  checkCacheVersion() {
    const cachedVersion = localStorage.getItem(CACHE_VERSION_KEY);

    if (cachedVersion !== CACHE_VERSION) {
      // 清除所有缓存
      Object.values(CACHE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });

      // 更新缓存版本
      localStorage.setItem(CACHE_VERSION_KEY, CACHE_VERSION);
    }
  }

  /**
   * 从localStorage加载缓存数据
   */
  loadCachedData() {
    try {

      // 检查缓存是否过期
      const timestamp = localStorage.getItem(CACHE_KEYS.TIMESTAMP);
      const now = Date.now();

      if (!timestamp) {
        return;
      }

      const age = now - parseInt(timestamp);
      console.log(`缓存时间戳: ${new Date(parseInt(timestamp)).toLocaleString()}, 当前时间: ${new Date(now).toLocaleString()}, 缓存年龄: ${Math.floor(age / 1000 / 60)} 分钟`);

      if (age < CACHE_EXPIRATION) {
        console.log('缓存未过期，尝试加载缓存数据');

        // 加载区域数据
        const regionsStr = localStorage.getItem(CACHE_KEYS.REGIONS);
        if (regionsStr) {
          const regions = JSON.parse(regionsStr);

          if (regions.length > 0) {
            this.cache.regions = regions;
            this.cache.regionMap = new Map(regions.map(r => [r.id, r]));
          }
        } else {
          console.log('缓存中没有区域数据');
        }

        // 加载社区数据
        const communitiesStr = localStorage.getItem(CACHE_KEYS.COMMUNITIES);
        if (communitiesStr) {
          const communities = JSON.parse(communitiesStr);
          console.log(`从缓存加载社区数据，数量: ${communities.length}`);

          if (communities.length > 0) {
            this.cache.communities = communities;
            this.cache.communityMap = new Map(communities.map(c => [c.id, c]));
            console.log('社区数据加载成功，Map大小:', this.cache.communityMap.size);
          }
        } else {
          console.log('缓存中没有社区数据');
        }

        // 加载小区数据
        const residentialsStr = localStorage.getItem(CACHE_KEYS.RESIDENTIALS);
        if (residentialsStr) {
          const residentials = JSON.parse(residentialsStr);
          console.log(`从缓存加载小区数据，数量: ${residentials.length}`);

          if (residentials.length > 0) {
            this.cache.residentials = residentials;
            this.cache.residentialMap = new Map(residentials.map(r => [r.id, r]));
            console.log('小区数据加载成功，Map大小:', this.cache.residentialMap.size);
          }
        } else {
          console.log('缓存中没有小区数据');
        }

        console.log('缓存数据加载完成');
      } else {
        console.log(`缓存已过期 (${Math.floor(age / 1000 / 60)} 分钟)，需要重新加载数据`);
      }
    } catch (error) {
      console.error('加载缓存数据失败:', error);
      // 清除可能损坏的缓存
      this.clearCache();
    }
  }

  /**
   * 清除所有缓存数据
   */
  clearCache() {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });

    this.cache = {
      regions: [],
      communities: [],
      residentials: [],
      regionMap: new Map(),
      communityMap: new Map(),
      residentialMap: new Map()
    };
  }

  /**
   * 保存数据到缓存
   */
  saveToCache() {
    try {
      // 保存时间戳
      localStorage.setItem(CACHE_KEYS.TIMESTAMP, Date.now().toString());

      // 保存区域数据
      if (this.cache.regions.length > 0) {
        localStorage.setItem(CACHE_KEYS.REGIONS, JSON.stringify(this.cache.regions));
      }

      // 保存社区数据
      if (this.cache.communities.length > 0) {
        localStorage.setItem(CACHE_KEYS.COMMUNITIES, JSON.stringify(this.cache.communities));
      }

      // 保存小区数据
      if (this.cache.residentials.length > 0) {
        localStorage.setItem(CACHE_KEYS.RESIDENTIALS, JSON.stringify(this.cache.residentials));
      }
    } catch (error) {
      console.error('保存缓存数据失败:', error);
    }
  }

  /**
   * 加载所有区域数据
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise} 加载完成的Promise
   */
  async loadAllData(forceRefresh = false) {
    console.log(`开始加载所有区域数据, forceRefresh=${forceRefresh}`);

    // 检查缓存中是否已有数据
    const hasRegions = this.cache.regions.length > 0;
    const hasCommunities = this.cache.communities.length > 0;
    const hasResidentials = this.cache.residentials.length > 0;

    console.log('当前缓存状态:', {
      hasRegions,
      hasCommunities,
      hasResidentials
    });

    // 如果已经在加载中，返回现有Promise
    if (this.loadPromises.all && !forceRefresh) {
      console.log('数据正在加载中，返回现有Promise');
      return this.loadPromises.all;
    }

    // 如果已有数据且不强制刷新，直接返回
    if (hasRegions && hasCommunities && hasResidentials && !forceRefresh) {
      console.log('缓存中已有所有数据，跳过加载');
      return Promise.resolve({
        regions: this.cache.regions,
        communities: this.cache.communities,
        residentials: this.cache.residentials
      });
    }

    // 如果强制刷新，清除现有缓存
    if (forceRefresh) {
      console.log('强制刷新，清除现有缓存');
      this.clearCache();
    }

    // 标记为加载中
    this.loading.all = true;
    console.log('开始从服务器加载数据...');

    // 创建新的加载Promise
    this.loadPromises.all = Promise.all([
      this.loadRegions(forceRefresh),
      this.loadCommunities(forceRefresh),
      this.loadResidentials(forceRefresh)
    ]).then(results => {
      console.log('所有数据加载完成:', {
        regions: results[0].length,
        communities: results[1].length,
        residentials: results[2].length
      });
      return {
        regions: results[0],
        communities: results[1],
        residentials: results[2]
      };
    }).finally(() => {
      this.loading.all = false;
    });

    return this.loadPromises.all;
  }

  /**
   * 加载区域数据
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 区域数据
   */
  async loadRegions(forceRefresh = false) {
    console.log(`开始加载区域数据, forceRefresh=${forceRefresh}, 当前缓存数量=${this.cache.regions.length}`);

    // 如果已有数据且不强制刷新，直接返回
    if (this.cache.regions.length > 0 && !forceRefresh) {
      console.log('缓存中已有区域数据，跳过加载');
      return Promise.resolve(this.cache.regions);
    }

    // 如果已经在加载中，返回现有Promise
    if (this.loadPromises.regions && !forceRefresh) {
      console.log('区域数据正在加载中，返回现有Promise');
      return this.loadPromises.regions;
    }

    // 标记为加载中
    this.loading.regions = true;
    console.log('开始从服务器加载区域数据...');

    // 创建新的加载Promise
    this.loadPromises.regions = listAllRegion()
      .then(response => {
        console.log('区域数据API响应:', response ? '成功' : '失败');

        // 处理不同的响应格式
        let regions = [];
        if (response.data && Array.isArray(response.data)) {
          regions = response.data;
          console.log('从response.data中提取区域数据');
        } else if (response.rows && Array.isArray(response.rows)) {
          regions = response.rows;
          console.log('从response.rows中提取区域数据');
        } else if (Array.isArray(response)) {
          regions = response;
          console.log('响应本身是区域数据数组');
        } else {
          console.warn('无法从响应中提取区域数据:', response);
        }

        console.log('加载区域数据成功，数量:', regions.length);
        if (regions.length > 0) {
          console.log('区域数据示例:', regions.slice(0, 3));
        }

        // 确保每个区域对象都有必要的属性
        regions = regions.map(region => ({
          id: Number(region.id),
          name: region.name,
          type: Number(region.type || 0),
          pid: Number(region.pid || 0),
          code: Number(region.code || 0)
        }));

        // 更新缓存
        this.cache.regions = regions;
        this.cache.regionMap = new Map(regions.map(r => [r.id, r]));
        console.log('区域Map创建完成，大小:', this.cache.regionMap.size);

        // 保存到localStorage
        this.saveToCache();
        console.log('区域数据已保存到localStorage');

        return regions;
      })
      .catch(error => {
        console.error('加载区域数据失败:', error);
        throw error;
      })
      .finally(() => {
        this.loading.regions = false;
        console.log('区域数据加载完成');
      });

    return this.loadPromises.regions;
  }

  /**
   * 加载社区数据
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 社区数据
   */
  async loadCommunities(forceRefresh = false) {
    // 如果已有数据且不强制刷新，直接返回
    if (this.cache.communities.length > 0 && !forceRefresh) {
      return Promise.resolve(this.cache.communities);
    }

    // 如果已经在加载中，返回现有Promise
    if (this.loadPromises.communities && !forceRefresh) {
      return this.loadPromises.communities;
    }

    // 标记为加载中
    this.loading.communities = true;

    // 创建新的加载Promise
    this.loadPromises.communities = listAllCommunity()
      .then(response => {
        // 处理不同的响应格式
        let communities = [];
        if (response.data && Array.isArray(response.data)) {
          communities = response.data;
        } else if (response.rows && Array.isArray(response.rows)) {
          communities = response.rows;
        } else if (Array.isArray(response)) {
          communities = response;
        }

        console.log('加载社区数据成功，数量:', communities.length);
        console.log('社区数据示例:', communities.slice(0, 3));

        // 确保每个社区对象都有必要的属性
        communities = communities.map(community => ({
          id: Number(community.id),
          name: community.name,
          regionId: Number(community.regionId || 0),
          code: community.code,
          status: Number(community.status || 0)
        }));

        // 更新缓存
        this.cache.communities = communities;
        this.cache.communityMap = new Map(communities.map(c => [c.id, c]));
        console.log('社区Map创建完成，大小:', this.cache.communityMap.size);

        // 保存到localStorage
        this.saveToCache();

        return communities;
      })
      .catch(error => {
        console.error('加载社区数据失败:', error);
        throw error;
      })
      .finally(() => {
        this.loading.communities = false;
      });

    return this.loadPromises.communities;
  }

  /**
   * 加载小区数据
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 小区数据
   */
  async loadResidentials(forceRefresh = false) {
    // 如果已有数据且不强制刷新，直接返回
    if (this.cache.residentials.length > 0 && !forceRefresh) {
      return Promise.resolve(this.cache.residentials);
    }

    // 如果已经在加载中，返回现有Promise
    if (this.loadPromises.residentials && !forceRefresh) {
      return this.loadPromises.residentials;
    }

    // 标记为加载中
    this.loading.residentials = true;

    // 创建新的加载Promise
    this.loadPromises.residentials = listAllArea()
      .then(response => {
        // 处理不同的响应格式
        let residentials = [];
        if (response.data && Array.isArray(response.data)) {
          residentials = response.data;
        } else if (response.rows && Array.isArray(response.rows)) {
          residentials = response.rows;
        } else if (Array.isArray(response)) {
          residentials = response;
        }

        console.log('加载小区数据成功，数量:', residentials.length);
        console.log('小区数据示例:', residentials.slice(0, 3));

        // 确保每个小区对象都有必要的属性
        residentials = residentials.map(residential => ({
          id: Number(residential.id),
          name: residential.name,
          communityId: Number(residential.communityId || 0),
          regionId: Number(residential.regionId || 0),
          address: residential.address,
          status: Number(residential.status || 0)
        }));

        // 更新缓存
        this.cache.residentials = residentials;
        this.cache.residentialMap = new Map(residentials.map(r => [r.id, r]));
        console.log('小区Map创建完成，大小:', this.cache.residentialMap.size);

        // 保存到localStorage
        this.saveToCache();

        return residentials;
      })
      .catch(error => {
        console.error('加载小区数据失败:', error);
        throw error;
      })
      .finally(() => {
        this.loading.residentials = false;
      });

    return this.loadPromises.residentials;
  }

  /**
   * 根据区域ID获取社区列表
   * @param {number} regionId 区域ID
   * @returns {Array} 社区列表
   */
  getCommunityListByRegionId(regionId) {
    if (!regionId) return [];
    return this.cache.communities.filter(c => c.regionId === Number(regionId));
  }

  /**
   * 根据社区ID获取小区列表
   * @param {number} communityId 社区ID
   * @returns {Array} 小区列表
   */
  getResidentialListByCommunityId(communityId) {
    if (!communityId) return [];
    return this.cache.residentials.filter(r => r.communityId === Number(communityId));
  }

  /**
   * 获取区域数据
   * @returns {Array} 区域数据
   */
  getRegions() {
    return this.cache.regions;
  }

  /**
   * 获取社区数据
   * @returns {Array} 社区数据
   */
  getCommunities() {
    return this.cache.communities;
  }

  /**
   * 获取小区数据
   * @returns {Array} 小区数据
   */
  getResidentials() {
    return this.cache.residentials;
  }

  /**
   * 获取加载状态
   * @returns {Object} 加载状态对象
   */
  getLoadingStatus() {
    return { ...this.loading };
  }

  /**
   * 根据ID获取区域信息
   * @param {number} id 区域ID
   * @returns {Object|null} 区域信息
   */
  getRegionById(id) {
    if (!id) return null;

    const numId = Number(id);
    const region = this.cache.regionMap.get(numId);

    if (!region) {
      console.warn(`未找到ID为${numId}的区域信息，当前区域Map大小:`, this.cache.regionMap.size);
      // 尝试从数组中查找
      const foundRegion = this.cache.regions.find(r => r.id === numId);
      if (foundRegion) {
        console.log(`从数组中找到ID为${numId}的区域信息:`, foundRegion);
        return foundRegion;
      }
    }

    return region || null;
  }

  /**
   * 获取区域的完整路径（省/市/区/街道）
   * @param {number} id 区域ID
   * @returns {Array} 区域路径数组，从省到当前区域
   */
  getRegionPath(id) {
    if (!id) return [];

    const region = this.getRegionById(id);
    if (!region) {
      console.warn(`获取区域路径失败: 未找到ID为${id}的区域`);
      return [];
    }


    const path = [region];

    // 如果不是省级，递归查找父级
    if (region.pid && region.pid !== 0) {
      const parentPath = this.getRegionPath(region.pid);
      return [...parentPath, region];
    }

    return path;
  }

  /**
   * 获取区域的完整路径名称（省/市/区/街道）
   * @param {number} id 区域ID
   * @returns {string} 完整路径名称，如"广东省/深圳市/南山区"
   */
  getRegionFullName(id) {
    if (!id) return '';

    const path = this.getRegionPath(id);

    if (!path.length) {
      console.warn(`获取区域完整路径名称失败: 路径为空`);
      return '';
    }

    const fullName = path.map(region => region.name).join('/');

    return fullName;
  }

  /**
   * 根据ID获取社区信息
   * @param {number} id 社区ID
   * @returns {Object|null} 社区信息
   */
  getCommunityById(id) {
    if (!id) return null;

    const numId = Number(id);
    const community = this.cache.communityMap.get(numId);

    if (!community) {
      console.warn(`未找到ID为${numId}的社区信息，当前社区Map大小:`, this.cache.communityMap.size);
      // 尝试从数组中查找
      const foundCommunity = this.cache.communities.find(c => c.id === numId);
      if (foundCommunity) {
        console.log(`从数组中找到ID为${numId}的社区信息:`, foundCommunity);
        return foundCommunity;
      }
    }

    return community || null;
  }

  /**
   * 根据ID获取小区信息
   * @param {number} id 小区ID
   * @returns {Object|null} 小区信息
   */
  getResidentialById(id) {
    if (!id) return null;

    const numId = Number(id);
    const residential = this.cache.residentialMap.get(numId);

    if (!residential) {
      // 尝试从数组中查找
      const foundResidential = this.cache.residentials.find(r => r.id === numId);
      if (foundResidential) {
        return foundResidential;
      }
    }

    return residential || null;
  }
}

// 创建单例实例
const areaDataService = new AreaDataService();

export default areaDataService;
