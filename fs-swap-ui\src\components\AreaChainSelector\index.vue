<template>
  <div class="area-chain-selector">
    <div :class="{ 'area-selector-row': inlineLayout }">
      <!-- 区域选择 -->
      <el-form-item :label="labels.region" :prop="props.region">
        <el-cascader
          v-model="selectedRegionPath"
          :options="regionOptions"
          :props="cascaderProps"
          :placeholder="placeholders.region"
          :clearable="true"
          :filterable="true"
          :show-all-levels="true"
          :loading="loading"
          @change="handleRegionPathChange"
        >
          <template slot="empty">
            <div v-if="loading" class="el-cascader-menu__empty-text">
              <i class="el-icon-loading"></i> 加载中...
            </div>
            <div v-else class="el-cascader-menu__empty-text">
              暂无数据
            </div>
          </template>
        </el-cascader>
      </el-form-item>

      <!-- 社区选择 -->
      <el-form-item v-if="showCommunity" :label="labels.community" :prop="props.community">
        <el-select
          v-model="selectedCommunityId"
          :placeholder="placeholders.community"
          filterable
          clearable
          :loading="loading"
          :disabled="!selectedRegionId"
          @change="handleCommunityChange"
        >
          <el-option
            v-for="option in communityOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 小区选择 -->
      <el-form-item v-if="showResidential" :label="labels.residential" :prop="props.residential">
        <el-select
          v-model="selectedResidentialId"
          :placeholder="placeholders.residential"
          filterable
          clearable
          :loading="loading"
          :disabled="!selectedCommunityId"
          @change="handleResidentialChange"
        >
          <el-option
            v-for="option in residentialOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="area-selector-error">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script>
import areaDataService from '@/services/areaDataService'

export default {
  name: 'AreaChainSelector',

  components: {},

  props: {
    // 外部传入的值
    regionId: {
      type: [Number, String],
      default: null
    },
    communityId: {
      type: [Number, String],
      default: null
    },
    residentialId: {
      type: [Number, String],
      default: null
    },

    // 表单属性名
    props: {
      type: Object,
      default: () => ({
        region: 'regionId',
        community: 'communityId',
        residential: 'residentialId'
      })
    },

    // 自定义标签
    labels: {
      type: Object,
      default: () => ({
        region: '行政区域',
        community: '社区',
        residential: '小区'
      })
    },

    // 自定义占位符
    placeholders: {
      type: Object,
      default: () => ({
        region: '请选择行政区域',
        community: '请选择社区',
        residential: '请选择小区'
      })
    },

    // 是否显示社区选择器
    showCommunity: {
      type: Boolean,
      default: true
    },

    // 是否显示小区选择器
    showResidential: {
      type: Boolean,
      default: true
    },

    // 是否正在加载表单数据
    isLoading: {
      type: Boolean,
      default: false
    },

    // 是否显示刷新按钮
    showRefreshButton: {
      type: Boolean,
      default: true
    },

    // 是否使用内联布局（将选择器放在同一行）
    inlineLayout: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      // 选中的值
      selectedRegionId: null,
      selectedCommunityId: null,
      selectedResidentialId: null,
      selectedRegionPath: [], // 级联选择器选中的路径

      // 加载状态
      loading: false,
      error: null,

      // 内部状态
      internalLoading: false,

      // 选项列表
      regionList: [],
      communityList: [],
      residentialList: [],

      // 缓存
      cachedRegionOptions: null,
      regionListVersion: 0,

      // 级联选择器配置
      cascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: false,
        emitPath: true,
        expandTrigger: 'click',
        lazy: false,
        multiple: false
      }
    }
  },

  computed: {
    // 区域选项（级联选择器数据源）
    regionOptions() {
      return this.cachedRegionOptions || []
    },

    // 社区选项
    communityOptions() {
      if (!this.selectedRegionId) return []

      return this.communityList
        .filter(item => item.regionId === Number(this.selectedRegionId))
        .map(item => ({
          label: item.name,
          value: item.id
        }))
    },

    // 小区选项
    residentialOptions() {
      if (!this.selectedCommunityId) return []

      return this.residentialList
        .filter(item => item.communityId === Number(this.selectedCommunityId))
        .map(item => ({
          label: item.name,
          value: item.id
        }))
    }
  },

  watch: {
    // 监听外部传入的值变化
    regionId(newVal) {
      if (!this.isLoading && newVal !== this.selectedRegionId) {
        this.selectedRegionId = newVal ? Number(newVal) : null
      }
    },

    communityId(newVal) {
      if (!this.isLoading && newVal !== this.selectedCommunityId) {
        this.selectedCommunityId = newVal ? Number(newVal) : null
      }
    },

    residentialId(newVal) {
      if (!this.isLoading && newVal !== this.selectedResidentialId) {
        this.selectedResidentialId = newVal ? Number(newVal) : null
      }
    },

    // 监听显示配置变化
    showCommunity(newVal) {
      if (!newVal) {
        // 如果不显示社区选择器，清空社区和小区选择
        this.selectedCommunityId = null
        this.selectedResidentialId = null
      }
    },

    showResidential(newVal) {
      if (!newVal) {
        // 如果不显示小区选择器，清空小区选择
        this.selectedResidentialId = null
      }
    },

    // 监听内部选择值变化，同步到外部
    selectedRegionId(newVal) {
      this.$emit('update:regionId', newVal)

      if (!this.internalLoading && newVal !== this.regionId) {
        // 清空社区和小区选择
        this.selectedCommunityId = null
        this.selectedResidentialId = null
        this.$emit('region-change', newVal)
      }
    },

    // 监听级联选择器路径变化
    selectedRegionPath(newVal) {
      if (this.internalLoading) return

      if (!newVal || newVal.length === 0) {
        // 清空选择
        this.selectedRegionId = null
        return
      }

      // 获取路径中的最后一个ID作为选中的区域ID
      const regionId = newVal[newVal.length - 1]
      if (regionId !== this.selectedRegionId) {
        this.selectedRegionId = regionId
      }
    },

    selectedCommunityId(newVal) {
      this.$emit('update:communityId', newVal)

      if (!this.internalLoading && newVal !== this.communityId) {
        // 清空小区选择
        this.selectedResidentialId = null
        this.$emit('community-change', newVal)
      }
    },

    selectedResidentialId(newVal) {
      this.$emit('update:residentialId', newVal)

      if (!this.internalLoading && newVal !== this.residentialId) {
        this.$emit('residential-change', newVal)
      }
    },

    // 监听错误状态
    error(newVal) {
      if (newVal) {
        this.$emit('error', newVal)
      }
    },

    // 监听区域数据变化，重新构建缓存
    regionList: {
      handler() {
        this.buildRegionOptionsCache()
      },
      immediate: true
    }
  },

  created() {
    // 加载区域数据
    this.loadData()

    // 如果不显示社区或小区选择器，确保相应的值为空
    if (!this.showCommunity) {
      this.selectedCommunityId = null
      this.selectedResidentialId = null
    } else if (!this.showResidential) {
      this.selectedResidentialId = null
    }
  },

  methods: {
    /**
     * 加载区域数据
     * @param {boolean} forceRefresh 是否强制刷新
     * @returns {Promise} 加载完成的Promise
     */
    async loadData(forceRefresh = false) {
      if (this.loading && !forceRefresh) {
        return
      }

      this.loading = true
      this.error = null

      try {
        // 加载区域数据
        const regionsPromise = areaDataService.loadRegions(forceRefresh)
        const communitiesPromise = areaDataService.loadCommunities(forceRefresh)
        const residentialsPromise = areaDataService.loadResidentials(forceRefresh)

        // 等待所有数据加载完成
        const results = await Promise.allSettled([regionsPromise, communitiesPromise, residentialsPromise])
        
        // 检查加载结果
        const failures = results.filter(result => result.status === 'rejected')
        if (failures.length > 0) {
          const errorDetails = failures.map(f => f.reason?.message || '未知错误').join(', ')
          throw new Error(`部分数据加载失败: ${errorDetails}`)
        }

        // 更新本地数据
        this.regionList = areaDataService.getRegions()
        this.communityList = areaDataService.getCommunities()
        this.residentialList = areaDataService.getResidentials()

        // 验证数据完整性
        if (this.regionList.length === 0) {
          throw new Error('区域数据为空，请检查数据源')
        }

        return true
      } catch (err) {
        console.error('加载区域数据失败:', err)
        this.error = err.message || '加载区域数据失败，请稍后重试'
        this.$emit('load-error', err)
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      return this.loadData(true)
    },

    /**
     * 区域路径变更事件处理
     */
    handleRegionPathChange() {
      if (this.isLoading) return

      // 级联选择器的值已经通过 watch 处理，这里不需要额外处理
    },

    /**
     * 社区变更事件处理
     */
    handleCommunityChange(val) {
      if (this.isLoading) return

      this.selectedCommunityId = val
    },

    /**
     * 小区变更事件处理
     */
    handleResidentialChange(val) {
      if (this.isLoading) return

      this.selectedResidentialId = val
    },

    /**
     * 清空所有选择
     */
    clearAll() {
      this.selectedRegionId = null
      this.selectedCommunityId = null
      this.selectedResidentialId = null
    },

    /**
     * 批量设置区域值，减少中间状态更新
     * @param {Number|null} regionId - 行政区域ID
     * @param {Number|null} communityId - 社区ID
     * @param {Number|null} residentialId - 小区ID
     */
    async batchSetAreaValues(regionId, communityId, residentialId) {
      // 临时禁用watch
      const tempInternalLoading = this.internalLoading
      this.internalLoading = true

      try {
        // 设置区域ID但不触发后续级联
        if (regionId !== null) {
          this.selectedRegionId = regionId
        }

        // 等待DOM更新完成
        await this.$nextTick()

        // 设置社区ID
        if (communityId !== null) {
          this.selectedCommunityId = communityId
        }

        // 等待DOM更新完成
        await this.$nextTick()

        // 设置小区ID
        if (residentialId !== null) {
          this.selectedResidentialId = residentialId
        }

        // 最终等待所有更新完成
        await this.$nextTick()
      } finally {
        // 恢复原始状态
        this.internalLoading = tempInternalLoading
      }
    },

    /**
     * 为编辑模式初始化区域选择器
     * @param {Object} data 包含区域、社区、小区ID的数据对象
     * @returns {Promise<boolean>} 是否初始化成功
     */
    async initForEdit(data = {}) {
      try {
        // 标记为内部加载状态，防止级联清空
        this.internalLoading = true

        // 首先清空所有选择，确保不会显示上次的数据
        this.selectedRegionId = null
        this.selectedCommunityId = null
        this.selectedResidentialId = null
        this.selectedRegionPath = []

        // 等待DOM更新，确保清空生效
        await this.$nextTick()

        // 检查数据是否已加载，如果未加载则加载
        if (this.regionList.length === 0 || this.communityList.length === 0 || this.residentialList.length === 0) {
          await this.loadData(false) // 不强制刷新
        }

        // 批量设置值
        if (data.regionId) {
          const numRegionId = Number(data.regionId)

          // 获取区域路径
          const path = areaDataService.getRegionPath(numRegionId)

          if (path && path.length > 0) {
            // 设置级联选择器路径
            this.selectedRegionPath = path.map(item => item.id)
          } else {
            // 如果无法获取路径，直接设置区域ID
            this.selectedRegionId = numRegionId
          }

          // 等待DOM更新
          await this.$nextTick()

          // 确保区域ID已正确设置
          if (!this.selectedRegionId) {
            this.selectedRegionId = numRegionId
            // 再次等待DOM更新
            await this.$nextTick()
          }

          // 只有在显示社区选择器时才设置社区ID
          if (this.showCommunity && data.communityId) {
            const numCommunityId = Number(data.communityId)

            // 确保社区选项已加载
            const communityOptions = this.communityOptions

            // 如果社区选项为空，可能是因为区域ID没有正确设置
            if (communityOptions.length === 0) {
              // 获取该社区所属的区域ID
              const community = areaDataService.getCommunityById(numCommunityId)
              if (community && community.regionId) {
                this.selectedRegionId = community.regionId
                // 等待DOM更新，确保社区选项已加载
                await this.$nextTick()
              }
            }

            // 设置社区ID
            this.selectedCommunityId = numCommunityId

            // 等待DOM更新
            await this.$nextTick()

            // 只有在显示小区选择器且有小区ID时才设置小区ID
            if (this.showResidential && data.residentialId) {
              const numResidentialId = Number(data.residentialId)

              // 确保小区选项已加载
              const residentialOptions = this.residentialOptions

              // 如果小区选项为空，可能是因为社区ID没有正确设置
              if (residentialOptions.length === 0) {
                // 获取该小区所属的社区ID
                const residential = areaDataService.getResidentialById(numResidentialId)
                if (residential && residential.communityId) {
                  this.selectedCommunityId = residential.communityId
                  // 等待DOM更新，确保小区选项已加载
                  await this.$nextTick()
                }
              }

              // 设置小区ID
              this.selectedResidentialId = numResidentialId
            } else {
              // 如果没有小区ID，确保小区选择器为空
              this.selectedResidentialId = null
            }
          } else {
            // 如果没有社区ID，确保社区和小区选择器为空
            this.selectedCommunityId = null
            this.selectedResidentialId = null
          }
        } else {
          // 如果没有区域ID，确保所有选择器为空
          this.selectedRegionId = null
          this.selectedCommunityId = null
          this.selectedResidentialId = null
          this.selectedRegionPath = []
        }

        return true
      } catch (err) {
        return false
      } finally {
        // 立即恢复状态，不使用延迟
        this.internalLoading = false
      }
    },

    /**
     * 构建区域选项缓存
     */
    buildRegionOptionsCache() {
      try {
        // 如果数据为空，清空缓存
        if (!this.regionList || this.regionList.length === 0) {
          this.cachedRegionOptions = []
          this.regionListVersion = 0
          return
        }

        // 构建树形结构（使用Map提升查找性能）
        const regionMap = new Map()
        this.regionList.forEach(item => {
          if (item && item.id) {
            regionMap.set(item.id, { ...item, children: [] })
          }
        })

        // 构建父子关系
        const roots = []
        this.regionList.forEach(item => {
          if (!item || !item.id) return
          
          const node = regionMap.get(item.id)
          if (!node) return

          if (item.pid && regionMap.has(item.pid)) {
            regionMap.get(item.pid).children.push(node)
          } else if (item.type === 1) { // 省级
            roots.push(node)
          }
        })

        // 转换为级联选择器格式并标记叶子节点
        const convertNode = (node) => ({
          id: node.id,
          name: node.name || '未知',
          children: node.children.length > 0 ? node.children.map(convertNode) : null,
          leaf: node.children.length === 0
        })

        const result = roots.map(convertNode)

        // 缓存结果
        this.cachedRegionOptions = result
        this.regionListVersion = this.regionList.length
      } catch (error) {
        console.error('构建区域选项缓存失败:', error)
        this.cachedRegionOptions = []
        this.regionListVersion = 0
      }
    }
  }
}
</script>

<style scoped>
.area-chain-selector {
  position: relative;
}

/* 内联布局样式 */
.area-selector-row {
  display: flex;
  flex-wrap: wrap;
}

.area-selector-row .el-form-item {
  margin-right: 10px;
  margin-bottom: 18px;
}

.area-selector-error {
  margin-top: 10px;
  margin-bottom: 10px;
}

/* 添加过渡效果 */
.el-select-dropdown__item {
  transition: background-color 0.2s ease;
}

/* 自定义下拉菜单样式 */
:deep(.el-select-dropdown__item) {
  padding: 8px 20px;
}

/* 自定义加载状态样式 */
:deep(.el-select.is-loading .el-input__inner) {
  transition: border-color 0.3s;
}

/* 禁用状态样式优化 */
:deep(.el-select.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  color: #909399;
}

/* 级联选择器样式 */
:deep(.el-cascader) {
  width: 100%;
}
</style>