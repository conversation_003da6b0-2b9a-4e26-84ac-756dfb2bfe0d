import { listAllArea } from '@/api/system/area'

// 缓存配置
const CACHE_KEY = 'residential_cache_data'
const CACHE_EXPIRY_KEY = 'residential_cache_expiry'
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24小时，单位毫秒

const state = {
  residentialCache: {}, // 存储小区数据的缓存
  loadingResidentials: false,
  allResidentialsLoaded: false // 标记是否已加载所有小区数据
}

const mutations = {
  SET_RESIDENTIAL_CACHE(state, { key, data }) {
    state.residentialCache = { ...state.residentialCache, [key]: data }
  },
  SET_RESIDENTIAL_CACHE_BULK(state, data) {
    state.residentialCache = data
  },
  SET_LOADING(state, status) {
    state.loadingResidentials = status
  },
  SET_ALL_RESIDENTIALS_LOADED(state, status) {
    state.allResidentialsLoaded = status
  }
}

const actions = {
  // 预加载所有小区数据
  async preloadAllResidentials({ commit, state, dispatch }) {
    // 如果已经加载过，直接返回
    if (state.allResidentialsLoaded) {
      return state.residentialCache
    }

    // 尝试从localStorage获取缓存数据
    const cachedData = await dispatch('loadFromCache')
    if (cachedData) {
      return cachedData
    }

    try {
      commit('SET_LOADING', true)
      // 一次性获取所有小区数据
      const response = await listAllArea()
      if (response && response.rows && Array.isArray(response.rows)) {
        const cacheData = {}
        // 将所有小区数据存入缓存，以ID为键
        response.rows.forEach(item => {
          const residential = {
            id: Number(item.id),
            name: item.name,
            alias: item.alias,
            communityId: Number(item.communityId),
            regionId: Number(item.regionId),
            address: item.address,
            location: item.location,
            status: Number(item.status)
          }
          cacheData[residential.id.toString()] = residential
        })

        // 批量更新缓存
        commit('SET_RESIDENTIAL_CACHE_BULK', cacheData)
        // 标记为已加载所有数据
        commit('SET_ALL_RESIDENTIALS_LOADED', true)

        // 保存到localStorage
        dispatch('saveToCache', cacheData)
      }
      return state.residentialCache
    } catch (error) {
      console.error('预加载所有小区数据失败:', error)
      return state.residentialCache
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 从localStorage加载缓存数据
  loadFromCache({ commit }) {
    try {
      // 检查缓存是否过期
      const expiryTime = localStorage.getItem(CACHE_EXPIRY_KEY)
      const now = new Date().getTime()

      if (!expiryTime || now > parseInt(expiryTime)) {
        // 缓存已过期或不存在
        return null
      }

      // 获取缓存数据
      const cachedData = localStorage.getItem(CACHE_KEY)
      if (cachedData) {
        const parsedData = JSON.parse(cachedData)
        commit('SET_RESIDENTIAL_CACHE_BULK', parsedData)
        commit('SET_ALL_RESIDENTIALS_LOADED', true)
        return parsedData
      }

      return null
    } catch (error) {
      console.error('从缓存加载小区数据失败:', error)
      return null
    }
  },

  // 保存数据到localStorage
  saveToCache({ commit }, data) {
    try {
      // 设置过期时间
      const expiryTime = new Date().getTime() + CACHE_DURATION
      localStorage.setItem(CACHE_EXPIRY_KEY, expiryTime.toString())

      // 保存数据
      localStorage.setItem(CACHE_KEY, JSON.stringify(data))
    } catch (error) {
      console.error('保存小区数据到缓存失败:', error)
    }
  },

  // 清理缓存
  clearCache({ commit }) {
    commit('SET_RESIDENTIAL_CACHE_BULK', {})
    commit('SET_ALL_RESIDENTIALS_LOADED', false)

    // 清除localStorage中的缓存
    try {
      localStorage.removeItem(CACHE_KEY)
      localStorage.removeItem(CACHE_EXPIRY_KEY)
    } catch (error) {
      console.error('清除小区缓存失败:', error)
    }
  }
}

const getters = {
  // 根据社区ID获取小区列表
  getResidentialsByCommunityId: state => (communityId) => {
    // 如果已加载所有数据，从缓存中筛选
    if (state.allResidentialsLoaded) {
      // 确保communityId是数字类型
      const numericCommunityId = Number(communityId);

      return Object.values(state.residentialCache)
        .filter(residential =>
          residential &&
          residential.communityId === numericCommunityId
        )
    }
    return []
  },
  // 是否正在加载
  isLoading: state => state.loadingResidentials
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
