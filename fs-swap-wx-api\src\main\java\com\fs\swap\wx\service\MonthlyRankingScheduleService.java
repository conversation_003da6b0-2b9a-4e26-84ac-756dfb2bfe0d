package com.fs.swap.wx.service;

import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.service.IMonthlyRankingRewardService;
import com.fs.swap.system.service.impl.MonthlyRankingServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 月度排行榜定时同步服务
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class MonthlyRankingScheduleService {
    
    private static final Logger log = LoggerFactory.getLogger(MonthlyRankingScheduleService.class);
    
    @Autowired
    private MonthlyRankingServiceImpl monthlyRankingService;
    
    @Autowired
    private IMonthlyRankingRewardService monthlyRankingRewardService;

    /**
     * 每月1号凌晨0:05，完成上月最终排名并生成奖励
     * 简单直接：0点后就是新月了，处理上月排名和奖励
     * 支持所有小区的排名确定和奖励生成
     */
    @Scheduled(cron = "0 5 0 1 * ?")
    public void finalizeLastMonth() {
        try {
            LocalDate now = LocalDate.now();
            LocalDate lastMonth = now.minusMonths(1);
            String lastMonthStr = lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            log.info("开始处理上月排行榜最终排名和奖励: {}", lastMonthStr);
            // 3. 生成所有小区的奖励记录
            int rewardCount = monthlyRankingRewardService.generateMonthlyRewards(lastMonthStr);
            
            log.info("上月排行榜处理完成: {} - 生成奖励记录: {} 条", lastMonthStr, rewardCount);
            
        } catch (Exception e) {
            log.error("处理上月排行榜失败", e);
        }
    }

//     /**
//      * 每小时批量发放待发放的奖励
//      * 支持所有小区的奖励发放
//      */
//     @Scheduled(cron = "0 30 * * * ?")
//     public void batchIssueRewards() {
//         try {
//             int issuedCount = monthlyRankingRewardService.batchIssueRewards();
//             if (issuedCount > 0) {
//                 log.info("批量发放奖励完成: {} 条", issuedCount);
//             }
//         } catch (Exception e) {
//             log.error("批量发放奖励失败", e);
//         }
//     }
} 