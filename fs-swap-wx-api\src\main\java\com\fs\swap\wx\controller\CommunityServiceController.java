package com.fs.swap.wx.controller;

import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.CommunityNearby;
import com.fs.swap.common.core.domain.entity.CommunityPhone;
import com.fs.swap.common.core.domain.entity.ResidentialArea;
import com.fs.swap.common.core.domain.entity.UserHome;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.utils.GeoUtils;
import com.fs.swap.common.utils.ResidentialUtils;
import com.fs.swap.system.service.ICommunityNearbyService;
import com.fs.swap.system.service.ICommunityPhoneService;
import com.fs.swap.system.service.IResidentialAreaService;
import com.fs.swap.system.service.IUserHomeService;
import com.fs.swap.wx.domain.convert.CommunityNearbyConvert;
import com.fs.swap.wx.domain.convert.CommunityPhoneConvert;
import com.fs.swap.wx.domain.vo.CommunityNearbyVO;
import com.fs.swap.wx.domain.vo.CommunityPhoneVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区服务Controller
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/community-service")
public class CommunityServiceController extends WxApiBaseController {

    @Autowired
    private ICommunityPhoneService communityPhoneService;

    @Autowired
    private ICommunityNearbyService communityNearbyService;

    @Autowired
    private IUserHomeService userHomeService;

    @Autowired
    private IResidentialAreaService residentialAreaService;

    @Value("${dromara.fileDomain}")
    private String fileUrl;

    /**
     * 获取常用电话列表
     * 支持未登录状态下通过residentialId参数获取指定小区的电话列表
     */
    @GetMapping("/phone/list")
    public AjaxResult getPhoneList(@RequestParam(required = false) Long residentialId) {

        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }
        Long finalResidentialId = ResidentialUtils.getResidentialId(userHome, residentialId);
        // 获取区域信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(finalResidentialId);

        // 根据用户所在位置查询适用的电话列表
        List<CommunityPhone> phoneList = communityPhoneService.selectCommunityPhoneListByLocation(
                residentialArea.getRegionId(),
                residentialArea.getCommunityId(),
                finalResidentialId);

        // 转换为VO
        List<CommunityPhoneVO> voList = CommunityPhoneConvert.INSTANCE.toVOList(phoneList);

        return AjaxResult.success(voList);
    }

    /**
     * 通过residentialId参数获取指定小区的周边服务
     */
    @GetMapping("/nearby/list")
    public AjaxResult getNearbyList(@RequestParam(required = false) Long residentialId) {

        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long finalResidentialId = ResidentialUtils.getResidentialId(userHome, residentialId);

        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(finalResidentialId);
        List<CommunityNearby> nearbyList;
        // 使用小区坐标为中心，查询3公里范围内的服务点
//        if (StringUtils.isNotBlank(residentialArea.getLocation())) {
//            // 使用GeoUtils规范化位置格式为标准POINT格式
//            String centerLocation = GeoUtils.normalizePointFormat(residentialArea.getLocation());
//
//            // 查询2公里（2000米）范围内的服务点
//            nearbyList = communityNearbyService.selectCommunityNearbyListByDistance(
//                    centerLocation, 2000);
//        } else {
//            // 如果小区没有坐标信息，回退到按区域查询
//            nearbyList = communityNearbyService.selectCommunityNearbyListByLocation(
//                    residentialArea.getRegionId(),
//                    residentialArea.getCommunityId(),
//                    finalResidentialId);
//        }
//
        nearbyList = communityNearbyService.selectCommunityNearbyListByLocation(
                residentialArea.getRegionId(),
                residentialArea.getCommunityId(),
                finalResidentialId);
        // 转换为VO
        List<CommunityNearbyVO> voList = CommunityNearbyConvert.INSTANCE.toVOList(nearbyList);

        // 处理图片路径
        for (CommunityNearbyVO vo : voList) {
            // 处理图片
            if (StringUtils.isNotBlank(vo.getImages())) {
                String[] imageUrls = vo.getImages().split(",");
                for (int i = 0; i < imageUrls.length; i++) {
                    imageUrls[i] = fileUrl + imageUrls[i];
                }
                vo.setImageUrls(imageUrls);
            }

            vo.setDistance(GeoUtils.calculateDistance(
                    residentialArea.getLocation(), vo.getLocation()));

        }

        return AjaxResult.success(voList);
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/nearby/detail/{id}")
    public AjaxResult getNearbyDetail(@PathVariable Long id) {
        CommunityNearby nearby = communityNearbyService.selectCommunityNearbyById(id);
        if (nearby == null) {
            return AjaxResult.error("服务信息不存在");
        }
        
        // 更新浏览次数
        communityNearbyService.incrementViewCount(id);
        
        // 转换为VO
        CommunityNearbyVO vo = CommunityNearbyConvert.INSTANCE.toVO(nearby);
        
        // 处理图片路径
        if (StringUtils.isNotBlank(vo.getImages())) {
            String[] imageUrls = vo.getImages().split(",");
            for (int i = 0; i < imageUrls.length; i++) {
                imageUrls[i] = fileUrl + imageUrls[i];
            }
            vo.setImageUrls(imageUrls);
        }
        
        // 处理标签
        if (StringUtils.isNotBlank(vo.getTags())) {
            vo.setTagArray(vo.getTags().split(","));
        }
        
        return AjaxResult.success(vo);
    }
    
    /**
     * 记录拨打电话行为
     */
    @PostMapping("/phone/call/{id}")
    public AjaxResult recordPhoneCall(@PathVariable Long id) {
        communityPhoneService.incrementCallCount(id);
        return AjaxResult.success("记录成功");
    }
    
    /**
     * 获取电话统计信息
     */
    @GetMapping("/phone/stats")
    public AjaxResult getPhoneStats() {
        // 获取用户当前选择的小区
        UserHome userHome = userHomeService.selectUserSelectedHome(getUserId());
        if (userHome == null) {
            return AjaxResult.success(new TableDataInfo());
        }

        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
        if (residentialArea == null) {
            return AjaxResult.success(new TableDataInfo());
        }

        List<CommunityPhone> stats = communityPhoneService.getPhoneStatsByCategory(
                residentialArea.getRegionId(),
                residentialArea.getCommunityId(),
                userHome.getResidentialId());

        return AjaxResult.success(stats);
    }
    
    /**
     * 记录导航行为
     */
    @PostMapping("/nearby/navigate/{id}")
    public AjaxResult recordNavigate(@PathVariable Long id) {
        communityNearbyService.incrementNavigateCount(id);
        return AjaxResult.success("记录成功");
    }
    
    /**
     * 获取推荐服务列表
     * 支持未登录状态下通过residentialId参数获取指定小区的推荐服务
     */
    @GetMapping("/nearby/recommended")
    public AjaxResult getRecommendedNearby(@RequestParam(defaultValue = "10") Integer limit,
                                         @RequestParam(required = false) Long residentialId) {

        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long finalResidentialId = ResidentialUtils.getResidentialId(userHome, residentialId);
        if (finalResidentialId == null) {
            return AjaxResult.success(new TableDataInfo());
        }

        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(finalResidentialId);
        if (residentialArea == null) {
            return AjaxResult.success(new TableDataInfo());
        }

        List<CommunityNearby> nearbyList = communityNearbyService.selectRecommendedCommunityNearby(
                residentialArea.getRegionId(),
                residentialArea.getCommunityId(),
                finalResidentialId,
                limit);
        
        // 转换为VO
        List<CommunityNearbyVO> voList = CommunityNearbyConvert.INSTANCE.toVOList(nearbyList);
        
        // 处理图片路径
        for (CommunityNearbyVO vo : voList) {
            // 处理图片
            if (StringUtils.isNotBlank(vo.getImages())) {
                String[] imageUrls = vo.getImages().split(",");
                for (int i = 0; i < imageUrls.length; i++) {
                    imageUrls[i] = fileUrl + imageUrls[i];
                }
                vo.setImageUrls(imageUrls);
            }
            
            // 处理标签
            if (StringUtils.isNotBlank(vo.getTags())) {
                vo.setTagArray(vo.getTags().split(","));
            }
        }
        
        return AjaxResult.success(voList);
    }
    
    /**
     * 获取热门服务列表
     */
    @GetMapping("/nearby/popular")
    public AjaxResult getPopularNearby(
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        // 获取用户当前选择的小区
        UserHome userHome = userHomeService.selectUserSelectedHome(getUserId());
        if (userHome == null) {
            return AjaxResult.success(new TableDataInfo());
        }
        
        // 获取小区信息
        ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
        if (residentialArea == null) {
            return AjaxResult.success(new TableDataInfo());
        }
        
        List<CommunityNearby> nearbyList = communityNearbyService.selectPopularCommunityNearby(
                residentialArea.getRegionId(),
                residentialArea.getCommunityId(),
                category,
                limit);
        
        // 转换为VO
        List<CommunityNearbyVO> voList = CommunityNearbyConvert.INSTANCE.toVOList(nearbyList);
        
        // 处理图片路径
        for (CommunityNearbyVO vo : voList) {
            // 处理图片
            if (StringUtils.isNotBlank(vo.getImages())) {
                String[] imageUrls = vo.getImages().split(",");
                for (int i = 0; i < imageUrls.length; i++) {
                    imageUrls[i] = fileUrl + imageUrls[i];
                }
                vo.setImageUrls(imageUrls);
            }
            
            // 处理标签
            if (StringUtils.isNotBlank(vo.getTags())) {
                vo.setTagArray(vo.getTags().split(","));
            }
        }
        
        return AjaxResult.success(voList);
    }
}
