package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 月度排行榜奖励发放记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface MonthlyRankingRewardRecordMapper {
    
    /**
     * 查询月度排行榜奖励发放记录
     * 
     * @param id 月度排行榜奖励发放记录主键
     * @return 月度排行榜奖励发放记录
     */
    public MonthlyRankingRewardRecord selectMonthlyRankingRewardRecordById(Long id);

    /**
     * 查询月度排行榜奖励发放记录列表
     * 
     * @param monthlyRankingRewardRecord 月度排行榜奖励发放记录
     * @return 月度排行榜奖励发放记录集合
     */
    public List<MonthlyRankingRewardRecord> selectMonthlyRankingRewardRecordList(MonthlyRankingRewardRecord monthlyRankingRewardRecord);

    /**
     * 查询指定年月的奖励发放记录（带用户信息）
     * 
     * @param yearMonth 年月
     * @param residentialId 小区ID（可选）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 奖励发放记录列表
     */
    public List<MonthlyRankingRewardRecord> selectRewardRecordWithUserInfoPaged(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 查询指定年月的奖励发放记录总数
     * 
     * @param yearMonth 年月
     * @param residentialId 小区ID（可选）
     * @return 总记录数
     */
    public Long countRewardRecord(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId);

    /**
     * 查询指定年月的奖励发放记录（带用户信息）
     * 
     * @param yearMonth 年月
     * @param residentialId 小区ID（可选）
     * @return 奖励发放记录列表
     */
    public List<MonthlyRankingRewardRecord> selectRewardRecordWithUserInfo(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId);

    /**
     * 查询用户的奖励历史记录
     * 
     * @param userId 用户ID
     * @return 用户奖励历史
     */
    public List<MonthlyRankingRewardRecord> selectUserRewardHistory(@Param("userId") Long userId);

    /**
     * 查询待发放的奖励记录
     * 
     * @return 待发放奖励记录列表
     */
    public List<MonthlyRankingRewardRecord> selectPendingRewards();

    /**
     * 新增月度排行榜奖励发放记录
     * 
     * @param monthlyRankingRewardRecord 月度排行榜奖励发放记录
     * @return 结果
     */
    public int insertMonthlyRankingRewardRecord(MonthlyRankingRewardRecord monthlyRankingRewardRecord);

    /**
     * 批量新增奖励发放记录
     * 
     * @param records 奖励发放记录列表
     * @return 结果
     */
    public int batchInsertRewardRecords(@Param("records") List<MonthlyRankingRewardRecord> records);

    /**
     * 修改月度排行榜奖励发放记录
     * 
     * @param monthlyRankingRewardRecord 月度排行榜奖励发放记录
     * @return 结果
     */
    public int updateMonthlyRankingRewardRecord(MonthlyRankingRewardRecord monthlyRankingRewardRecord);

    /**
     * 更新奖励发放状态
     * 
     * @param id 记录ID
     * @param status 发放状态
     * @return 结果
     */
    public int updateRewardStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 删除月度排行榜奖励发放记录
     * 
     * @param id 月度排行榜奖励发放记录主键
     * @return 结果
     */
    public int deleteMonthlyRankingRewardRecordById(Long id);

    /**
     * 批量删除月度排行榜奖励发放记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyRankingRewardRecordByIds(Long[] ids);

    /**
     * 删除指定年月的奖励记录
     * 
     * @param yearMonth 年月
     * @return 结果
     */
    public int deleteByYearMonth(@Param("yearMonth") String yearMonth);
} 