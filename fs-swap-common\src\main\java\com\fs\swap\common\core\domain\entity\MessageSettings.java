package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 消息设置对象 message_settings
 * 
 * <AUTHOR>
 * @date 2024
 */
public class MessageSettings extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 系统通知开关：0-关闭，1-开启 */
    @Excel(name = "系统通知开关")
    private Integer systemNotification;

    /** 聊天消息开关：0-关闭，1-开启 */
    @Excel(name = "聊天消息开关")
    private Integer chatMessage;

    /** 活动通知开关：0-关闭，1-开启 */
    @Excel(name = "活动通知开关")
    private Integer activityNotification;

    /** 订单通知开关：0-关闭，1-开启 */
    @Excel(name = "订单通知开关")
    private Integer orderNotification;

    /** 免打扰模式：0-关闭，1-开启 */
    @Excel(name = "免打扰模式")
    private Integer doNotDisturb;

    /** 免打扰开始时间（小时）*/
    @Excel(name = "免打扰开始时间")
    private Integer doNotDisturbStartHour;

    /** 免打扰结束时间（小时）*/
    @Excel(name = "免打扰结束时间")
    private Integer doNotDisturbEndHour;

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Integer getSystemNotification()
    {
        return systemNotification;
    }

    public void setSystemNotification(Integer systemNotification)
    {
        this.systemNotification = systemNotification;
    }

    public Integer getChatMessage()
    {
        return chatMessage;
    }

    public void setChatMessage(Integer chatMessage)
    {
        this.chatMessage = chatMessage;
    }

    public Integer getActivityNotification()
    {
        return activityNotification;
    }

    public void setActivityNotification(Integer activityNotification)
    {
        this.activityNotification = activityNotification;
    }

    public Integer getOrderNotification()
    {
        return orderNotification;
    }

    public void setOrderNotification(Integer orderNotification)
    {
        this.orderNotification = orderNotification;
    }

    public Integer getDoNotDisturb()
    {
        return doNotDisturb;
    }

    public void setDoNotDisturb(Integer doNotDisturb)
    {
        this.doNotDisturb = doNotDisturb;
    }

    public Integer getDoNotDisturbStartHour()
    {
        return doNotDisturbStartHour;
    }

    public void setDoNotDisturbStartHour(Integer doNotDisturbStartHour)
    {
        this.doNotDisturbStartHour = doNotDisturbStartHour;
    }

    public Integer getDoNotDisturbEndHour()
    {
        return doNotDisturbEndHour;
    }

    public void setDoNotDisturbEndHour(Integer doNotDisturbEndHour)
    {
        this.doNotDisturbEndHour = doNotDisturbEndHour;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("systemNotification", getSystemNotification())
            .append("chatMessage", getChatMessage())
            .append("activityNotification", getActivityNotification())
            .append("orderNotification", getOrderNotification())
            .append("doNotDisturb", getDoNotDisturb())
            .append("doNotDisturbStartHour", getDoNotDisturbStartHour())
            .append("doNotDisturbEndHour", getDoNotDisturbEndHour())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 