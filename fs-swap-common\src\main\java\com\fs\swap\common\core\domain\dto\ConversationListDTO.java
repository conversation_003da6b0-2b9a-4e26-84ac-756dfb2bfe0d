package com.fs.swap.common.core.domain.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 会话列表DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
public class ConversationListDTO
{
    /** 会话ID */
    private String conversationId;

    /** 会话类型：single-单聊，group-群聊 */
    private String type;

    /** 会话标题 */
    private String title;

    /** 会话头像 */
    private String avatar;

    /** 最后一条消息ID */
    private String lastMessageId;

    /** 最后消息内容 */
    private String lastMessageContent;

    /** 最后消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastMessageTime;

    /** 未读消息数量 */
    private Integer unreadCount;

    /** 是否免打扰：0-否，1-是 */
    private Integer isMuted;

    /** 会话更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public ConversationListDTO()
    {
    }

    public String getConversationId()
    {
        return conversationId;
    }

    public void setConversationId(String conversationId)
    {
        this.conversationId = conversationId;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getLastMessageId()
    {
        return lastMessageId;
    }

    public void setLastMessageId(String lastMessageId)
    {
        this.lastMessageId = lastMessageId;
    }

    public String getLastMessageContent()
    {
        return lastMessageContent;
    }

    public void setLastMessageContent(String lastMessageContent)
    {
        this.lastMessageContent = lastMessageContent;
    }

    public Date getLastMessageTime()
    {
        return lastMessageTime;
    }

    public void setLastMessageTime(Date lastMessageTime)
    {
        this.lastMessageTime = lastMessageTime;
    }

    public Integer getUnreadCount()
    {
        return unreadCount;
    }

    public void setUnreadCount(Integer unreadCount)
    {
        this.unreadCount = unreadCount;
    }

    public Integer getIsMuted()
    {
        return isMuted;
    }

    public void setIsMuted(Integer isMuted)
    {
        this.isMuted = isMuted;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    @Override
    public String toString()
    {
        return "ConversationListDTO{" +
                "conversationId='" + conversationId + '\'' +
                ", type='" + type + '\'' +
                ", title='" + title + '\'' +
                ", avatar='" + avatar + '\'' +
                ", lastMessageId='" + lastMessageId + '\'' +
                ", lastMessageContent='" + lastMessageContent + '\'' +
                ", lastMessageTime=" + lastMessageTime +
                ", unreadCount=" + unreadCount +
                ", isMuted=" + isMuted +
                ", updateTime=" + updateTime +
                '}';
    }
} 