package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.TaskConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface TaskConfigMapper {
    /**
     * 查询任务配置
     * 
     * @param id 任务配置主键
     * @return 任务配置
     */
    public TaskConfig selectTaskConfigById(Long id);

    /**
     * 查询任务配置列表
     * 
     * @param taskConfig 任务配置
     * @return 任务配置集合
     */
    public List<TaskConfig> selectTaskConfigList(TaskConfig taskConfig);

    /**
     * 新增任务配置
     * 
     * @param taskConfig 任务配置
     * @return 结果
     */
    public int insertTaskConfig(TaskConfig taskConfig);

    /**
     * 修改任务配置
     * 
     * @param taskConfig 任务配置
     * @return 结果
     */
    public int updateTaskConfig(TaskConfig taskConfig);

    /**
     * 删除任务配置
     * 
     * @param id 任务配置主键
     * @return 结果
     */
    public int deleteTaskConfigById(Long id);

    /**
     * 批量删除任务配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskConfigByIds(Long[] ids);

    /**
     * 根据任务编码查询任务配置
     * 
     * @param taskCode 任务编码
     * @return 任务配置
     */
    public TaskConfig selectTaskConfigByCode(String taskCode);

    /**
     * 根据触发事件查询任务配置列表
     * 
     * @param triggerEvent 触发事件
     * @return 任务配置集合
     */
    public List<TaskConfig> selectTaskConfigByTriggerEvent(String triggerEvent);

    /**
     * 查询启用的任务配置列表
     * 
     * @return 任务配置集合
     */
    public List<TaskConfig> selectEnabledTaskConfigs();
} 