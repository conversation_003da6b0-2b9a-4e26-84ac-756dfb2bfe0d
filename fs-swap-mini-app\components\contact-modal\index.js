// components/contact-modal/index.js
const systemInfoService = require('../../services/systemInfo.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 联系人昵称
    targetNickname: {
      type: String,
      value: '用户'
    },
    // 联系方式列表
    contacts: {
      type: Array,
      value: []
    },
    // 文件服务器地址
    fileUrl: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 微信二维码弹框相关
    showQrcodeModal: false,
    qrcodeImageUrl: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭联系方式弹窗
    closeModal() {
      this.setData({
        show: false
      })
      this.triggerEvent('close')
    },

    // 复制联系方式
    onCopyContact(e) {
      const content = e.currentTarget.dataset.content
      const typeCode = e.currentTarget.dataset.type
      
      let typeName = ''
      if (typeCode === 1) {
        typeName = '手机号'
      } else if (typeCode === 2) {
        typeName = '微信号'
      } else if (typeCode === 3) { // 微信二维码
        // 如果是微信二维码，则显示自定义弹框
        this.showWechatQrCode(content)
        return
      } else {
        typeName = '联系方式'
      }
      
      if (!content) {
        wx.showToast({
          title: `${typeName}未设置`,
          icon: 'none'
        })
        return
      }
      
      wx.setClipboardData({
        data: content,
        success: () => {
          wx.showToast({
            title: `${typeName}已复制`,
            icon: 'success'
          })
        }
      })
    },
    
    // 显示微信二维码弹框
    async showWechatQrCode(qrCodeUrl) {
      if (!qrCodeUrl) {
        wx.showToast({
          title: '二维码未设置',
          icon: 'none'
        })
        return
      }
      
      try {
        // 使用系统信息服务处理图片URL
        const imageUrl = await systemInfoService.processImageUrl(qrCodeUrl)
        
        // 显示自定义弹框
        this.setData({
          qrcodeImageUrl: imageUrl,
          showQrcodeModal: true
        })
      } catch (error) {
        console.error('处理二维码图片失败:', error)
        wx.showToast({
          title: '二维码加载失败',
          icon: 'none'
        })
      }
    },
    
    // 关闭微信二维码弹框
    closeQrcodeModal() {
      this.setData({
        showQrcodeModal: false
      })
    },
    
    // 拨打电话
    onCallPhone(e) {
      const phone = e.currentTarget.dataset.phone
      
      if (!phone) {
        wx.showToast({
          title: '手机号未设置',
          icon: 'none'
        })
        return
      }
      
      wx.showModal({
        title: '拨打电话',
        content: `确定要拨打 ${phone} 吗？`,
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: phone,
              success: () => {
                },
              fail: (err) => {
                console.error('拨打电话失败:', err)
                wx.showToast({
                  title: '拨打失败，请手动拨号',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    }
  }
})
