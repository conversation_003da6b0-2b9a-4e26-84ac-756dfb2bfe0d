package com.fs.swap.wx.controller;

import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardRecord;
import com.fs.swap.common.core.domain.entity.UserHome;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.utils.ResidentialUtils;
import com.fs.swap.system.service.IMonthlyRankingRewardService;
import com.fs.swap.system.service.IUserHomeService;
import com.fs.swap.system.service.impl.MonthlyRankingServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 月度排行榜Controller
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@RestController
@RequestMapping("/ranking")
public class MonthlyRankingController extends WxApiBaseController {
    
    @Autowired
    private MonthlyRankingServiceImpl monthlyRankingService;
    
    @Autowired
    private IMonthlyRankingRewardService monthlyRankingRewardService;
    
    @Autowired
    private IUserHomeService userHomeService;

    /**
     * 获取排行榜列表（支持分页和时间范围筛选）
     */
    @GetMapping("/list")
    public AjaxResult getRankingList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String timeRange) {

        // 根据timeRange参数确定年月
        String yearMonth;
        if ("month".equals(timeRange)) {
            yearMonth = DateUtils.dateTimeNow("yyyy-MM");
        } else {
            // 默认使用当前月份
            yearMonth = DateUtils.dateTimeNow("yyyy-MM");
        }

        Long userId = getUserId();
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome == null) {
            return AjaxResult.error("请先选择你的小区");
        }
        Long residentialId = userHome.getResidentialId();

        // 获取排行榜数据
        TableDataInfo tableData = monthlyRankingService.getRankingList(yearMonth, residentialId, pageNum, pageSize);

        return AjaxResult.success(tableData.getRows());
    }

    /**
     * 获取月度排行榜（按用户所在小区）- 包含排行榜列表和用户排名
     * 支持未登录状态下通过residentialId参数获取指定小区的排行榜
     */
    @GetMapping("/monthly")
    public AjaxResult getMonthlyRanking(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) Long residentialId) {
        String yearMonth = DateUtils.dateTimeNow("yyyy-MM");

        // 获取小区ID：优先使用用户绑定的小区，其次使用请求参数中的小区ID
        UserHome userHome = null;
        if (isLogin()) {
            userHome = userHomeService.selectUserSelectedHome(getUserId());
        }

        Long finalResidentialId = ResidentialUtils.getResidentialId(userHome, residentialId);

        // 调用MonthlyRankingServiceImpl的方法，需要传入pageNum和pageSize参数
        TableDataInfo tableData = monthlyRankingService.getRankingList(yearMonth, finalResidentialId, 1, limit);
        List<Map<String, Object>> rankingList = (List<Map<String, Object>>) tableData.getRows();

        Map<String, Object> result = new HashMap<>();
        result.put("rankingList", rankingList);

        // 只有登录用户才返回个人排名信息
        if (isLogin()) {
            Map<String, Object> myRanking = monthlyRankingService.getUserRankingInfo(getUserId(), yearMonth);
            result.put("myRanking", myRanking);
        } else {
            result.put("myRanking", null);
        }

        return AjaxResult.success(result);
    }

    /**
     * 获取用户奖励历史
     */
    @GetMapping("/rewards/history")
    public AjaxResult getUserRewardHistory() {
        Long userId = getUserId();
        List<MonthlyRankingRewardRecord> rewardHistory = monthlyRankingRewardService.selectUserRewardHistory(userId);
        return AjaxResult.success(rewardHistory);
    }

    /**
     * 获取上期榜单（从月度排行榜奖励记录中查询）
     */
    @GetMapping("/previous")
    public AjaxResult getPreviousRanking(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {

        Long userId = getUserId();
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome == null) {
            return AjaxResult.error("请先选择你的小区");
        }
        Long residentialId = userHome.getResidentialId();

        // 获取上一个月的年月
        String previousMonth = DateUtils.getPreviousMonth();

        // 从奖励记录表中查询上期榜单
        TableDataInfo tableData = monthlyRankingRewardService.getPreviousRankingList(previousMonth, residentialId, pageNum, pageSize);

        return AjaxResult.success(tableData.getRows());
    }

    /**
     * 获取用户自己的排名信息
     */
    @GetMapping("/my-ranking")
    public AjaxResult getMyRanking(@RequestParam(required = false) String yearMonth) {
        if (yearMonth == null || yearMonth.isEmpty()) {
            yearMonth = DateUtils.dateTimeNow("yyyy-MM");
        }

        Long userId = getUserId();

        // 获取用户的小区ID
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome == null) {
            return AjaxResult.error("请先选择你的小区");
        }

        // 获取用户排名信息
        Map<String, Object> myRanking = monthlyRankingService.getUserRankingInfo(userId, yearMonth);

        if (myRanking.isEmpty()) {
            return AjaxResult.error("未找到您的排名信息");
        }

        return AjaxResult.success(myRanking);
    }
}