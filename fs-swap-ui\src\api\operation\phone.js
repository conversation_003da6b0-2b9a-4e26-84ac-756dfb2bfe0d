import request from '@/utils/request'

// 查询社区服务-常用电话列表
export function listPhone(query) {
  return request({
    url: '/operation/phone/list',
    method: 'get',
    params: query
  })
}

// 查询社区服务-常用电话详细
export function getPhone(id) {
  return request({
    url: '/operation/phone/' + id,
    method: 'get'
  })
}

// 新增社区服务-常用电话
export function addPhone(data) {
  return request({
    url: '/operation/phone',
    method: 'post',
    data: data
  })
}

// 修改社区服务-常用电话
export function updatePhone(data) {
  return request({
    url: '/operation/phone',
    method: 'put',
    data: data
  })
}

// 删除社区服务-常用电话
export function delPhone(id) {
  return request({
    url: '/operation/phone/' + id,
    method: 'delete'
  })
}

// 审核社区服务-常用电话
export function auditPhone(id, auditStatus, auditRemark) {
  return request({
    url: '/operation/phone/audit/' + id,
    method: 'put',
    params: {
      auditStatus: auditStatus,
      auditRemark: auditRemark
    }
  })
}

// 批量审核社区服务-常用电话
export function batchAuditPhone(ids, auditStatus, auditRemark) {
  return request({
    url: '/operation/phone/audit/batch',
    method: 'put',
    params: {
      ids: ids,
      auditStatus: auditStatus,
      auditRemark: auditRemark
    }
  })
}

// 获取电话统计信息
export function getPhoneStats(regionId, communityId, residentialId) {
  return request({
    url: '/operation/phone/stats',
    method: 'get',
    params: {
      regionId: regionId,
      communityId: communityId,
      residentialId: residentialId
    }
  })
} 