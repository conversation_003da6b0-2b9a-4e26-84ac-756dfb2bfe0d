/* 会话列表页面样式 */
page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #3B7FFF;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  --border-radius: 16rpx;
  --border-color: #eeeeee;
}

.conversation-container {
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.header {
  background: var(--primary-color);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--card-shadow);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-color);
}

.actions {
  display: flex;
  align-items: center;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
}

/* 会话列表 */
.conversation-list {
  flex: 1;
  padding: 0 20rpx;
}

.conversation-items {
  background: var(--primary-color);
  border-radius: var(--border-radius);
  margin-top: 20rpx;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.conversation-item:last-child {
  border-bottom: none;
}

.conversation-item:active {
  background-color: var(--secondary-color);
}

/* 头像区域 */
.avatar-wrapper {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: var(--secondary-color);
  border: 2rpx solid var(--primary-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 20rpx;
  text-align: center;
  line-height: 20rpx;
  border: 2rpx solid var(--primary-color);
}

/* 会话信息 */
.conversation-info {
  flex: 1;
  min-width: 0;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
}

.time {
  font-size: 24rpx;
  color: var(--text-light);
  white-space: nowrap;
}

.last-message {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-light);
  margin-top: 16rpx;
}

/* 响应式优化 - 小屏幕适配 */
@media screen and (max-width: 375px) {
  .header {
    padding: 28rpx 24rpx 20rpx;
  }
  
  .title {
    font-size: 32rpx;
  }
  
  .add-btn {
    width: 64rpx;
    height: 64rpx;
  }
  
  .add-btn .iconfont {
    font-size: 28rpx;
  }
  
  .conversation-item {
    padding: 20rpx 24rpx;
  }
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
  }
  
  .avatar-wrapper {
    margin-right: 20rpx;
  }
  
  .name {
    font-size: 30rpx;
  }
  
  .last-message {
    font-size: 26rpx;
  }
  
  .time {
    font-size: 22rpx;
  }
  
  .empty-state {
    padding: 100rpx 40rpx;
  }
  
  .empty-icon {
    font-size: 100rpx;
    margin-bottom: 24rpx;
  }
  
  .empty-text {
    font-size: 30rpx;
    margin-bottom: 12rpx;
  }
  
  .empty-desc {
    font-size: 26rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .header {
    padding: 24rpx 20rpx 16rpx;
  }
  
  .title {
    font-size: 30rpx;
  }
  
  .add-btn {
    width: 56rpx;
    height: 56rpx;
  }
  
  .add-btn .iconfont {
    font-size: 24rpx;
  }
  
  .conversation-item {
    padding: 16rpx 20rpx;
  }
  
  .avatar {
    width: 72rpx;
    height: 72rpx;
  }
  
  .avatar-wrapper {
    margin-right: 16rpx;
  }
  
  .name {
    font-size: 28rpx;
    margin-right: 12rpx;
  }
  
  .last-message {
    font-size: 24rpx;
  }
  
  .time {
    font-size: 20rpx;
  }
  
  .unread-badge {
    font-size: 18rpx;
    padding: 2rpx 6rpx;
    min-width: 16rpx;
    line-height: 16rpx;
  }
  
  .empty-state {
    padding: 80rpx 30rpx;
  }
  
  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    margin-bottom: 10rpx;
  }
  
  .empty-desc {
    font-size: 24rpx;
  }
} 