package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 活动信息对象 activity
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class Activity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long id;

    /** 发布者用户ID */
    @Excel(name = "发布者用户ID")
    private Long userId;

    /** 所属小区ID */
    @Excel(name = "所属小区ID")
    private Long residentialId;

    /** 所属社区ID */
    @Excel(name = "所属社区ID")
    private Long communityId;

    /** 行政区域ID */
    @Excel(name = "行政区域ID")
    private Integer regionId;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String title;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String description;

    /** 活动图片 */
    @Excel(name = "活动图片")
    private String images;

    /** 活动地点描述 */
    @Excel(name = "活动地点描述")
    private String address;

    /** 活动地点坐标（POINT类型） */
    @Excel(name = "活动地点坐标")
    private String location;

    /** 活动分类（字典值） */
    @Excel(name = "活动分类", readConverterExp = "字=典值")
    private String category;

    /** 活动开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 报名开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signupStartTime;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signupEndTime;

    /** 最大参与人数，NULL表示不限制 */
    @Excel(name = "最大参与人数")
    private Integer maxParticipants;

    /** 是否免费（Y-收费 N-免费） */
    @Excel(name = "是否免费", readConverterExp = "Y=-收费,N=-免费")
    private String isFree;

    /** 活动费用 */
    @Excel(name = "活动费用")
    private Double feeAmount;

    /** 费用说明（如现场支付方式等） */
    @Excel(name = "费用说明", readConverterExp = "如=现场支付方式等")
    private String feeDescription;

    /** 状态（0-草稿 1-发布中 2-已结束 3-已取消） */
    @Excel(name = "状态", readConverterExp = "0=-草稿,1=-发布中,2=-已结束,3=-已取消")
    private String status;

    // 非数据库字段
    /** 发布者昵称 */
    private String nickname;
    /** 发布者头像 */
    private String avatar;
    /** 已报名人数 */
    private Integer signupCount;
    /** 当前用户是否已报名 */
    private Boolean isSignedUp;
}
