package com.fs.swap.wx.controller;

import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.wx.service.TaskService;
import com.fs.swap.common.annotation.RepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/task")
public class TaskController extends WxApiBaseController {
    
    @Autowired
    private TaskService taskService;

    /**
     * 获取用户任务列表
     */
    @GetMapping("/list")
    public AjaxResult getTaskList() {
        Long userId = getUserId();
        List<Object> tasks = taskService.getUserTaskList(userId);
        return AjaxResult.success(tasks);
    }

    /**
     * 领取任务奖励
     */
    @RepeatSubmit(interval = 5000, message = "请勿重复提交，请稍后再试")
    @PostMapping("/claim/{taskId}")
    public AjaxResult claimReward(@PathVariable Long taskId) {
        Long userId = getUserId();
        taskService.claimTaskReward(userId, taskId);
        return AjaxResult.success("领取成功");
    }

    /**
     * 手动触发任务事件(测试用)
     */
//    @PostMapping("/trigger")
//    public AjaxResult triggerTaskEvent(@RequestParam String eventType,
//                                     @RequestParam(required = false) String businessId) {
//        Long userId = getUserId();
//        taskService.processTaskEvent(userId, eventType, businessId);
//        return AjaxResult.success("任务事件触发成功");
//    }

    /**
     * 获取待领取的任务列表
     */
    @GetMapping("/claim-list")
    public AjaxResult getTasksToClaimList() {
        Long userId = getUserId();
        // 查询用户待领取的任务
        List<Object> tasks = taskService.getUserTaskList(userId);
        List<Object> claimableTasks = tasks.stream()
            .filter(task -> {
                Map<String, Object> taskMap = (Map<String, Object>) task;
                return Boolean.TRUE.equals(taskMap.get("canClaim"));
            })
            .collect(java.util.stream.Collectors.toList());
        
        return AjaxResult.success(claimableTasks);
    }

    /**
     * 批量领取奖励
     */
    @RepeatSubmit(interval = 5000, message = "请勿重复提交，请稍后再试")
    @PostMapping("/claim-all")
    public AjaxResult claimAllRewards() {
        Long userId = getUserId();
        
        // 获取用户待领取的任务
        List<Object> tasks = taskService.getUserTaskList(userId);
        int claimedCount = 0;
        
        for (Object task : tasks) {
            Map<String, Object> taskMap = (Map<String, Object>) task;
            if (Boolean.TRUE.equals(taskMap.get("canClaim"))) {
                Long taskId = (Long) taskMap.get("id");
                if (taskId != null) {
                    try {
                        taskService.claimTaskReward(userId, taskId);
                        claimedCount++;
                    } catch (Exception e) {
                        // 忽略单个任务领取失败，继续处理其他任务
                    }
                }
            }
        }
        
        return AjaxResult.success("成功领取 " + claimedCount + " 个任务奖励");
    }
} 