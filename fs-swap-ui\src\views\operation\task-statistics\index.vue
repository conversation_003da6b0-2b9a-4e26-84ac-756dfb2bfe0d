<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="statistic-card">
            <div class="statistic-icon">
              <i class="el-icon-s-check" style="color: #67C23A"></i>
            </div>
            <div class="statistic-content">
              <div class="statistic-title">今日完成任务数</div>
              <div class="statistic-value">{{ statistics.todayCompleted || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="statistic-card">
            <div class="statistic-icon">
              <i class="el-icon-user" style="color: #409EFF"></i>
            </div>
            <div class="statistic-content">
              <div class="statistic-title">活跃用户数</div>
              <div class="statistic-value">{{ statistics.activeUsers || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="statistic-card">
            <div class="statistic-icon">
              <i class="el-icon-coin" style="color: #FF9800"></i>
            </div>
            <div class="statistic-content">
              <div class="statistic-title">今日发放碳豆</div>
              <div class="statistic-value">{{ statistics.todayReward || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="statistic-card">
            <div class="statistic-icon">
              <i class="el-icon-trophy" style="color: #E6A23C"></i>
            </div>
            <div class="statistic-content">
              <div class="statistic-title">任务完成率</div>
              <div class="statistic-value">{{ (statistics.completionRate || 0).toFixed(1) }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-card shadow="never" class="mb20">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="统计类型" prop="statisticsType">
          <el-select v-model="queryParams.statisticsType" placeholder="请选择统计类型" @change="handleTypeChange">
            <el-option label="完成统计" value="completion" />
            <el-option label="活跃统计" value="activity" />
            <el-option label="奖励统计" value="reward" />
            <el-option label="排行榜" value="ranking" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
            <el-option
              v-for="dict in dict.type.task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span>任务完成趋势</span>
          </div>
          <div id="trendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span>任务类型分布</span>
          </div>
          <div id="pieChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card shadow="never">
      <div slot="header">
        <span>{{ getTableTitle() }}</span>
      </div>
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <!-- 完成统计列 -->
        <template v-if="queryParams.statisticsType === 'completion'">
          <el-table-column label="日期" align="center" prop="date" width="120" />
          <el-table-column label="任务类型" align="center" prop="taskType" width="120">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.task_type" :value="scope.row.taskType"/>
            </template>
          </el-table-column>
          <el-table-column label="完成数量" align="center" prop="completedCount" />
          <el-table-column label="目标数量" align="center" prop="targetCount" />
          <el-table-column label="完成率" align="center" prop="completionRate">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.completionRate" :show-text="false" />
              <span class="ml10">{{ scope.row.completionRate }}%</span>
            </template>
          </el-table-column>
        </template>

        <!-- 活跃统计列 -->
        <template v-if="queryParams.statisticsType === 'activity'">
          <el-table-column label="日期" align="center" prop="date" width="120" />
          <el-table-column label="活跃用户数" align="center" prop="activeUsers" />
          <el-table-column label="新增用户数" align="center" prop="newUsers" />
          <el-table-column label="任务参与率" align="center" prop="participationRate">
            <template slot-scope="scope">
              <span>{{ scope.row.participationRate }}%</span>
            </template>
          </el-table-column>
        </template>

        <!-- 奖励统计列 -->
        <template v-if="queryParams.statisticsType === 'reward'">
          <el-table-column label="日期" align="center" prop="date" width="120" />
          <el-table-column label="发放碳豆数" align="center" prop="rewardAmount">
            <template slot-scope="scope">
              <span class="reward-silver">{{ scope.row.rewardAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="领取用户数" align="center" prop="claimUsers" />
          <el-table-column label="平均奖励" align="center" prop="avgReward">
            <template slot-scope="scope">
              <span class="reward-silver">{{ scope.row.avgReward }}</span>
            </template>
          </el-table-column>
        </template>

        <!-- 排行榜列 -->
        <template v-if="queryParams.statisticsType === 'ranking'">
          <el-table-column label="排名" align="center" prop="rank" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.rank <= 3" :type="getRankType(scope.row.rank)">
                {{ scope.row.rank }}
              </el-tag>
              <span v-else>{{ scope.row.rank }}</span>
            </template>
          </el-table-column>
          <el-table-column label="用户ID" align="center" prop="userId" width="100" />
          <el-table-column label="用户名" align="center" prop="userName" width="150" />
          <el-table-column label="完成任务数" align="center" prop="completedTasks" />
          <el-table-column label="获得碳豆" align="center" prop="totalReward">
            <template slot-scope="scope">
              <span class="reward-silver">{{ scope.row.totalReward }}</span>
            </template>
          </el-table-column>
          <el-table-column label="完成率" align="center" prop="completionRate">
            <template slot-scope="scope">
              <span>{{ scope.row.completionRate }}%</span>
            </template>
          </el-table-column>
        </template>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getTaskStatistics, getTaskStatisticsList } from '@/api/operation/task-statistics'
import * as echarts from 'echarts'

export default {
  name: 'TaskStatistics',
  dicts: ['task_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 统计数据
      statistics: {},
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        statisticsType: 'completion',
        taskType: null,
        beginTime: null,
        endTime: null
      },
      // 图表实例
      trendChart: null,
      pieChart: null
    }
  },
  watch: {
    // 日期范围
    dateRange(val) {
      if (val === null) {
        this.queryParams.beginTime = null
        this.queryParams.endTime = null
      } else {
        this.queryParams.beginTime = val[0]
        this.queryParams.endTime = val[1]
      }
    }
  },
  mounted() {
    this.initCharts()
    this.getStatistics()
    this.getList()
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.pieChart) {
      this.pieChart.dispose()
    }
  },
  methods: {
    /** 初始化图表 */
    initCharts() {
      this.trendChart = echarts.init(document.getElementById('trendChart'))
      this.pieChart = echarts.init(document.getElementById('pieChart'))

      // 趋势图配置
      const trendOption = {
        title: {
          text: '任务完成趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['完成数量', '目标数量']
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '完成数量',
            type: 'line',
            data: []
          },
          {
            name: '目标数量',
            type: 'line',
            data: []
          }
        ]
      }

      // 饼图配置
      const pieOption = {
        title: {
          text: '任务类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '任务类型',
            type: 'pie',
            radius: '50%',
            data: []
          }
        ]
      }

      this.trendChart.setOption(trendOption)
      this.pieChart.setOption(pieOption)
    },
    /** 获取统计数据 */
    getStatistics() {
      getTaskStatistics().then(response => {
        this.statistics = response.data
      })
    },
    /** 查询列表数据 */
    getList() {
      this.loading = true
      getTaskStatisticsList(this.queryParams).then(response => {
        this.tableData = response.rows
        this.total = response.total
        this.loading = false
        this.updateCharts(response.chartData || {})
      })
    },
    /** 更新图表 */
    updateCharts(chartData) {
      // 更新趋势图
      if (chartData.trend) {
        this.trendChart.setOption({
          xAxis: {
            data: chartData.trend.dates || []
          },
          series: [
            {
              data: chartData.trend.completed || []
            },
            {
              data: chartData.trend.target || []
            }
          ]
        })
      }

      // 更新饼图
      if (chartData.distribution) {
        this.pieChart.setOption({
          series: [
            {
              data: chartData.distribution || []
            }
          ]
        })
      }
    },
    /** 统计类型改变 */
    handleTypeChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.statisticsType = 'completion'
      this.handleQuery()
    },
    /** 获取表格标题 */
    getTableTitle() {
      const titles = {
        completion: '任务完成统计',
        activity: '用户活跃统计',
        reward: '奖励发放统计',
        ranking: '用户排行榜'
      }
      return titles[this.queryParams.statisticsType] || '统计数据'
    },
    /** 获取排名标签类型 */
    getRankType(rank) {
      if (rank === 1) return 'danger'
      if (rank === 2) return 'warning'
      if (rank === 3) return 'success'
      return 'info'
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/task-statistics/export', {
        ...this.queryParams
      }, `task_statistics_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.statistic-card {
  display: flex;
  align-items: center;
}

.statistic-icon {
  font-size: 40px;
  margin-right: 16px;
}

.statistic-content {
  flex: 1;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.reward-silver {
  color: #ff9800;
  font-weight: bold;
}
</style> 