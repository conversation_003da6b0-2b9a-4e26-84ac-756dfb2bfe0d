package com.fs.swap.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;

/**
 * 会话成员对象 conversation_member
 * 
 * <AUTHOR>
 * @date 2024
 */
public class ConversationMember extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 会话ID */
    @Excel(name = "会话ID")
    private String conversationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 未读消息数量 */
    @Excel(name = "未读消息数量")
    private Integer unreadCount;

    /** 最后已读消息ID */
    @Excel(name = "最后已读消息ID")
    private String lastReadMessageId;

    /** 是否免打扰：0-否，1-是 */
    @Excel(name = "是否免打扰", readConverterExp = "0=否,1=是")
    private Integer isMuted;

    /** 加入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "加入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    /** 离开时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "离开时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date leaveTime;

    /** 状态：0-正常，1-已离开 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=已离开")
    private Integer status;

    /** 用户信息（关联查询时使用） */
    private UserInfo userInfo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setConversationId(String conversationId) 
    {
        this.conversationId = conversationId;
    }

    public String getConversationId() 
    {
        return conversationId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUnreadCount(Integer unreadCount) 
    {
        this.unreadCount = unreadCount;
    }

    public Integer getUnreadCount() 
    {
        return unreadCount;
    }
    public void setLastReadMessageId(String lastReadMessageId) 
    {
        this.lastReadMessageId = lastReadMessageId;
    }

    public String getLastReadMessageId() 
    {
        return lastReadMessageId;
    }
    public void setIsMuted(Integer isMuted) 
    {
        this.isMuted = isMuted;
    }

    public Integer getIsMuted() 
    {
        return isMuted;
    }
    public void setJoinTime(Date joinTime) 
    {
        this.joinTime = joinTime;
    }

    public Date getJoinTime() 
    {
        return joinTime;
    }
    public void setLeaveTime(Date leaveTime) 
    {
        this.leaveTime = leaveTime;
    }

    public Date getLeaveTime() 
    {
        return leaveTime;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("conversationId", getConversationId())
            .append("userId", getUserId())
            .append("unreadCount", getUnreadCount())
            .append("lastReadMessageId", getLastReadMessageId())
            .append("isMuted", getIsMuted())
            .append("joinTime", getJoinTime())
            .append("leaveTime", getLeaveTime())
            .append("status", getStatus())
            .toString();
    }
} 