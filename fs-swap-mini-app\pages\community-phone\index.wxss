/* pages/community-phone/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* 顶部筛选区域 */
.filter-header {
  background: #ffffff;
  padding: 10px 16px 6px;
  z-index: 100;
  position: relative;
  border-bottom: 1px solid #f0f2f5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

/* 搜索框样式 */
.search-section {
  margin-bottom: 10px;
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

/* 自定义van-search样式 */
.search-input {
  padding: 0 !important;
  background-color: transparent !important;
}

.search-input .van-search__content {
  background-color: #f8f9fb !important;
  border-radius: 12px !important;
  height: 36px !important;
  box-shadow: none !important;
  border: 1px solid #e8eaed !important;
  transition: all 0.2s ease !important;
  position: relative !important;
}

.search-input .van-search__content:focus-within {
  border-color: #3B7FFF !important;
  background-color: #ffffff !important;
  box-shadow: 0 0 0 3px rgba(59, 127, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

.search-input .van-search__content:hover:not(:focus-within) {
  border-color: #d0d3d9 !important;
  background-color: #ffffff !important;
}

.search-input .van-field__left-icon {
  color: #8a8e99 !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.search-input .van-cell {
  padding: 4px 16px !important;
  background-color: transparent !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

.search-input-field {
  color: #1f2329 !important;
  font-size: 14px !important;
  height: 28px !important;
  font-weight: 400 !important;
}

.search-input .van-field__placeholder {
  color: #8a8e99 !important;
  font-size: 14px !important;
}

/* 分类区域 */
.category-section {
  position: relative;
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 分类标签样式 */
.category-tabs {
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0;
  margin: 0 -16px;
  padding-left: 16px;
  padding-right: 16px;
  position: relative;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 隐藏WebKit滚动条 */
.category-tabs::-webkit-scrollbar {
  display: none;
}

/* 滑动指示器 */
.category-tabs::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 24px;
  background: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  pointer-events: none;
  z-index: 1;
}

.category-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 8px;
  margin-right: 6px;
  background-color: #f8f9fb;
  border-radius: 12px;
  min-width: auto;
  height: 22px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.category-tab:last-child {
  margin-right: 16px;
}

.category-tab:active {
  transform: scale(0.96);
}

.category-tab.active {
  background-color: #3B7FFF;
  border-color: #3B7FFF;
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.2);
  transform: translateY(-0.5px);
}

.category-tab:not(.active):hover {
  background-color: #f0f2f5;
  border-color: #e8eaed;
  transform: translateY(-0.5px);
}

.category-tab:not(.active):active {
  transform: scale(0.96) translateY(0);
}

.tab-text {
  font-size: 11px;
  font-weight: 500;
  color: #4e5969;
  transition: color 0.2s ease;
  line-height: 1;
  user-select: none;
}

.category-tab.active .tab-text {
  color: #ffffff;
  font-weight: 600;
}


/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

/* 主体内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #f8f9fa;
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: #f8f9fa;
  height: calc(100vh - 76px);
}

.loading-text {
  margin-top: 12px;
  font-size: 13px;
  color: #86909c;
  font-weight: 500;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  flex: 1;
}

.empty-text {
  font-size: 15px;
  color: #86909c;
  margin-top: 16px;
  font-weight: 500;
}

.empty-hint {
  font-size: 12px;
  color: #c4c6cc;
  margin-top: 6px;
  line-height: 1.4;
}

/* 分区标题 */
.section-header {
  margin: 16px 16px 12px;
  position: relative;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #1f2329;
  margin-bottom: 6px;
  padding: 0 8px;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: linear-gradient(135deg, #3B7FFF, #5B9FFF);
  border-radius: 2px;
}

.section-text {
  margin-left: 12px;
  font-weight: 600;
}

.section-divider {
  height: 1px;
  background: linear-gradient(to right, rgba(59, 127, 255, 0.1), rgba(59, 127, 255, 0.05));
  margin: 0 8px;
}

/* 电话分组 */
.phone-group {
  margin-bottom: 6px;
}

/* 电话列表 */
.phone-list {
  padding: 8px 0 100px 0;
  flex: 1;
}

.phone-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 10px 14px;
  margin: 0 16px 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
}

.phone-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 127, 255, 0.02), rgba(59, 127, 255, 0.01));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.phone-item:hover::before {
  opacity: 1;
}

.phone-item:active {
  transform: translateY(2px) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.phone-info {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 70%;
}

.phone-details {
  flex: 1;
  min-width: 0;
}

.phone-name {
  font-size: 14px;
  font-weight: 400;
  color: #1f2329;
  margin-bottom: 8px;
  line-height: 1.3;
}

.phone-number-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 3px;
}

.phone-number {
  font-size: 13px;
  color: #3B7FFF;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 0.5px;
}

.phone-description {
  font-size: 11px;
  color: #86909c;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

/* 拨号按钮 */
.phone-call {
  width: 70px;
  height: 32px;
  border-radius: 8px;
  background-color: #3B7FFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
  margin-left: 12px;
  flex-shrink: 0;
  color: #ffffff;
  position: relative;
  overflow: hidden;
  z-index: 10;
  cursor: pointer;
}

.phone-call::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.phone-call:hover::before {
  opacity: 1;
}

.phone-call:active {
  transform: scale(0.9);
  box-shadow: 0 1px 6px rgba(59, 127, 255, 0.35);
}

.call-text {
  font-size: 12px;
  color: #ffffff;
  font-weight: 500;
  line-height: 1;
}

/* 悬浮操作按钮 */
.floating-actions {
  position: fixed;
  bottom: 32px;
  right: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 100;
  transition: bottom 0.3s ease;
}

.fab-btn {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.fab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 24px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fab-btn:hover::before {
  opacity: 1;
}

.refresh-btn {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.my-submissions-btn {
  background: linear-gradient(135deg, #FF9500, #FF7A00);
}

.add-btn {
  background: linear-gradient(135deg, #3B7FFF, #1E64FF);
}

.fab-btn-hover {
  transform: scale(0.92);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

/* 拨打次数统计样式 */
.phone-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  flex-shrink: 0;
}

.phone-item:hover .phone-stats {
  opacity: 1;
}

.call-count {
  font-size: 10px;
  color: #86909c;
  font-weight: 400;
  transition: color 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .category-tabs {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .category-tab {
    flex: none;
    min-width: calc(50% - 4px);
    font-size: 11px;
    padding: 6px 8px;
    height: 32px;
  }
  
  .phone-item {
    padding: 12px 14px;
    margin: 0 12px 8px;
    border-radius: 14px;
  }
  
  .phone-call {
    width: 20%;
    height: 40px;
    border-radius: 10px;
    margin-left: 14px;
  }
  
  .fab-btn {
    width: 44px;
    height: 44px;
    border-radius: 22px;
  }
}

 