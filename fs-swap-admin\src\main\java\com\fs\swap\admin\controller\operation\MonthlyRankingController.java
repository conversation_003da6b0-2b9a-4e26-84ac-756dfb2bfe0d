package com.fs.swap.admin.controller.operation;

import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.AdminBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.domain.entity.MonthlyRanking;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.system.service.IMonthlyRankingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 月度排行榜Controller
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@RestController
@RequestMapping("/operation/monthly-ranking")
public class MonthlyRankingController extends AdminBaseController {
    
    @Autowired
    private IMonthlyRankingService monthlyRankingService;

    /**
     * 获取月度排行榜列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyRanking monthlyRanking) {
        startPage();
        List<MonthlyRanking> list = monthlyRankingService.selectMonthlyRankingList(monthlyRanking);
        return getDataTable(list);
    }

    /**
     * 重置月度排行榜
     * @param yearMonth 年月，格式：yyyy-MM
     * @param residentialId 小区ID，可选。如果不传则重置所有小区的排行榜
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking:reset')")
    @Log(title = "月度排行榜", businessType = BusinessType.UPDATE)
    @PostMapping("/reset")
    public AjaxResult reset(@RequestParam String yearMonth,
                          @RequestParam(required = false) Long residentialId) {
        monthlyRankingService.resetRanking(yearMonth, residentialId);
        return AjaxResult.success(
            residentialId != null ? 
            "重置小区ID " + residentialId + " 的月度排行榜成功" : 
            "重置全部小区的月度排行榜成功"
        );
    }
} 