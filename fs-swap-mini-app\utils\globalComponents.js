/**
 * 全局组件管理工具
 * 提供统一的组件操作接口，简化页面中的组件使用
 */

/**
 * 显示登录组件
 * @param {Object} pageContext 页面上下文
 * @param {boolean} autoClose 登录成功后是否自动关闭，默认true
 * @returns {boolean} 是否成功显示登录组件
 */
function showLoginComponent(pageContext, autoClose = true) {
  if (!pageContext) {
    console.warn('showLoginComponent: 页面上下文不能为空')
    return false
  }

  try {
    const loginComp = pageContext.selectComponent('#loginAction')
    if (loginComp) {
      loginComp.setData({ autoClose: autoClose })
      loginComp.show()
      return true
    } else {
      console.warn('showLoginComponent: 未找到登录组件，请确保页面中包含 <login-action id="loginAction"></login-action>')
      // 显示提示
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000,
      })
      return false
    }
  } catch (error) {
    console.error('showLoginComponent: 显示登录组件失败', error)
    return false
  }
}

/**
 * 隐藏登录组件
 * @param {Object} pageContext 页面上下文
 * @returns {boolean} 是否成功隐藏登录组件
 */
function hideLoginComponent(pageContext) {
  if (!pageContext) {
    console.warn('hideLoginComponent: 页面上下文不能为空')
    return false
  }

  try {
    const loginComp = pageContext.selectComponent('#loginAction')
    if (loginComp && typeof loginComp.hide === 'function') {
      loginComp.hide()
      return true
    }
    return false
  } catch (error) {
    console.error('hideLoginComponent: 隐藏登录组件失败', error)
    return false
  }
}

/**
 * 显示小区认证组件
 * @param {Object} pageContext 页面上下文
 * @returns {boolean} 是否成功显示小区认证组件
 */
function showResidentialAuthComponent(pageContext) {
  if (!pageContext) {
    console.warn('showResidentialAuthComponent: 页面上下文不能为空')
    return false
  }

  try {
    const authComp = pageContext.selectComponent('#residentialAuth')
    if (authComp) {
      authComp.show()
      return true
    } else {
      console.warn('showResidentialAuthComponent: 未找到小区认证组件，请确保页面中包含 <residential-auth id="residentialAuth"></residential-auth>')
      // 显示提示
      wx.showToast({
        title: '请先绑定小区',
        icon: 'none',
        duration: 2000,
      })
      return false
    }
  } catch (error) {
    console.error('showResidentialAuthComponent: 显示小区认证组件失败', error)
    return false
  }
}

/**
 * 隐藏小区认证组件
 * @param {Object} pageContext 页面上下文
 * @returns {boolean} 是否成功隐藏小区认证组件
 */
function hideResidentialAuthComponent(pageContext) {
  if (!pageContext) {
    console.warn('hideResidentialAuthComponent: 页面上下文不能为空')
    return false
  }

  try {
    const authComp = pageContext.selectComponent('#residentialAuth')
    if (authComp && typeof authComp.hide === 'function') {
      authComp.hide()
      return true
    }
    return false
  } catch (error) {
    console.error('hideResidentialAuthComponent: 隐藏小区认证组件失败', error)
    return false
  }
}

/**
 * 页面混入对象，提供通用的组件操作方法
 * 使用方式：在页面的 Page() 中使用 Object.assign(pageOptions, globalComponentsMixin)
 */
const globalComponentsMixin = {
  /**
   * 显示登录组件
   * @param {boolean} autoClose 登录成功后是否自动关闭，默认true
   */
  showLogin(autoClose = true) {
    return showLoginComponent(this, autoClose)
  },

  /**
   * 隐藏登录组件
   */
  hideLogin() {
    return hideLoginComponent(this)
  },

  /**
   * 显示小区认证组件
   */
  showResidentialAuth() {
    return showResidentialAuthComponent(this)
  },

  /**
   * 隐藏小区认证组件
   */
  hideResidentialAuth() {
    return hideResidentialAuthComponent(this)
  },

  /**
   * 通用的登录成功处理
   */
  onLoginSuccess() {
    console.log('登录成功')
    // 可以在这里添加通用的登录成功处理逻辑
    // 子页面可以重写这个方法来添加特定的处理逻辑
  },

  /**
   * 通用的登录失败处理
   */
  onLoginFail(e) {
    console.log('登录失败', e)
    // 可以在这里添加通用的登录失败处理逻辑
  },

  /**
   * 通用的小区认证确认处理
   */
  onConfirmResidentialAuth(e) {
    console.log('小区认证确认', e)
    // 可以在这里添加通用的小区认证确认处理逻辑
  },

  /**
   * 通用的小区认证关闭处理
   */
  onCloseResidentialAuth() {
    console.log('小区认证关闭')
    // 可以在这里添加通用的小区认证关闭处理逻辑
  }
}

/**
 * 获取标准的组件模板代码
 * 用于快速在页面中添加组件
 */
function getComponentTemplate() {
  return {
    wxml: `
<!-- 登录组件 -->
<login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>

<!-- 小区认证弹框 -->
<residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />
    `.trim(),
    
    js: `
// 引入全局组件工具
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  // 页面数据
  data: {
    // 你的页面数据
  },

  // 页面方法
  // 你的页面方法

}, globalComponents.globalComponentsMixin))
    `.trim()
  }
}

module.exports = {
  showLoginComponent,
  hideLoginComponent,
  showResidentialAuthComponent,
  hideResidentialAuthComponent,
  globalComponentsMixin,
  getComponentTemplate
}
