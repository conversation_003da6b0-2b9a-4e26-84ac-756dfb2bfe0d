package com.fs.swap.system.service.impl;

import com.fs.swap.common.core.domain.entity.MonthlyRanking;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardConfig;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardRecord;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.MonthlyRankingMapper;
import com.fs.swap.system.mapper.MonthlyRankingRewardConfigMapper;
import com.fs.swap.system.mapper.MonthlyRankingRewardRecordMapper;
import com.fs.swap.system.service.IMonthlyRankingRewardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.*;

/**
 * 月度排行榜奖励业务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class MonthlyRankingRewardServiceImpl implements IMonthlyRankingRewardService {
    
    private static final Logger log = LoggerFactory.getLogger(MonthlyRankingRewardServiceImpl.class);
    
    @Autowired
    private MonthlyRankingRewardRecordMapper monthlyRankingRewardRecordMapper;
    
    @Autowired
    private MonthlyRankingRewardConfigMapper monthlyRankingRewardConfigMapper;
    
    @Autowired
    private MonthlyRankingMapper monthlyRankingMapper;

    /**
     * 查询月度排行榜奖励发放记录
     * 
     * @param id 月度排行榜奖励发放记录主键
     * @return 月度排行榜奖励发放记录
     */
    @Override
    public MonthlyRankingRewardRecord selectMonthlyRankingRewardRecordById(Long id) {
        return monthlyRankingRewardRecordMapper.selectMonthlyRankingRewardRecordById(id);
    }

    /**
     * 查询月度排行榜奖励发放记录列表
     * 
     * @param monthlyRankingRewardRecord 月度排行榜奖励发放记录
     * @return 月度排行榜奖励发放记录
     */
    @Override
    public List<MonthlyRankingRewardRecord> selectMonthlyRankingRewardRecordList(MonthlyRankingRewardRecord monthlyRankingRewardRecord) {
        return monthlyRankingRewardRecordMapper.selectMonthlyRankingRewardRecordList(monthlyRankingRewardRecord);
    }

    /**
     * 查询指定年月的奖励发放记录（带用户信息）
     * 
     * @param yearMonth 年月
     * @param residentialId 小区ID（可选）
     * @return 奖励发放记录列表
     */
    @Override
    public List<MonthlyRankingRewardRecord> selectRewardRecordWithUserInfo(String yearMonth, Long residentialId) {
        return monthlyRankingRewardRecordMapper.selectRewardRecordWithUserInfo(yearMonth, residentialId);
    }

    /**
     * 查询用户的奖励历史记录
     * 
     * @param userId 用户ID
     * @return 用户奖励历史
     */
    @Override
    public List<MonthlyRankingRewardRecord> selectUserRewardHistory(Long userId) {
        return monthlyRankingRewardRecordMapper.selectUserRewardHistory(userId);
    }

    /**
     * 生成月度奖励记录
     * 
     * @param yearMonth 年月
     * @return 生成的奖励记录数量
     */
    @Override
    @Transactional
    public int generateMonthlyRewards(String yearMonth) {
        try {
            log.info("开始生成月度奖励记录: {}", yearMonth);
            
            // 先删除已存在的奖励记录
            monthlyRankingRewardRecordMapper.deleteByYearMonth(yearMonth);
            
            // 查询月度排行榜数据
            MonthlyRanking query = new MonthlyRanking();
            query.setYearMonth(yearMonth);
            List<MonthlyRanking> rankings = monthlyRankingMapper.selectMonthlyRankingList(query);
            
            if (rankings.isEmpty()) {
                log.warn("月度排行榜为空，无法生成奖励: {}", yearMonth);
                return 0;
            }

            // 按小区分组并计算排名
            Map<Long, List<MonthlyRanking>> residentialGroups = new HashMap<>();
            for (MonthlyRanking ranking : rankings) {
                residentialGroups.computeIfAbsent(ranking.getResidentialId(), k -> new ArrayList<>()).add(ranking);
            }

            // 获取启用的奖励配置
            List<MonthlyRankingRewardConfig> rewardConfigs = monthlyRankingRewardConfigMapper.selectActiveRewardConfigs();
            if (rewardConfigs.isEmpty()) {
                log.warn("无启用的奖励配置，无法生成奖励");
                return 0;
            }
            
            // 生成奖励记录
            List<MonthlyRankingRewardRecord> rewardRecords = new ArrayList<>();
            
            // 为每个小区计算排名并生成奖励
            for (Map.Entry<Long, List<MonthlyRanking>> entry : residentialGroups.entrySet()) {
                Long residentialId = entry.getKey();
                List<MonthlyRanking> residentialRankings = entry.getValue();
                
                // 按碳豆数量降序排序
                residentialRankings.sort((r1, r2) -> Long.compare(r2.getTotalSilver(), r1.getTotalSilver()));
                
                // 计算排名并生成奖励记录
                for (int i = 0; i < residentialRankings.size(); i++) {
                    MonthlyRanking ranking = residentialRankings.get(i);
                    int rankPosition = i + 1;  // 排名从1开始
                    
                    MonthlyRankingRewardConfig matchedConfig = findMatchingRewardConfig(rankPosition, rewardConfigs);
                    
                    if (matchedConfig != null) {
                        MonthlyRankingRewardRecord record = new MonthlyRankingRewardRecord();
                        record.setYearMonth(yearMonth);
                        record.setResidentialId(ranking.getResidentialId());
                        record.setUserId(ranking.getUserId());
                        record.setTotalSilver(ranking.getTotalSilver());
                        record.setRankPosition(rankPosition);
                        record.setRewardConfigId(matchedConfig.getId());
                        record.setRewardType(matchedConfig.getRewardType());
                        record.setRewardAmount(matchedConfig.getRewardAmount());
                        record.setRewardName(matchedConfig.getRewardName());
                        record.setStatus(MonthlyRankingRewardRecord.Status.PENDING);
                        record.setCreateTime(DateUtils.getNowDate());
                        
                        rewardRecords.add(record);
                        
                        log.debug("生成奖励记录: 小区{}, 用户{}, 排名{}, 总碳豆{}, 奖励{}", 
                            ranking.getResidentialId(), ranking.getUserId(), rankPosition, ranking.getTotalSilver(), matchedConfig.getRewardName());
                    }
                }
            }
            
            // 批量插入奖励记录
            if (!rewardRecords.isEmpty()) {
                int insertCount = monthlyRankingRewardRecordMapper.batchInsertRewardRecords(rewardRecords);
                log.info("成功生成月度奖励记录: {} 条，月份: {}", insertCount, yearMonth);
                return insertCount;
            } else {
                log.warn("没有匹配的奖励记录生成: {}", yearMonth);
                return 0;
            }
            
        } catch (Exception e) {
            log.error("生成月度奖励记录失败: {}", yearMonth, e);
            throw e;
        }
    }

    /**
     * 发放奖励给用户
     * 
     * @param rewardRecord 奖励记录
     * @return 是否发放成功
     */
    @Override
    @Transactional
    public boolean issueReward(MonthlyRankingRewardRecord rewardRecord) {
        try {
            // 这里需要根据实际业务调用相应的服务发放奖励
            // 例如：碳豆奖励调用碳豆服务，金币奖励调用金币服务
            
            boolean issueSuccess = false;
            
            switch (rewardRecord.getRewardType()) {
                case MonthlyRankingRewardConfig.RewardType.SILVER:
                    // 调用碳豆服务发放碳豆
                    issueSuccess = issueSilverReward(rewardRecord);
                    break;
                case MonthlyRankingRewardConfig.RewardType.COIN:
                    // 调用金币服务发放金币
                    issueSuccess = issueCoinReward(rewardRecord);
                    break;
                default:
                    log.warn("未知的奖励类型: {}", rewardRecord.getRewardType());
                    break;
            }
            
            // 更新发放状态
            String status = issueSuccess ? MonthlyRankingRewardRecord.Status.SUCCESS : MonthlyRankingRewardRecord.Status.FAILED;
            monthlyRankingRewardRecordMapper.updateRewardStatus(rewardRecord.getId(), status);
            
            if (issueSuccess) {
                log.info("奖励发放成功: userId={}, rewardType={}, rewardAmount={}", 
                        rewardRecord.getUserId(), rewardRecord.getRewardType(), rewardRecord.getRewardAmount());
            } else {
                log.error("奖励发放失败: userId={}, rewardType={}, rewardAmount={}", 
                        rewardRecord.getUserId(), rewardRecord.getRewardType(), rewardRecord.getRewardAmount());
            }
            
            return issueSuccess;
            
        } catch (Exception e) {
            log.error("发放奖励异常: userId={}, rewardType={}", rewardRecord.getUserId(), rewardRecord.getRewardType(), e);
            
            // 标记为发放失败
            try {
                monthlyRankingRewardRecordMapper.updateRewardStatus(rewardRecord.getId(), MonthlyRankingRewardRecord.Status.FAILED);
            } catch (Exception updateEx) {
                log.error("更新奖励状态失败", updateEx);
            }
            
            return false;
        }
    }

    /**
     * 批量发放待发放的奖励
     * 
     * @return 发放成功的奖励数量
     */
    @Override
    public int batchIssueRewards() {
        try {
            List<MonthlyRankingRewardRecord> pendingRewards = monthlyRankingRewardRecordMapper.selectPendingRewards();
            
            if (pendingRewards.isEmpty()) {
                log.info("无待发放的奖励");
                return 0;
            }
            
            int successCount = 0;
            
            for (MonthlyRankingRewardRecord record : pendingRewards) {
                if (issueReward(record)) {
                    successCount++;
                }
                
                // 防止批量处理时间过长，可以考虑分批处理
                if (successCount % 100 == 0) {
                    log.info("已处理奖励数量: {}", successCount);
                }
            }
            
            log.info("批量发放奖励完成: 成功 {} 条，总共 {} 条", successCount, pendingRewards.size());
            return successCount;
            
        } catch (Exception e) {
            log.error("批量发放奖励失败", e);
            return 0;
        }
    }

    /**
     * 删除指定年月的奖励记录
     * 
     * @param yearMonth 年月
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRewardsByYearMonth(String yearMonth) {
        return monthlyRankingRewardRecordMapper.deleteByYearMonth(yearMonth);
    }

    /**
     * 查找匹配的奖励配置
     */
    private MonthlyRankingRewardConfig findMatchingRewardConfig(Integer rankPosition, List<MonthlyRankingRewardConfig> configs) {
        for (MonthlyRankingRewardConfig config : configs) {
            if (rankPosition >= config.getRankStart() && rankPosition <= config.getRankEnd()) {
                return config;
            }
        }
        return null;
    }

    /**
     * 发放碳豆奖励
     */
    private boolean issueSilverReward(MonthlyRankingRewardRecord rewardRecord) {
        try {
            // TODO: 调用碳豆服务发放碳豆
            // silverService.addSilver(rewardRecord.getUserId(), rewardRecord.getRewardAmount(), "月度排行榜奖励");
            
            // 暂时返回true，实际需要根据业务服务的返回结果
            log.info("发放碳豆奖励: userId={}, amount={}", rewardRecord.getUserId(), rewardRecord.getRewardAmount());
            return true;
        } catch (Exception e) {
            log.error("发放碳豆奖励失败", e);
            return false;
        }
    }

    /**
     * 发放金币奖励
     */
    private boolean issueCoinReward(MonthlyRankingRewardRecord rewardRecord) {
        try {
            // TODO: 调用金币服务发放金币
            log.info("发放金币奖励: 用户{}, 金币数量{}", rewardRecord.getUserId(), rewardRecord.getRewardAmount());
            
            // 模拟发放成功
            return true;
        } catch (Exception e) {
            log.error("发放金币奖励失败: 用户{}, 金币数量{}", rewardRecord.getUserId(), rewardRecord.getRewardAmount(), e);
            return false;
        }
    }

    /**
     * 获取上期榜单列表（从奖励记录中查询）
     * 
     * @param yearMonth 年月（格式：yyyy-MM）
     * @param residentialId 小区ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 上期榜单数据
     */
    @Override
    public com.fs.swap.common.core.page.TableDataInfo getPreviousRankingList(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize) {
        try {
            log.info("查询上期榜单: 年月{}, 小区ID{}, 页码{}, 每页{}", yearMonth, residentialId, pageNum, pageSize);
            
            // 使用PageHelper进行分页
            PageHelper.startPage(pageNum, pageSize);
            
            // 查询分页数据（不需要手动计算offset）
            List<MonthlyRankingRewardRecord> rewardRecords = monthlyRankingRewardRecordMapper.selectRewardRecordWithUserInfo(yearMonth, residentialId);
            
            // 使用PageInfo获取分页信息
            PageInfo<MonthlyRankingRewardRecord> pageInfo = new PageInfo<>(rewardRecords);
            
            // 转换为前端需要的格式
            List<Map<String, Object>> rankingList = new ArrayList<>();
            for (MonthlyRankingRewardRecord record : rewardRecords) {
                Map<String, Object> item = new HashMap<>();
                item.put("userId", record.getUserId());
                item.put("rank", record.getRankPosition());
                item.put("rankPosition", record.getRankPosition());
                item.put("nickname", record.getNickname());
                item.put("avatar", record.getAvatar());
                item.put("totalSilver", record.getTotalSilver() != null ? record.getTotalSilver() : 0L); // 显示用户的总碳豆数量
                item.put("residentialId", record.getResidentialId());
                item.put("reward", record.getRewardName());
                item.put("rewardName", record.getRewardName());
                item.put("rewardAmount", record.getRewardAmount());
                item.put("rewardType", record.getRewardType());
                item.put("status", record.getStatus());
                
                rankingList.add(item);
            }
            
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(0);
            rspData.setRows(rankingList);
            rspData.setTotal((int) pageInfo.getTotal());
            
            log.info("上期榜单查询完成: 总数{}, 返回{}", pageInfo.getTotal(), rankingList.size());
            return rspData;
            
        } catch (Exception e) {
            log.error("查询上期榜单失败: 年月{}, 小区ID{}", yearMonth, residentialId, e);
            
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(500);
            rspData.setRows(new ArrayList<>());
            rspData.setTotal(0);
            return rspData;
        }
    }
} 