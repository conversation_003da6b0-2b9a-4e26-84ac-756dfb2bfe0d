package com.fs.swap.wx.domain.vo;

import lombok.Data;

/**
 * 社区服务-常用电话VO
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
public class CommunityPhoneVO {

    /**
     * 电话ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 分类
     */
    private String category;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 审核状态(0:待审核,1:已通过,2:已拒绝)
     */
    private Long auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 拨打次数
     */
    private Integer callCount;

    /**
     * 创建时间
     */
    private java.util.Date createTime;
}
