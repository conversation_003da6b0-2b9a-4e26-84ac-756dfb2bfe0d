<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="发布者id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入发布者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
          <area-chain-selector
        :regionId="queryParams.regionId"
        :communityId="queryParams.communityId"
        :residentialId="queryParams.residentialId"
        @update:regionId="val => queryParams.regionId = val"
        @update:communityId="val => queryParams.communityId = val"
        @update:residentialId="val => queryParams.residentialId = val"
        :labels="{
          region: '行政区域',
          community: '社区',
          residential: '小区'
        }"
        :showCommunity="true"
        :showResidential="true"
        :inlineLayout="true"
        ref="queryAreaSelector"
        @community-change="handleQuery"
        @region-change="handleRegionChange"
      />
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地点" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择分类" clearable>
          <el-option
            v-for="dict in dict.type.activity_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否免费" prop="isFree">
        <el-select v-model="queryParams.isFree" placeholder="请选择是否免费" clearable>
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.activity_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:activity:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:activity:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:activity:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:activity:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="activityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="id" />
      <el-table-column label="发布者" align="center" prop="userId" />
      <el-table-column label="行政区域" align="center" prop="regionId" :formatter="regionFormatter" />
      <el-table-column label="社区" align="center" prop="communityId" :formatter="communityFormatter" />
      <el-table-column label="小区" align="center" prop="residentialId" :formatter="residentialFormatter" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="图片" align="center" prop="images" width="300">
        <template slot-scope="scope">
          <div class="image-list">
            <template v-if="scope.row.images">
              <image-preview
                v-for="(img, index) in scope.row.images.split(',')"
                :key="index"
                :src="filePrefix + img"
                :width="50"
                :height="50"
                style="margin: 2px;"
              />
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="地点" align="center" prop="address" />
      <el-table-column label="坐标" align="center" prop="location" />
      <el-table-column label="分类" align="center" prop="category">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.activity_category" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="活动开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名开始时间" align="center" prop="signupStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.signupStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报名截止时间" align="center" prop="signupEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.signupEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最大参与人数" align="center" prop="maxParticipants" />
      <el-table-column label="是否免费" align="center" prop="isFree">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isFree"/>
        </template>
      </el-table-column>
      <el-table-column label="活动费用" align="center" prop="feeAmount" />
      <el-table-column label="费用说明" align="center" prop="feeDescription" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.activity_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:activity:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:activity:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="发布者" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入发布者" />
        </el-form-item>
        <area-chain-selector
          :regionId="form.regionId"
          :communityId="form.communityId"
          :residentialId="form.residentialId"
          @update:regionId="val => form.regionId = val"
          @update:communityId="val => form.communityId = val"
          @update:residentialId="val => form.residentialId = val"
          :isLoading="isLoadingFormData"
          :showCommunity="true"
          :showResidential="true"
          ref="areaSelector"
        />
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图片" prop="images">
          <image-upload v-model="form.images" :type="3"/>
        </el-form-item>
        <el-form-item label="地点" prop="address">
          <el-input v-model="form.address" placeholder="请输入地点" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option
              v-for="dict in dict.type.activity_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动结束时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="报名开始时间" prop="signupStartTime">
          <el-date-picker clearable
            v-model="form.signupStartTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择报名开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="报名截止时间" prop="signupEndTime">
          <el-date-picker clearable
            v-model="form.signupEndTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择报名截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最大参与人数，NULL表示不限制" prop="maxParticipants">
          <el-input v-model="form.maxParticipants" placeholder="请输入最大参与人数，NULL表示不限制" />
        </el-form-item>
        <el-form-item label="是否免费" prop="isFree">
          <el-select v-model="form.isFree" placeholder="请选择是否免费">
            <el-option
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动费用" prop="feeAmount">
          <el-input v-model="form.feeAmount" placeholder="请输入活动费用" />
        </el-form-item>
        <el-form-item label="费用说明" prop="feeDescription">
          <el-input v-model="form.feeDescription" placeholder="请输入费用说明" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option
              v-for="dict in dict.type.activity_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listActivity, getActivity, delActivity, addActivity, updateActivity } from "@/api/operation/activity";
import AreaChainSelector from '@/components/AreaChainSelector'
import { regionFormatter, communityFormatter, residentialFormatter } from '@/utils/areaFormatter'

export default {
  name: "Activity",
  components: {
    AreaChainSelector
  },
  dicts: ['activity_status', 'sys_yes_no', 'activity_category'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 文件前缀
      filePrefix: process.env.VUE_APP_FILE_URL,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动信息表格数据
      activityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否正在加载表单数据
      isLoadingFormData: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        regionId: null,
        residentialId: null,
        communityId: null,
        title: null,
        description: null,
        images: null,
        address: null,
        location: null,
        category: null,
        startTime: null,
        endTime: null,
        signupStartTime: null,
        signupEndTime: null,
        maxParticipants: null,
        isFree: null,
        feeAmount: null,
        feeDescription: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "发布者不能为空", trigger: "blur" }
        ],
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "活动开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "活动结束时间不能为空", trigger: "blur" }
        ],
        signupStartTime: [
          { required: true, message: "报名开始时间不能为空", trigger: "blur" }
        ],
        signupEndTime: [
          { required: true, message: "报名截止时间不能为空", trigger: "blur" }
        ],
        isFree: [
          { required: true, message: "是否免费不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  async created() {
    // 加载数据（包含预加载区域数据）
    await this.initData();
  },
  methods: {
    /** 初始化数据 */
    async initData() {
      try {
        // 直接加载活动列表，区域数据会在需要时由组件自动加载
        this.getList();
      } catch (error) {
        this.$message.error('加载数据失败，请重试');
      }
    },

    /**
     * 通用方法：处理区域级联的数据初始化
     * @param {Object} data - 包含区域、社区、小区ID的数据对象
     * @returns {Promise<boolean>} - 初始化结果
     */
    async initAreaSelector(data) {
      try {
        // 等待DOM更新
        await this.$nextTick();

        // 初始化区域选择器
        if (this.$refs.areaSelector) {
          if (data.regionId) {
            // 使用 AreaChainSelector 组件的 initForEdit 方法
            await this.$refs.areaSelector.initForEdit({
              regionId: data.regionId,
              communityId: data.communityId,
              residentialId: data.residentialId
            });

            // 检查初始化后的值
            await this.$nextTick();
          }
        }

        return true;
      } catch (error) {
        return false;
      }
      // 注意：不在这里重置isLoadingFormData，由调用方负责重置
    },

    /** 查询活动信息列表 */
    getList() {
      this.loading = true;
      listActivity(this.queryParams).then(response => {
        this.activityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();

      // 确保区域选择器被重置
      if (this.$refs.areaSelector) {
        this.$refs.areaSelector.clearAll();
      }

      // 重置加载表单数据标志
      this.isLoadingFormData = false;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        regionId: null,
        residentialId: null,
        communityId: null,
        title: null,
        description: null,
        images: null,
        address: null,
        location: null,
        category: null,
        startTime: null,
        endTime: null,
        signupStartTime: null,
        signupEndTime: null,
        maxParticipants: null,
        isFree: null,
        feeAmount: null,
        feeDescription: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset();

      // 设置正在加载表单数据标志，防止触发级联清空
      this.isLoadingFormData = true;

      try {
        // 显示loading
        const loading = this.$loading({
          lock: true,
          text: '准备数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 不需要预检查区域数据，AreaChainSelector组件内部会处理

        this.open = true;
        this.title = "添加活动信息";

        // 等待DOM更新
        await this.$nextTick();

        // 确保区域选择器被重置
        if (this.$refs.areaSelector) {
          this.$refs.areaSelector.clearAll();
        }

        // 关闭loading
        loading.close();
      } catch (error) {
        this.$message.error('准备数据失败，请重试');
      } finally {
        // 重置加载表单数据标志
        setTimeout(() => {
          this.isLoadingFormData = false;
        }, 300);
      }
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;

      try {
        // 标记正在加载表单数据，防止触发级联清空
        this.isLoadingFormData = true;

        // 显示loading - 只使用一个loading实例
        const loading = this.$loading({
          lock: true,
          text: '加载数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 1. 不需要预加载区域数据，AreaChainSelector组件内部会处理

        // 2. 获取活动详细信息
        const response = await getActivity(id);

        // 3. 设置表单数据
        this.form = response.data;

        // 4. 不需要预检查社区和小区数据，AreaChainSelector组件内部会处理

        // 5. 所有数据准备就绪后，再打开对话框
        this.title = "修改活动信息";
        this.open = true;

        // 6. 等待DOM更新
        await this.$nextTick();

        // 7. 初始化区域选择器
        await this.initAreaSelector({
          regionId: this.form.regionId,
          communityId: this.form.communityId,
          residentialId: this.form.residentialId
        });

        // 8. 关闭loading
        loading.close();

      } catch (error) {
        this.$message.error('加载数据失败，请重试');
        if (this.$loading) {
          this.$loading().close();
        }
      } finally {
        // 重置加载状态 - 不使用延迟
        this.isLoadingFormData = false;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateActivity(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addActivity(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除活动信息编号为"' + ids + '"的数据项？').then(function() {
        return delActivity(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/activity/export', {
        ...this.queryParams
      }, `activity_${new Date().getTime()}.xlsx`)
    },

    handleRegionChange() {
      this.handleQuery();
    },

    // 使用通用的区域格式化方法
    regionFormatter,

    // 使用通用的社区格式化方法
    communityFormatter,

    // 使用通用的小区格式化方法
    residentialFormatter
  }
};
</script>
