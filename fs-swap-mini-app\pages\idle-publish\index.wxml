<!--pages/idle-publish/index.wxml-->
<view class="container">
  <form bindsubmit="handleSubmit">
    <view class="content-card">
      <view class="description-area">
        <textarea 
          name="description" 
          placeholder="描述宝贝的品牌型号、功能成色、出手原因..." 
          value="{{ formData.description }}"
          bindinput="onDescriptionChange"
        />
      </view>
      <view class="upload-section">
        <van-uploader 
          file-list="{{ formData.fileList }}" 
          deletable="{{ true }}"
          bind:after-read="afterRead"
          bind:delete="onDeleteFile"
          max-count="{{MAX_UPLOAD_COUNT}}"
          multiple="{{true}}"
          upload-text="添加图片"
          image-fit="aspectFill"
        />
      </view>
    </view>

    <view class="trade-card">
      <view class="condition-section" bindtap="showPriceInput">
        <text class="condition-label">价格</text>
        <view class="condition-value">
          <text>¥{{formData.price || '0.00'}}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="showWearPicker">
        <text class="condition-label">成色</text>
        <view class="condition-value">
          <text>{{ formData.wearText || '请选择' }}</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="divider"></view>
      <view class="condition-section" bindtap="showContactPicker">
        <text class="condition-label">联系方式</text>
        <view class="condition-value">
          <view class="contact-icons">
            <van-icon name="qr-invalid" size="20px" color="{{ contactInfo.wechatQrVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="wechat" size="20px" color="{{ contactInfo.wechatVisible ? '#4080FF' : '#cccccc' }}" />
            <van-icon name="phone" size="20px" color="{{ contactInfo.mobileVisible ? '#4080FF' : '#cccccc' }}" />
          </view>
          <text class="arrow">></text>
        </view>
      </view>
      <!-- 联系方式提示信息 -->
      <view class="contact-tip" wx:if="{{ !hasValidContact }}">
        <van-icon name="info-o" size="14px" color="#ff6b6b" />
        <text>请至少维护一种联系方式</text>
      </view>
    </view>

    <view class="submit-section">
      <button
        form-type="submit"
        class="publish-btn"
        loading="{{ publishing }}"
        disabled="{{ !formValid }}"
      >发布</button>
    </view>
  </form>

  <!-- 成色选择器 -->
  <van-popup
    show="{{ showWearPicker }}"
    position="bottom"
    round
    bind:close="onCloseWearPicker"
  >
    <van-picker
      show-toolbar
      title="选择成色"
      columns="{{ wearOptions }}"
      bind:cancel="onCloseWearPicker"
      bind:confirm="onSelectWear"
      default-index="{{ wearIndex }}"
    />
  </van-popup>

  <!-- 价格输入弹窗 -->
  <van-popup
    show="{{showPriceInput}}"
    position="bottom"
    round
    bind:close="onClosePriceInput"
    custom-style="background: #f7f8fa;"
  >
    <view class="price-input-popup">
      <view class="price-display">
        <text class="price-label">价格</text>
        <view class="value">
          <text class="currency">¥</text>
          <text class="price-value">{{formData.price || '0.00'}}</text>
        </view>
      </view>
      <view class="keyboard-container">
        <view class="keyboard-wrapper">
          <view class="keyboard-left">
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="1">1</view>
              <view class="key-item" bindtap="onKeyPress" data-key="2">2</view>
              <view class="key-item" bindtap="onKeyPress" data-key="3">3</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="4">4</view>
              <view class="key-item" bindtap="onKeyPress" data-key="5">5</view>
              <view class="key-item" bindtap="onKeyPress" data-key="6">6</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key="7">7</view>
              <view class="key-item" bindtap="onKeyPress" data-key="8">8</view>
              <view class="key-item" bindtap="onKeyPress" data-key="9">9</view>
            </view>
            <view class="keyboard-row">
              <view class="key-item" bindtap="onKeyPress" data-key=".">.</view>
              <view class="key-item" bindtap="onKeyPress" data-key="0">0</view>
              <view class="key-item" bindtap="onClosePriceInput">
                <van-icon name="arrow-down" size="36rpx" />
              </view>
            </view>
          </view>
          <view class="keyboard-right">
            <view class="key-item delete" bindtap="onDelete">
              <van-icon name="cross" size="36rpx" />
            </view>
            <view class="key-item confirm" bindtap="onConfirmPrice">确定</view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 引入小区认证弹窗组件 -->
  <residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />

  <!-- 联系方式管理组件 -->
  <contact-manager
    show="{{ showContactPicker }}"
    contactInfo="{{ contactInfo }}"
    bind:close="onCloseContactPicker"
    bind:contactChange="onContactChange"
  />
</view> 