import request from '@/utils/request'

// 查询用户任务列表
export function listUserTask(query) {
  return request({
    url: '/operation/user-task/list',
    method: 'get',
    params: query
  })
}

// 查询用户任务详细
export function getUserTask(id) {
  return request({
    url: '/operation/user-task/' + id,
    method: 'get'
  })
}

// 领取任务奖励
export function claimTaskReward(userId, taskCode) {
  return request({
    url: '/operation/user-task/claim',
    method: 'post',
    data: {
      userId: userId,
      taskCode: taskCode
    }
  })
}

// 刷新用户任务
export function refreshTask(data) {
  return request({
    url: '/operation/user-task/refresh',
    method: 'post',
    data: data
  })
}

// 导出用户任务
export function exportUserTask(query) {
  return request({
    url: '/operation/user-task/export',
    method: 'get',
    params: query
  })
} 