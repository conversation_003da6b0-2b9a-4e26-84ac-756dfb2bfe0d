<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- 小区选择器 -->
      <area-chain-selector
        :regionId="queryParams.regionId"
        :communityId="queryParams.communityId"
        :residentialId="queryParams.residentialId"
        @update:regionId="val => queryParams.regionId = val"
        @update:communityId="val => queryParams.communityId = val"
        @update:residentialId="val => queryParams.residentialId = val"
        :labels="{
          region: '行政区域',
          community: '社区',
          residential: '小区'
        }"
        :showCommunity="true"
        :showResidential="true"
        :inlineLayout="true"
        ref="queryAreaSelector"
      />
      <el-form-item label="年月" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="请选择年月"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleReset"
          v-hasPermi="['operation:monthly-ranking:reset']"
        >重置排行榜</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:monthly-ranking:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="rankingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="年月" align="center" prop="yearMonth" width="100" />
      <el-table-column label="排名" align="center" prop="rankPosition" width="80">
        <template slot-scope="scope">
          <el-tag :type="getRankTagType(scope.row.rankPosition)" size="small">
            第{{scope.row.rankPosition}}名
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用户头像" align="center" width="80">
        <template slot-scope="scope">
          <image-preview
            :src="scope.row.avatar || defaultAvatar"
            :width="50"
            :height="50"
            fit="cover"
            style="border-radius: 50%;"
          />
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickname" />
      <el-table-column label="碳豆数量" align="center" prop="totalSilver" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 重置确认对话框 -->
    <el-dialog
      title="确认重置排行榜"
      :visible.sync="resetDialogVisible"
      width="400px"
      :before-close="() => { this.resetDialogVisible = false }"
    >
      <p>确定要重置 <strong>{{ resetParams.yearMonth }}</strong> 月份的排行榜数据吗？</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="resetLoading" @click="confirmReset">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listMonthlyRanking, resetMonthlyRanking } from '@/api/operation/monthly-ranking'
import AreaChainSelector from '@/components/AreaChainSelector'

export default {
  name: 'MonthlyRanking',
  components: {
    AreaChainSelector
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 排行榜表格数据
      rankingList: [],
      // 默认头像
      defaultAvatar: require('@/assets/images/profile.jpg'),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        yearMonth: null,
        regionId: null,
        communityId: null,
        residentialId: null
      },
      // 重置对话框
      resetDialogVisible: false,
      resetLoading: false,
      resetParams: {
        yearMonth: null,
        residentialId: null
      }
    }
  },
  created() {
    // 默认查询当前月份
    const now = new Date()
    this.queryParams.yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    this.getList()
  },
  methods: {
    /** 查询排行榜列表 */
    getList() {
      this.loading = true
      listMonthlyRanking(this.queryParams).then(response => {
        this.rankingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        yearMonth: null,
        residentialId: null,
        userId: null,
        totalSilver: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.$refs.queryAreaSelector.reset()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 重置排行榜按钮操作 */
    handleReset() {
      if (!this.queryParams.yearMonth) {
        this.$modal.msgError('请先选择要重置的年月')
        return
      }

      // 设置重置参数
      this.resetParams.yearMonth = this.queryParams.yearMonth
      this.resetParams.residentialId = this.queryParams.residentialId

      this.resetDialogVisible = true
    },
    /** 确认重置 */
    confirmReset() {
      this.resetLoading = true
      resetMonthlyRanking(this.resetParams.yearMonth, this.resetParams.residentialId).then(response => {
        this.$modal.msgSuccess(response.msg)
        this.resetDialogVisible = false
        this.getList()
      }).finally(() => {
        this.resetLoading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/monthly-ranking/export', {
        ...this.queryParams
      }, `monthly_ranking_${new Date().getTime()}.xlsx`)
    },
    /** 获取排名标签类型 */
    getRankTagType(rank) {
      if (rank === 1) return 'danger'    // 第一名红色
      if (rank === 2) return 'warning'   // 第二名橙色
      if (rank === 3) return 'success'   // 第三名绿色
      return 'info'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
