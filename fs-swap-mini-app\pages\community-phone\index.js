const util = require('../../utils/util.js')
const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo.js')
const userUtils = require('../../utils/user.js')
const app = getApp()

Page({
    data: {
        // 筛选相关
        phoneFilter: 'all', // 电话筛选：all, property, service, emergency
        searchValue: '', // 搜索关键词

        // 分类数据（从后端字典接口获取）
        categories: [],

        // 列表数据
        phoneList: [], // 原始电话列表
        filteredList: [], // 筛选后的电话列表

        // 分类是否有数据的标志（动态生成）
        // hasPropertyPhones, hasServicePhones, hasEmergencyPhones 等

        // 加载状态
        loading: false,

        // 分类名称映射（从后端字典接口获取）
        categoryNames: {},

        // 防重复点击
        isCalling: false,
    },

    onLoad: function () {
        // 重置拨号状态
        this.setData({ isCalling: false })

        // 加载分类数据
        this.loadCategories()

        // 加载电话数据
        this.loadPhoneData()
    },

    onShow: function () {
        // 重置拨号状态
        this.setData({ isCalling: false })
    },

    // 下拉刷新
    onPullDownRefresh: function () {
        this.loadPhoneData()
    },

    // 加载分类数据（使用统一的系统信息服务）
    loadCategories: async function () {
        try {
            // 使用统一的系统信息服务获取格式化的电话分类数据
            const categories =
                await systemInfoService.getFormattedPhoneCategories()
            const categoryNames =
                await systemInfoService.getPhoneCategoryNameMap()

            this.setData({
                categories: categories,
                categoryNames: categoryNames,
            })
        } catch (error) {
            console.error('加载分类数据失败:', error)

            // 设置默认的分类数据，确保页面能正常工作
            const defaultCategories = [{ id: 'all', name: '全部' }]

            const defaultCategoryNames = {}

            this.setData({
                categories: defaultCategories,
                categoryNames: defaultCategoryNames,
            })

            // 显示错误提示，但不影响页面功能
            wx.showToast({
                title: '分类加载失败，请重试',
                icon: 'none',
                duration: 2000,
            })
        }
    },

    // 加载电话数据
    loadPhoneData: function () {
        this.setData({ loading: true })

        // 获取当前小区ID
        const localResidentialService = require('../../services/localResidential')
        localResidentialService.init()
        const residentialId = localResidentialService.getCurrentResidentialId()

        // 调用API获取电话数据，传递小区参数
        const params = residentialId ? { residentialId } : {}
        api.getCommunityPhoneList(params)
            .then((res) => {
                if (res.code === 200 && res.data) {
                    let phoneList = res.data || []

                    // 处理手机号隐私
                    const processedList = this.processPhoneList(phoneList)

                    this.setData({
                        phoneList: processedList,
                        loading: false,
                    })

                    // 应用筛选
                    this.applyFilters()

                    // 停止下拉刷新
                    wx.stopPullDownRefresh()
                } else {
                    throw new Error(res.msg || '获取电话列表失败')
                }
            })
            .catch((error) => {
                this.setData({ loading: false })

                // 停止下拉刷新
                wx.stopPullDownRefresh()

                // 检查是否是未选择小区的错误
                if (
                    error.message &&
                    error.message.includes('请先选择您所在的小区')
                ) {
                    wx.showModal({
                        title: '提示',
                        content: '使用社区服务需要先选择您所在的小区',
                        confirmText: '去选择',
                        cancelText: '取消',
                        success: (res) => {
                            if (res.confirm) {
                                // 跳转到小区选择页面
                                wx.navigateTo({
                                    url: '/pages/residential/index',
                                })
                            }
                        },
                    })
                } else {
                    wx.showToast({
                        title: '加载失败，请重试',
                        icon: 'none',
                    })
                }
            })
    },

    // 应用筛选条件
    applyFilters: function () {
        let filteredList = [...this.data.phoneList]

        // 分类筛选
        if (this.data.phoneFilter !== 'all') {
            filteredList = filteredList.filter(
                (item) => item.category === this.data.phoneFilter
            )
        }

        // 搜索筛选
        if (this.data.searchValue) {
            const keyword = this.data.searchValue.toLowerCase()
            filteredList = filteredList.filter(
                (item) =>
                    item.name.toLowerCase().includes(keyword) ||
                    item.displayPhoneNumber.includes(keyword) ||
                    (item.description &&
                        item.description.toLowerCase().includes(keyword))
            )
        }

        this.setData({
            filteredList: filteredList,
        })

        // 更新分类状态
        this.updateCategoriesStatus()
    },

    // 更新分类状态
    updateCategoriesStatus: function () {
        const { filteredList, categories } = this.data

        // 动态检查每个分类是否有数据
        const categoryStatus = {}

        // 遍历所有分类，检查是否有对应的电话数据
        categories.forEach((category) => {
            if (category.id !== 'all') {
                categoryStatus[
                    `has${
                        category.id.charAt(0).toUpperCase() +
                        category.id.slice(1)
                    }Phones`
                ] = filteredList.some((item) => item.category === category.id)
            }
        })

        this.setData(categoryStatus)
    },

    // 检查分类是否有数据
    hasCategoryPhones: function (categoryId) {
        const { filteredList } = this.data
        return filteredList.some((item) => item.category === categoryId)
    },

    // 分类筛选变化
    onPhoneFilterChange: function (event) {
        const filter = event.currentTarget.dataset.filter
        this.setData({
            phoneFilter: filter,
        })
        this.applyFilters()
    },

    // 搜索变化
    onSearchChange: function (event) {
        this.setData({
            searchValue: event.detail,
        })
        // 直接应用筛选，无需防抖
        this.applyFilters()
    },

    // 拨打电话
    onCallPhone: function (event) {
        // 防重复点击
        if (this.data.isCalling) {
            return
        }

        const item = event.currentTarget.dataset.item

        if (!item) {
            wx.showToast({
                title: '数据错误',
                icon: 'none',
            })
            return
        }

        const phoneNumber = item.phoneNumber

        // 验证电话号码
        if (!phoneNumber || phoneNumber.trim() === '') {
            wx.showToast({
                title: '电话号码无效',
                icon: 'none',
            })
            return
        }

        // 检查登录状态，拨打电话需要登录
        this.ensureLogin(() => {
            // 登录成功后显示确认弹框
            wx.showModal({
                title: '拨打电话',
                content: `确定要拨打 ${item.name} 的电话 ${phoneNumber} 吗？`,
                confirmText: '拨打',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        // 记录拨打行为并拨打电话
                        this.recordPhoneCallAndCall(item, phoneNumber)
                    }
                },
                fail: (err) => {
                    wx.showToast({
                        title: '弹框显示失败',
                        icon: 'none',
                    })
                },
            })
        })
    },

    // 记录拨打电话行为并拨打电话
    recordPhoneCallAndCall: function (item, phoneNumber) {
        // 设置防重复点击状态
        this.setData({ isCalling: true })

        // 直接调用记录接口
        api.recordPhoneCall(item.id)

        // 拨打电话
        wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
                // 延迟重置状态，避免快速重复点击
                setTimeout(() => {
                    this.setData({ isCalling: false })
                }, 2000)
            },
            fail: (err) => {
                // 根据错误类型显示不同的提示
                let errorMessage = '拨打失败，请手动拨号'
                if (err.errMsg && err.errMsg.includes('cancel')) {
                    errorMessage = '用户取消拨号'
                } else if (err.errMsg && err.errMsg.includes('permission')) {
                    errorMessage = '拨号权限被拒绝'
                }

                wx.showToast({
                    title: errorMessage,
                    icon: 'none',
                })

                // 重置状态
                this.setData({ isCalling: false })
            },
        })
    },

    // 更新通话次数
    updateCallCount: function (phoneId) {
        if (!phoneId) return

        // 调用API更新通话次数
        api.updatePhoneCallCount(phoneId)
            .then((res) => {
                if (res.code === 200) {
                    // 更新本地数据中的通话次数
                    const phoneList = this.data.phoneList.map((item) => {
                        if (item.id === phoneId) {
                            return {
                                ...item,
                                callCount: (item.callCount || 0) + 1,
                            }
                        }
                        return item
                    })

                    this.setData({ phoneList })
                    this.applyFilters() // 重新应用筛选以更新显示
                }
            })
            .catch((error) => {
                console.error('更新通话次数失败:', error)
                // 即使更新失败也不影响用户体验，静默处理
            })
    },

    // 添加电话按钮点击
    onAddPhone: function () {
        // 检查小区认证状态
        userUtils.ensureResidentialAuth(this, () => {
            // 小区认证通过，跳转到发布页面
            wx.navigateTo({
                url: '/pages/community-phone-publish/index',
            })
        })
    },

    // 刷新数据
    refreshData: function () {
        this.loadPhoneData()
        wx.showToast({
            title: '刷新成功',
            icon: 'success',
            duration: 1500,
        })
    },

    // 查看我的提交历史
    onMySubmissionsButtonTap: function () {
        // 检查登录状态，查看提交记录需要登录
        this.ensureLogin(() => {
            wx.navigateTo({
                url: '/pages/community-my-submissions/index?type=phone',
            })
        })
    },

    // 查看详情
    onViewDetail(e) {
        const phoneId = e.currentTarget.dataset.id
        wx.navigateTo({
            url: `/pages/community-phone/detail/detail?id=${phoneId}`,
        })
    },

    // 编辑号码
    onEdit(e) {
        const phoneInfo = e.currentTarget.dataset.item
        wx.navigateTo({
            url: `/pages/community-phone/edit/edit?id=${phoneInfo.id}`,
        })
    },

    // 删除号码
    onDelete(e) {
        const phoneId = e.currentTarget.dataset.id

        wx.showModal({
            title: '确认删除',
            content: '确定要删除这个号码吗？',
            success: async (res) => {
                if (res.confirm) {
                    try {
                        const result = await api.deleteCommunityPhone(phoneId)
                        if (result.code === 200) {
                            wx.showToast({
                                title: '删除成功',
                                icon: 'success',
                            })

                            // 重新加载数据
                            this.loadPhoneData()
                        } else {
                            wx.showToast({
                                title: result.msg || '删除失败',
                                icon: 'none',
                            })
                        }
                    } catch (error) {
                        wx.showToast({
                            title: '删除失败，请重试',
                            icon: 'none',
                        })
                    }
                }
            },
        })
    },

    // 手机号隐私处理
    maskPhoneNumber: function (phoneNumber) {
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return phoneNumber
        }

        const phone = phoneNumber.trim()

        // 1. 11位手机号（1开头）：隐藏中间4位
        if (phone.length === 11 && /^1[3-9]\d{9}$/.test(phone)) {
            return phone.substring(0, 3) + '****' + phone.substring(7)
        }

        // 2. 座机号码（区号+号码，通常7-12位）
        if (phone.length >= 7 && phone.length <= 12) {
            // 判断是否为座机号（以0开头或3-4位区号）
            if (/^0\d{2,3}/.test(phone) || /^\d{3,4}-\d{7,8}/.test(phone)) {
                // 座机号：保留前3位和后4位，中间用*代替
                const start = phone.substring(0, 3)
                const end = phone.substring(phone.length - 4)
                const middle = '*'.repeat(Math.max(2, phone.length - 7))
                return start + middle + end
            }
        }

        // 3. 服务号码（400、800、95等开头）
        if (/^(400|800|95\d{2})\d{6,8}$/.test(phone)) {
            // 服务号码：保留前4位和后3位
            const start = phone.substring(0, 4)
            const end = phone.substring(phone.length - 3)
            const middle = '*'.repeat(phone.length - 7)
            return start + middle + end
        }

        // 4. 短号码（5-6位）
        if (phone.length >= 5 && phone.length <= 6) {
            // 短号码：保留首尾各1位
            const start = phone.substring(0, 1)
            const end = phone.substring(phone.length - 1)
            const middle = '*'.repeat(phone.length - 2)
            return start + middle + end
        }

        // 5. 其他号码：通用脱敏规则
        if (phone.length > 4) {
            const start = phone.substring(0, 2)
            const end = phone.substring(phone.length - 2)
            const middle = '*'.repeat(phone.length - 4)
            return start + middle + end
        }

        // 6. 短于4位的号码不脱敏
        return phone
    },

    // 处理列表数据，添加隐私处理后的手机号
    processPhoneList: function (list) {
        return list.map((item) => ({
            ...item,
            displayPhoneNumber: this.maskPhoneNumber(item.phoneNumber),
        }))
    },

    // 显示登录组件
    showLoginComponent() {
        userUtils.showLoginComponent(this, true)
    },

    // 隐藏登录组件
    hideLoginComponent() {
        userUtils.hideLoginComponent(this)
    },

    // 确保登录并执行回调的辅助方法
    ensureLogin(callback) {
        return userUtils.ensureLogin(this, callback)
    },

    // 登录成功回调
    onLoginSuccess() {
        console.log('常用电话页面：用户登录成功')
        // 登录成功后可以进行一些操作，比如刷新数据等
        // 这里暂时不需要特殊处理
    },

    // 分享
    onShareAppMessage: function () {
        return {
            title: '常用电话',
            path: '/pages/community-phone/index',
        }
    },

    onShareTimeline: function () {
        return {
            title: '常用电话',
        }
    },
})
