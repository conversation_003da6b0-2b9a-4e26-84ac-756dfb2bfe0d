package com.fs.swap.common.enums;

/**
 * 任务状态枚举
 */
public enum TaskStatus implements BaseEnum {
    /**
     * 进行中
     */
    IN_PROGRESS("1", "进行中"),

    /**
     * 已完成
     */
    COMPLETED("2", "已完成"),

    /**
     * 已领取
     */
    CLAIMED("3", "已领取"),

    /**
     * 已过期
     */
    EXPIRED("4", "已过期");

    private final String code;
    private final String info;

    TaskStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据编码获取枚举
     */
    public static TaskStatus getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskStatus status : TaskStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 