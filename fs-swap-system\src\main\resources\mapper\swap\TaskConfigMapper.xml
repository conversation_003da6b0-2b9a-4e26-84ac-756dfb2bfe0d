<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.TaskConfigMapper">
    
    <resultMap type="TaskConfig" id="TaskConfigResult">
        <result property="id"             column="id"               />
        <result property="taskCode"       column="task_code"        />
        <result property="taskName"       column="task_name"        />
        <result property="taskDesc"       column="task_desc"        />
        <result property="taskType"       column="task_type"        />
        <result property="triggerEvent"   column="trigger_event"    />
        <result property="targetCount"    column="target_count"     />
        <result property="rewardSilver"   column="reward_silver"    />
        <result property="page"           column="page"             />
        <result property="sortOrder"      column="sort_order"       />
        <result property="status"         column="status"           />
        <result property="createBy"       column="create_by"        />
        <result property="createTime"     column="create_time"      />
        <result property="updateBy"       column="update_by"        />
        <result property="updateTime"     column="update_time"      />
        <result property="deleted"        column="deleted"          />
    </resultMap>

    <sql id="selectTaskConfigVo">
        select id, task_code, task_name, task_desc, task_type, trigger_event, target_count, 
               reward_silver, page, sort_order, status, create_by, create_time, update_by, update_time, deleted
        from task_config
    </sql>

    <select id="selectTaskConfigList" parameterType="TaskConfig" resultMap="TaskConfigResult">
        <include refid="selectTaskConfigVo"/>
        <where>  
            deleted = 0
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
            <if test="triggerEvent != null  and triggerEvent != ''"> and trigger_event = #{triggerEvent}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectTaskConfigById" parameterType="Long" resultMap="TaskConfigResult">
        <include refid="selectTaskConfigVo"/>
        where id = #{id} and deleted = 0
    </select>

    <select id="selectTaskConfigByCode" parameterType="String" resultMap="TaskConfigResult">
        <include refid="selectTaskConfigVo"/>
        where task_code = #{taskCode} and deleted = 0
    </select>

    <select id="selectTaskConfigByTriggerEvent" parameterType="String" resultMap="TaskConfigResult">
        <include refid="selectTaskConfigVo"/>
        where trigger_event = #{triggerEvent} and status = '1' and deleted = 0
        order by sort_order asc
    </select>

    <select id="selectEnabledTaskConfigs" resultMap="TaskConfigResult">
        <include refid="selectTaskConfigVo"/>
        where status = '1' and deleted = 0
        order by sort_order asc
    </select>
        
    <insert id="insertTaskConfig" parameterType="TaskConfig" useGeneratedKeys="true" keyProperty="id">
        insert into task_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskDesc != null">task_desc,</if>
            <if test="taskType != null and taskType != ''">task_type,</if>
            <if test="triggerEvent != null and triggerEvent != ''">trigger_event,</if>
            <if test="targetCount != null">target_count,</if>
            <if test="rewardSilver != null">reward_silver,</if>
            <if test="page != null">page,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            deleted,
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskDesc != null">#{taskDesc},</if>
            <if test="taskType != null and taskType != ''">#{taskType},</if>
            <if test="triggerEvent != null and triggerEvent != ''">#{triggerEvent},</if>
            <if test="targetCount != null">#{targetCount},</if>
            <if test="rewardSilver != null">#{rewardSilver},</if>
            <if test="page != null">#{page},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            0,
            now()
         </trim>
    </insert>

    <update id="updateTaskConfig" parameterType="TaskConfig">
        update task_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="taskType != null and taskType != ''">task_type = #{taskType},</if>
            <if test="triggerEvent != null and triggerEvent != ''">trigger_event = #{triggerEvent},</if>
            <if test="targetCount != null">target_count = #{targetCount},</if>
            <if test="rewardSilver != null">reward_silver = #{rewardSilver},</if>
            <if test="page != null">page = #{page},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskConfigById" parameterType="Long">
        update task_config set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteTaskConfigByIds" parameterType="String">
        update task_config set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 