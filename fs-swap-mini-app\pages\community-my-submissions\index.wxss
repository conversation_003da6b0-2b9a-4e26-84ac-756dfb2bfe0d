/* pages/community-my-submissions/index.wxss */
page {
  background: #f5f6f8;
  min-height: 100vh;
}

.container {
  height: 100vh;
  background: #f5f6f8;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  background: #f5f6f8;
  -webkit-overflow-scrolling: touch;
}

/* 标签页样式 */
.van-tabs {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.van-tab {
  font-size: 15px;
  font-weight: 500;
  color: #666666;
}

.van-tab--active {
  color: #3B7FFF;
  font-weight: 600;
}

.van-tabs__line {
  background: #3B7FFF;
  height: 3px;
  border-radius: 2px;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  padding: 40px 20px;
}

.loading-text {
  font-size: 14px;
  color: #666666;
  margin-top: 12px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-top: 16px;
  font-weight: 500;
}

.empty-description {
  font-size: 13px;
  color: #999999;
  margin-top: 12px;
  text-align: center;
  line-height: 1.5;
}

.empty-description text {
  display: block;
  margin-bottom: 4px;
}

.empty-description text:last-child {
  margin-top: 8px;
  color: #3B7FFF;
}

/* 列表容器 */
.list-container {
  padding: 16px 12px;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 提交项卡片 */
.submission-item {
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.submission-item:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* 项头部 */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  flex: 1;
  line-height: 1.4;
}

/* 状态标签 */
.status-tag {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.9;
}

/* 项内容 */
.item-content {
  margin-bottom: 12px;
}

.item-address {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666666;
}

.phone-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f0f7ff;
  border-radius: 8px;
}

.phone-number {
  font-size: 15px;
  color: #3B7FFF;
  font-weight: 600;
}

.item-description {
  font-size: 13px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 图片展示 */
.item-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background-color: #f5f5f5;
}

/* 项底部 */
.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.submit-time {
  font-size: 12px;
  color: #999999;
}

/* 加载更多 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  gap: 8px;
}

.loading-more .loading-text {
  font-size: 13px;
  color: #999999;
  margin-top: 0;
}

.no-more {
  text-align: center;
  padding: 24px 0;
  font-size: 12px;
  color: #cccccc;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .list-container {
    padding: 12px 8px;
  }
  
  .submission-item {
    padding: 14px;
  }
  
  .item-title {
    font-size: 15px;
  }
} 