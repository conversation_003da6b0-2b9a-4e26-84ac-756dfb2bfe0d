<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <area-chain-selector
        :regionId="queryParams.regionId"
        :communityId="queryParams.communityId"
        :residentialId="queryParams.residentialId"
        @update:regionId="val => queryParams.regionId = val"
        @update:communityId="val => queryParams.communityId = val"
        @update:residentialId="val => queryParams.residentialId = val"
        :labels="{
          region: '行政区域',
          community: '社区',
          residential: '小区'
        }"
        :showCommunity="true"
        :showResidential="true"
        :inlineLayout="true"
        ref="queryAreaSelector"
        @community-change="handleQuery"
        @region-change="handleRegionChange"
      />
      <el-form-item label="地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择分类" clearable>
          <el-option
            v-for="dict in dict.type.community_service_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-select v-model="queryParams.tags" placeholder="请选择标签" clearable>
          <el-option
            v-for="dict in dict.type.community_service_tag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推荐状态" prop="isRecommended">
        <el-select v-model="queryParams.isRecommended" placeholder="请选择推荐状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_yes_no_num"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option
            v-for="dict in dict.type.common_audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.business_normal_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:nearby:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:nearby:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleAudit"
          v-hasPermi="['operation:nearby:audit']"
        >审核</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:nearby:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:nearby:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="nearbyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="id" width="80" />
      <el-table-column label="名称" align="center" prop="name" width="150" />
      <el-table-column label="地址" align="center" prop="address" width="200" show-overflow-tooltip />
      <el-table-column label="分类" align="center" prop="category" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.community_service_category" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tags" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.community_service_tag" :value="scope.row.tags"/>
        </template>
      </el-table-column>
      <el-table-column label="图片" align="center" prop="images" width="200">
        <template slot-scope="scope">
          <div class="image-list">
            <template v-if="scope.row.images">
              <image-preview
                v-for="(img, index) in scope.row.images.split(',')"
                :key="index"
                :src="filePrefix + img"
                :width="50"
                :height="50"
                style="margin: 2px;"
              />
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="contactPhone" width="130" />
      <el-table-column label="营业时间" align="center" prop="businessHours" width="150" show-overflow-tooltip />
      <el-table-column label="行政区域" align="center" prop="regionId" :formatter="regionFormatter" width="120" />
      <el-table-column label="社区" align="center" prop="communityId" :formatter="communityFormatter" width="120" />
      <el-table-column label="小区" align="center" prop="residentialId" :formatter="residentialFormatter" width="120" />
      <el-table-column label="推荐" align="center" prop="isRecommended" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no_num" :value="scope.row.isRecommended"/>
        </template>
      </el-table-column>
      <el-table-column label="官方发布" align="center" prop="isOfficial" width="90">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no_num" :value="scope.row.isOfficial"/>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.common_audit_status" :value="scope.row.auditStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.business_normal_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:nearby:edit']"
          >修改</el-button>

          <!-- 审核按钮 - 根据审核状态显示不同样式 -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['operation:nearby:audit']"
            v-if="scope.row.auditStatus == 0"
            class="audit-btn-pending"
          >审核</el-button>

          <!-- 已审核状态显示 -->
          <el-tooltip
            v-if="scope.row.auditStatus == 1"
            content="已审核通过"
            placement="top"
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-circle-check"
              disabled
              class="audit-btn-approved"
            >已通过</el-button>
          </el-tooltip>

          <el-tooltip
            v-if="scope.row.auditStatus == 2"
            :content="scope.row.auditRemark || '已审核拒绝'"
            placement="top"
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-circle-close"
              disabled
              class="audit-btn-rejected"
            >已拒绝</el-button>
          </el-tooltip>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:nearby:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改周边推荐对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入详细地址" />
        </el-form-item>
        <area-chain-selector
          :regionId="form.regionId"
          :communityId="form.communityId"
          :residentialId="form.residentialId"
          @update:regionId="val => form.regionId = val"
          @update:communityId="val => form.communityId = val"
          @update:residentialId="val => form.residentialId = val"
          :isLoading="isLoadingFormData"
          :showCommunity="true"
          :showResidential="true"
          ref="areaSelector"
        />
        <el-form-item label="位置坐标" prop="location">
          <el-input v-model="form.location" placeholder="格式：经度,纬度 (如：116.397128,39.916527)" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option
              v-for="dict in dict.type.community_service_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图片" prop="images">
          <image-upload v-model="form.images" :type="3"/>
        </el-form-item>
        <el-form-item label="服务描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入服务描述" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-select v-model="form.tags" placeholder="请选择标签">
            <el-option
              v-for="dict in dict.type.community_service_tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="营业时间" prop="businessHours">
          <el-input v-model="form.businessHours" placeholder="请输入营业时间" />
        </el-form-item>
        <el-form-item label="是否推荐" prop="isRecommended">
          <el-select v-model="form.isRecommended" placeholder="请选择是否推荐">
            <el-option
              v-for="dict in dict.type.sys_yes_no_num"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否官方发布" prop="isOfficial">
          <el-select v-model="form.isOfficial" placeholder="请选择是否官方发布">
            <el-option
              v-for="dict in dict.type.sys_yes_no_num"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.business_normal_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" placeholder="数字越大排序越靠前" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核周边推荐" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="dynamicAuditRules" label-width="100px">
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark" v-if="auditForm.auditStatus === 2">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNearby, getNearby, delNearby, addNearby, updateNearby, auditNearby } from '@/api/operation/nearby'
import AreaChainSelector from '@/components/AreaChainSelector'
import { regionFormatter, communityFormatter, residentialFormatter } from '@/utils/areaFormatter'

export default {
  name: 'Nearby',
  components: {
    AreaChainSelector
  },
  dicts: ['community_service_category', 'sys_yes_no_num', 'common_audit_status', 'community_service_tag', 'business_normal_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 文件前缀
      filePrefix: process.env.VUE_APP_FILE_URL,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 周边推荐表格数据
      nearbyList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否正在加载表单数据
      isLoadingFormData: false,
      // 审核对话框
      auditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        address: null,
        category: null,
        tags: null,
        isRecommended: null,
        auditStatus: null,
        status: null,
        regionId: null,
        communityId: null,
        residentialId: null
      },
      // 表单参数
      form: {},
      // 审核表单
      auditForm: {
        ids: [],
        auditStatus: 1,
        auditRemark: ''
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '分类不能为空', trigger: 'change' }
        ]
      },
      // 审核表单校验
      auditRules: {
        auditStatus: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 动态计算审核备注的校验规则
    dynamicAuditRules() {
      const rules = {
        auditStatus: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ]
      }

      // 只有选择拒绝时才需要填写备注
      if (this.auditForm.auditStatus === 2) {
        rules.auditRemark = [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' },
          { min: 5, message: '拒绝原因至少5个字符', trigger: 'blur' }
        ]
      }

      return rules
    }
  },
  async created() {
    await this.initData()
  },
  methods: {
    /** 初始化数据 */
    async initData() {
      try {
        this.getList()
      } catch (error) {
        this.$message.error('加载数据失败，请重试')
      }
    },

    /**
     * 通用方法：处理区域级联的数据初始化
     */
    async initAreaSelector(data) {
      try {
        await this.$nextTick()
        if (this.$refs.areaSelector) {
          if (data.regionId) {
            await this.$refs.areaSelector.initForEdit({
              regionId: data.regionId,
              communityId: data.communityId,
              residentialId: data.residentialId
            })
            await this.$nextTick()
          }
        }
        return true
      } catch (error) {
        return false
      }
    },

    /** 查询周边推荐列表 */
    getList() {
      this.loading = true
      listNearby(this.queryParams).then(response => {
        this.nearbyList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
      if (this.$refs.areaSelector) {
        this.$refs.areaSelector.clearAll()
      }
      this.isLoadingFormData = false
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        address: null,
        location: null,
        category: null,
        images: null,
        description: null,
        tags: null,
        contactPhone: null,
        businessHours: null,
        viewCount: null,
        callCount: null,
        navigateCount: null,
        isRecommended: null,
        isOfficial: null,
        regionId: null,
        communityId: null,
        residentialId: null,
        submitUserId: null,
        auditStatus: null,
        auditUserId: null,
        auditTime: null,
        auditRemark: null,
        sort: null,
        status: 1,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm('form')
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      if (this.$refs.queryAreaSelector) {
        this.$refs.queryAreaSelector.clearAll()
      }
      this.handleQuery()
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    async handleAdd() {
      this.reset()
      this.isLoadingFormData = true

      try {
        const loading = this.$loading({
          lock: true,
          text: '准备数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        this.open = true
        this.title = '添加周边推荐'

        await this.$nextTick()

        if (this.$refs.areaSelector) {
          this.$refs.areaSelector.clearAll()
        }

        loading.close()
      } catch (error) {
        this.$message.error('准备数据失败，请重试')
      } finally {
        setTimeout(() => {
          this.isLoadingFormData = false
        }, 300)
      }
    },

    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids

      try {
        this.isLoadingFormData = true

        const loading = this.$loading({
          lock: true,
          text: '加载数据中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        const response = await getNearby(id)
        this.form = response.data

        this.title = '修改周边推荐'
        this.open = true

        await this.$nextTick()

        await this.initAreaSelector({
          regionId: this.form.regionId,
          communityId: this.form.communityId,
          residentialId: this.form.residentialId
        })

        loading.close()
      } catch (error) {
        this.$message.error('加载数据失败，请重试')
        if (this.$loading) {
          this.$loading().close()
        }
      } finally {
        this.isLoadingFormData = false
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNearby(this.form).then(() => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addNearby(this.form).then(() => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },

    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm.ids = row ? [row.id] : this.ids
      this.auditForm.auditStatus = 1
      this.auditForm.auditRemark = ''
      this.auditOpen = true
    },

    /** 提交审核 */
    submitAudit() {
      this.$refs['auditForm'].validate(valid => {
        if (valid) {
          auditNearby(this.auditForm).then(() => {
            this.$modal.msgSuccess('审核成功')
            this.auditOpen = false
            this.getList()
          })
        }
      })
    },

    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false
      this.auditForm = {
        ids: [],
        auditStatus: 1,
        auditRemark: ''
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除周边推荐编号为"' + ids + '"的数据项？').then(function() {
        return delNearby(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/nearby/export', {
        ...this.queryParams
      }, `nearby_${new Date().getTime()}.xlsx`)
    },

    handleRegionChange() {
      this.handleQuery()
    },

    // 使用通用的区域格式化方法
    regionFormatter,
    communityFormatter,
    residentialFormatter
  }
}
</script>

<style scoped>
.image-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2px;
}

.el-table .cell {
  word-break: break-all;
}

.el-table__row:hover .image-list img {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 优化筛选表单布局 */
.el-form--inline .el-form-item {
  margin-bottom: 10px;
}

/* 优化审核状态显示 */
.dict-tag {
  margin: 0 2px;
}

/* 表格行高优化 */
.el-table .el-table__row td {
  padding: 8px 0;
}

/* 操作按钮优化 */
.el-button--text {
  padding: 5px 8px;
  margin: 0 2px;
}

/* 审核按钮样式优化 */
.audit-btn-pending {
  color: #67C23A !important;
  font-weight: 500;
}

.audit-btn-pending:hover {
  color: #529b2e !important;
  background-color: rgba(103, 194, 58, 0.1) !important;
}

.audit-btn-approved {
  color: #67C23A !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.audit-btn-approved:hover {
  color: #67C23A !important;
  background-color: transparent !important;
}

.audit-btn-rejected {
  color: #F56C6C !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.audit-btn-rejected:hover {
  color: #F56C6C !important;
  background-color: transparent !important;
}

/* 工具提示样式 */
.el-tooltip__popper {
  max-width: 200px;
}
</style>
