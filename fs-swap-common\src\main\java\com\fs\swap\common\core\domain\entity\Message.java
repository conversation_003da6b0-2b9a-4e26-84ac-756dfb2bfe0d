package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 消息对象 message
 * 
 * <AUTHOR>
 * @date 2024
 */
public class Message extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private String id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 消息类型：system-系统通知，chat-聊天消息，activity-活动通知，order-订单通知 */
    @Excel(name = "消息类型")
    private String type;

    /** 消息标题 */
    @Excel(name = "消息标题")
    private String title;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String content;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatar;

    /** 是否已读：0-未读，1-已读 */
    @Excel(name = "是否已读")
    private Integer isRead;

    /** 跳转类型：page-页面，tabBar-标签页，order-订单，activity-活动，none-无跳转 */
    @Excel(name = "跳转类型")
    private String jumpType;

    /** 跳转URL */
    @Excel(name = "跳转URL")
    private String jumpUrl;

    /** 关联ID */
    @Excel(name = "关联ID")
    private String relatedId;

    /** 操作按钮文本 */
    @Excel(name = "操作按钮文本")
    private String actionText;

    /** 发送者用户ID（用于聊天消息） */
    @Excel(name = "发送者用户ID")
    private Long fromUserId;

    /** 会话ID（用于聊天消息） */
    @Excel(name = "会话ID")
    private String conversationId;

    /** 消息状态：0-正常，1-已删除，2-已撤回 */
    @Excel(name = "消息状态")
    private Integer status;

    public String getId()
    {
        return id;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getContent()
    {
        return content;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public Integer getIsRead()
    {
        return isRead;
    }

    public void setIsRead(Integer isRead)
    {
        this.isRead = isRead;
    }

    public String getJumpType()
    {
        return jumpType;
    }

    public void setJumpType(String jumpType)
    {
        this.jumpType = jumpType;
    }

    public String getJumpUrl()
    {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl)
    {
        this.jumpUrl = jumpUrl;
    }

    public String getRelatedId()
    {
        return relatedId;
    }

    public void setRelatedId(String relatedId)
    {
        this.relatedId = relatedId;
    }

    public String getActionText()
    {
        return actionText;
    }

    public void setActionText(String actionText)
    {
        this.actionText = actionText;
    }

    public Long getFromUserId()
    {
        return fromUserId;
    }

    public void setFromUserId(Long fromUserId)
    {
        this.fromUserId = fromUserId;
    }

    public String getConversationId()
    {
        return conversationId;
    }

    public void setConversationId(String conversationId)
    {
        this.conversationId = conversationId;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("type", getType())
            .append("title", getTitle())
            .append("content", getContent())
            .append("avatar", getAvatar())
            .append("isRead", getIsRead())
            .append("jumpType", getJumpType())
            .append("jumpUrl", getJumpUrl())
            .append("relatedId", getRelatedId())
            .append("actionText", getActionText())
            .append("fromUserId", getFromUserId())
            .append("conversationId", getConversationId())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 