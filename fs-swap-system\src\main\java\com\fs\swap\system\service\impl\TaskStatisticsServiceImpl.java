package com.fs.swap.system.service.impl;

import com.fs.swap.common.core.domain.entity.TaskCompleteLog;
import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.enums.TaskStatus;
import com.fs.swap.system.service.ITaskStatisticsService;
import com.fs.swap.system.mapper.TaskCompleteLogMapper;
import com.fs.swap.system.mapper.TaskConfigMapper;
import com.fs.swap.system.mapper.UserTaskMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 任务统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class TaskStatisticsServiceImpl implements ITaskStatisticsService {
    
    @Autowired
    private TaskConfigMapper taskConfigMapper;
    
    @Autowired
    private UserTaskMapper userTaskMapper;
    
    @Autowired
    private TaskCompleteLogMapper taskCompleteLogMapper;
    
    @Autowired
    private RedisCache redisCache;
    
    private static final String TASK_STATS_CACHE_KEY = "task:stats:";
    private static final int CACHE_EXPIRE_MINUTES = 60;

    /**
     * 获取用户任务统计数据
     */
    @Override
    public Map<String, Object> getUserTaskStatistics(Long userId) {
        String cacheKey = TASK_STATS_CACHE_KEY + "user:" + userId;
        Map<String, Object> result = redisCache.getCacheObject(cacheKey);
        
        if (result != null) {
            return result;
        }
        
        result = new HashMap<>();
        
        // 查询用户所有任务
        UserTask queryTask = new UserTask();
        queryTask.setUserId(userId);
        List<UserTask> userTasks = userTaskMapper.selectUserTaskList(queryTask);
        
        // 统计各种状态的任务数量
        long totalTasks = userTasks.size();
        long completedTasks = userTasks.stream()
            .filter(task -> TaskStatus.COMPLETED.getCode().equals(task.getStatus()) || 
                           TaskStatus.CLAIMED.getCode().equals(task.getStatus()))
            .count();
        long inProgressTasks = userTasks.stream()
            .filter(task -> TaskStatus.IN_PROGRESS.getCode().equals(task.getStatus()))
            .count();
        long claimedTasks = userTasks.stream()
            .filter(task -> TaskStatus.CLAIMED.getCode().equals(task.getStatus()))
            .count();
        
        // 查询用户获得的总碳豆
        TaskCompleteLog queryLog = new TaskCompleteLog();
        queryLog.setUserId(userId);
        List<TaskCompleteLog> completeLogs = taskCompleteLogMapper.selectTaskCompleteLogList(queryLog);
        int totalRewards = completeLogs.stream()
            .mapToInt(log -> log.getRewardSilver() != null ? log.getRewardSilver() : 0)
            .sum();
        
        // 计算完成率
        double completionRate = totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0;
        
        result.put("totalTasks", totalTasks);
        result.put("completedTasks", completedTasks);
        result.put("inProgressTasks", inProgressTasks);
        result.put("claimedTasks", claimedTasks);
        result.put("totalRewards", totalRewards);
        result.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 任务类型分布
        Map<String, Long> taskTypeDistribution = userTasks.stream()
            .collect(Collectors.groupingBy(
                task -> getTaskTypeName(task.getTaskCode()),
                Collectors.counting()
            ));
        result.put("taskTypeDistribution", taskTypeDistribution);
        
        // 缓存结果
        redisCache.setCacheObject(cacheKey, result, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return result;
    }

    /**
     * 获取系统任务统计数据
     */
    @Override
    public Map<String, Object> getSystemTaskStatistics() {
        String cacheKey = TASK_STATS_CACHE_KEY + "system";
        Map<String, Object> result = redisCache.getCacheObject(cacheKey);
        
        if (result != null) {
            return result;
        }
        
        result = new HashMap<>();
        
        // 统计任务配置数量
        List<TaskConfig> allConfigs = taskConfigMapper.selectTaskConfigList(new TaskConfig());
        long totalConfigs = allConfigs.size();
        long enabledConfigs = allConfigs.stream()
            .filter(config -> "1".equals(config.getStatus()))
            .count();
        
        // 统计用户任务数量
        List<UserTask> allUserTasks = userTaskMapper.selectUserTaskList(new UserTask());
        long totalUserTasks = allUserTasks.size();
        long completedUserTasks = allUserTasks.stream()
            .filter(task -> TaskStatus.COMPLETED.getCode().equals(task.getStatus()) || 
                           TaskStatus.CLAIMED.getCode().equals(task.getStatus()))
            .count();
        
        // 统计任务完成记录
        List<TaskCompleteLog> allLogs = taskCompleteLogMapper.selectTaskCompleteLogList(new TaskCompleteLog());
        long totalCompletions = allLogs.size();
        int totalRewardsDistributed = allLogs.stream()
            .mapToInt(log -> log.getRewardSilver() != null ? log.getRewardSilver() : 0)
            .sum();
        
        // 计算系统完成率
        double systemCompletionRate = totalUserTasks > 0 ? 
            (double) completedUserTasks / totalUserTasks * 100 : 0;
        
        result.put("totalConfigs", totalConfigs);
        result.put("enabledConfigs", enabledConfigs);
        result.put("totalUserTasks", totalUserTasks);
        result.put("completedUserTasks", completedUserTasks);
        result.put("totalCompletions", totalCompletions);
        result.put("totalRewardsDistributed", totalRewardsDistributed);
        result.put("systemCompletionRate", Math.round(systemCompletionRate * 100.0) / 100.0);
        
        // 任务类型统计
        Map<String, Long> configTypeDistribution = allConfigs.stream()
            .collect(Collectors.groupingBy(TaskConfig::getTaskType, Collectors.counting()));
        result.put("configTypeDistribution", configTypeDistribution);
        
        // 热门任务Top 10
        Map<String, Long> taskPopularity = allUserTasks.stream()
            .collect(Collectors.groupingBy(UserTask::getTaskCode, Collectors.counting()));
        List<Map.Entry<String, Long>> topTasks = taskPopularity.entrySet().stream()
            .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
            .limit(10)
            .collect(Collectors.toList());
        result.put("topTasks", topTasks);
        
        // 缓存结果
        redisCache.setCacheObject(cacheKey, result, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return result;
    }

    /**
     * 获取任务排行榜
     */
    @Override
    public Map<String, Object> getTaskRanking(String taskCode, int limit) {
        String cacheKey = TASK_STATS_CACHE_KEY + "ranking:" + taskCode + ":" + limit;
        Map<String, Object> result = redisCache.getCacheObject(cacheKey);
        
        if (result != null) {
            return result;
        }
        
        result = new HashMap<>();
        
        // 查询指定任务的完成记录
        TaskCompleteLog queryLog = new TaskCompleteLog();
        queryLog.setTaskCode(taskCode);
        List<TaskCompleteLog> logs = taskCompleteLogMapper.selectTaskCompleteLogList(queryLog);
        
        // 按用户分组统计完成次数
        Map<Long, Long> userCompletionCount = logs.stream()
            .collect(Collectors.groupingBy(TaskCompleteLog::getUserId, Collectors.counting()));
        
        // 排序并取前N名
        List<Map<String, Object>> ranking = userCompletionCount.entrySet().stream()
            .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> {
                Map<String, Object> item = new HashMap<>();
                item.put("userId", entry.getKey());
                item.put("completionCount", entry.getValue());
                return item;
            })
            .collect(Collectors.toList());
        
        result.put("taskCode", taskCode);
        result.put("ranking", ranking);
        result.put("totalParticipants", userCompletionCount.size());
        
        // 缓存结果
        redisCache.setCacheObject(cacheKey, result, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return result;
    }
    
    /**
     * 根据任务编码获取任务类型名称
     */
    private String getTaskTypeName(String taskCode) {
        TaskConfig config = taskConfigMapper.selectTaskConfigByCode(taskCode);
        return config != null ? config.getTaskType() : "未知";
    }
} 