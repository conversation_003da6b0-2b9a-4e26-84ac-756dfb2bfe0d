<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.MessageSettingsMapper">

    <resultMap type="MessageSettings" id="MessageSettingsResult">
        <result property="userId"    column="user_id"    />
        <result property="systemNotification"    column="system_notification"    />
        <result property="chatMessage"    column="chat_message"    />
        <result property="activityNotification"    column="activity_notification"    />
        <result property="orderNotification"    column="order_notification"    />
        <result property="doNotDisturb"    column="do_not_disturb"    />
        <result property="doNotDisturbStartHour"    column="do_not_disturb_start_hour"    />
        <result property="doNotDisturbEndHour"    column="do_not_disturb_end_hour"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMessageSettingsVo">
        select user_id, system_notification, chat_message, activity_notification, order_notification, do_not_disturb, do_not_disturb_start_hour, do_not_disturb_end_hour, create_time, update_time from message_settings
    </sql>

    <select id="selectMessageSettingsByUserId" parameterType="Long" resultMap="MessageSettingsResult">
        <include refid="selectMessageSettingsVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertMessageSettings" parameterType="MessageSettings">
        insert into message_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="systemNotification != null">system_notification,</if>
            <if test="chatMessage != null">chat_message,</if>
            <if test="activityNotification != null">activity_notification,</if>
            <if test="orderNotification != null">order_notification,</if>
            <if test="doNotDisturb != null">do_not_disturb,</if>
            <if test="doNotDisturbStartHour != null">do_not_disturb_start_hour,</if>
            <if test="doNotDisturbEndHour != null">do_not_disturb_end_hour,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="systemNotification != null">#{systemNotification},</if>
            <if test="chatMessage != null">#{chatMessage},</if>
            <if test="activityNotification != null">#{activityNotification},</if>
            <if test="orderNotification != null">#{orderNotification},</if>
            <if test="doNotDisturb != null">#{doNotDisturb},</if>
            <if test="doNotDisturbStartHour != null">#{doNotDisturbStartHour},</if>
            <if test="doNotDisturbEndHour != null">#{doNotDisturbEndHour},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMessageSettings" parameterType="MessageSettings">
        update message_settings
        <set>
            <if test="systemNotification != null">system_notification = #{systemNotification},</if>
            <if test="chatMessage != null">chat_message = #{chatMessage},</if>
            <if test="activityNotification != null">activity_notification = #{activityNotification},</if>
            <if test="orderNotification != null">order_notification = #{orderNotification},</if>
            <if test="doNotDisturb != null">do_not_disturb = #{doNotDisturb},</if>
            <if test="doNotDisturbStartHour != null">do_not_disturb_start_hour = #{doNotDisturbStartHour},</if>
            <if test="doNotDisturbEndHour != null">do_not_disturb_end_hour = #{doNotDisturbEndHour},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <delete id="deleteMessageSettingsByUserId" parameterType="Long">
        delete from message_settings where user_id = #{userId}
    </delete>

</mapper> 