package com.fs.swap.wx.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发送用户消息DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
@Data
public class SendUserMessageDTO {
    
    /**
     * 接收用户ID
     */
    @NotNull(message = "接收用户ID不能为空")
    private Long toUserId;
    
    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;
    
    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")  
    private String conversationId;
} 