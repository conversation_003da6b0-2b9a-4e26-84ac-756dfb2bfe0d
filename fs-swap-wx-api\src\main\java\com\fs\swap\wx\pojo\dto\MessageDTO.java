package com.fs.swap.wx.pojo.dto;

import java.util.List;

/**
 * 消息DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
public class MessageDTO {
    
    /** 消息类型 */
    private String type;
    
    /** 页码 */
    private Integer pageNum = 1;
    
    /** 每页大小 */
    private Integer pageSize = 20;
    
    /** 消息ID列表（用于批量操作） */
    private List<String> messageIds;
    
    /** 搜索关键词 */
    private String keyword;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getMessageIds() {
        return messageIds;
    }

    public void setMessageIds(List<String> messageIds) {
        this.messageIds = messageIds;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
} 