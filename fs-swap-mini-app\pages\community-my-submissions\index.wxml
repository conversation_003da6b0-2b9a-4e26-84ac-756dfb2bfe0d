<!--pages/community-my-submissions/index.wxml-->
<view class="container">
  <!-- 标签页 -->
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky color="#3B7FFF">
    <van-tab wx:for="{{ tabs }}" wx:key="key" title="{{ item.title }}">
      <!-- 下拉刷新 -->
      <scroll-view 
        class="scroll-container"
        scroll-y="true"
        refresher-enabled="true"
        refresher-triggered="{{ refreshing }}"
        bindrefresherrefresh="onRefresh"
        bindscrolltolower="onLoadMore"
        enhanced="true"
        show-scrollbar="false"
      >
        <!-- 加载状态 -->
        <view wx:if="{{ loading && submissionList.length === 0 }}" class="loading-container">
          <van-loading type="spinner" size="24px" color="#3B7FFF" />
          <text class="loading-text">正在加载提交记录...</text>
        </view>

        <!-- 空状态 -->
        <view wx:elif="{{ !loading && submissionList.length === 0 }}" class="empty-container">
          <van-icon name="orders-o" size="48px" color="#cccccc" />
          <text class="empty-text">暂无提交记录</text>
          <view class="empty-description">
            <text wx:if="{{ tabs[activeTab].key === 'nearby' }}">还没有提交过周边服务信息</text>
            <text wx:else>还没有提交过常用电话信息</text>
            <text>快去添加一些有用的信息吧！</text>
          </view>
        </view>

        <!-- 列表内容 -->
        <view wx:else class="list-container">
          <view 
            wx:for="{{ submissionList }}" 
            wx:key="id"
            class="submission-item"
            data-item="{{ item }}"
            bindtap="onItemTap"
          >
            <!-- 头部 -->
            <view class="item-header">
              <text class="item-title">{{ item.name }}</text>
              <view class="status-tag" style="color: {{ item.statusInfo.color }}; border-color: {{ item.statusInfo.color }};">
                {{ item.statusInfo.text }}
              </view>
            </view>
            
            <!-- 内容 -->
            <view class="item-content">
              <!-- 地址信息（周边服务） -->
              <view wx:if="{{ tabs[activeTab].key === 'nearby' }}" class="item-address">
                <van-icon name="location-o" size="12px" color="#999" />
                <text>{{ item.address }}</text>
              </view>
              
              <!-- 电话信息（常用电话） -->
              <view wx:else class="phone-info">
                <van-icon name="phone-o" size="14px" color="#3B7FFF" />
                <text class="phone-number">{{ item.phoneNumber }}</text>
              </view>
              
              <!-- 描述 -->
              <view wx:if="{{ item.description }}" class="item-description">
                {{ item.description }}
              </view>
              
              <!-- 图片（仅周边服务） -->
              <view wx:if="{{ tabs[activeTab].key === 'nearby' && item.imagesArray.length > 0 }}" class="item-images">
                <image 
                  wx:for="{{ item.imagesArray }}" 
                  wx:for-item="imageUrl"
                  wx:key="*this"
                  src="{{ imageUrl }}" 
                  class="item-image"
                  mode="aspectFill"
                  lazy-load="true"
                />
              </view>
            </view>
            
            <!-- 底部 -->
            <view class="item-footer">
              <text class="submit-time">{{ item.createTimeFormatted }}</text>
              <van-icon name="arrow" size="12px" color="#ccc" />
            </view>
          </view>

          <!-- 加载更多 -->
          <view wx:if="{{ loadingMore }}" class="loading-more">
            <van-loading type="spinner" size="16px" color="#999" />
            <text class="loading-text">加载更多...</text>
          </view>

          <!-- 没有更多 -->
          <view wx:elif="{{ !hasMore && submissionList.length > 0 }}" class="no-more">
            没有更多了
          </view>
        </view>
      </scroll-view>
    </van-tab>
  </van-tabs>
</view> 