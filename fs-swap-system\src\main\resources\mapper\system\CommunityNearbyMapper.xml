<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.CommunityNearbyMapper">
    
    <resultMap type="CommunityNearby" id="CommunityNearbyResult">
        <id     property="id"             column="id"              />
        <result property="name"           column="name"            />
        <result property="address"        column="address"         />
        <result property="location"       column="location"        />
        <result property="category"       column="category"        />
        <result property="images"         column="images"          />
        <result property="description"    column="description"     />
        <result property="contactPhone"   column="contact_phone"   />
        <result property="businessHours"  column="business_hours"  />
        <result property="viewCount"      column="view_count"      />
        <result property="callCount"      column="call_count"      />
        <result property="navigateCount"  column="navigate_count"  />
        <result property="isRecommended"  column="is_recommended"  />
        <result property="isOfficial"     column="is_official"     />
        <result property="regionId"       column="region_id"       />
        <result property="tags"           column="tags"            />
        <result property="communityId"    column="community_id"    />
        <result property="residentialId"  column="residential_id"  />
        <result property="submitUserId"   column="submit_user_id"  />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditUserId"    column="audit_user_id"   />
        <result property="auditTime"      column="audit_time"      />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="sort"           column="sort"            />
        <result property="status"         column="status"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="deleted"        column="deleted"         />
        <result property="distance"       column="distance"        />
    </resultMap>

    <sql id="selectCommunityNearbyVo">
        select id, name, address, ST_AsText(location) as location, category, images, description, contact_phone, 
        business_hours, view_count, call_count, navigate_count, is_recommended, is_official, 
        region_id, tags, community_id, residential_id, submit_user_id, audit_status, audit_user_id, 
        audit_time, audit_remark, sort, status, create_by, create_time, update_by, update_time, deleted
        from community_nearby
    </sql>

    <select id="selectCommunityNearbyList" parameterType="CommunityNearby" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        <where>
            deleted = 0
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="address != null and address != ''">
                AND address like concat('%', #{address}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="communityId != null">
                AND community_id = #{communityId}
            </if>
            <if test="residentialId != null">
                AND residential_id = #{residentialId}
            </if>
            <if test="submitUserId != null">
                AND submit_user_id = #{submitUserId}
            </if>
            <if test="auditStatus != null">
                AND audit_status = #{auditStatus}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by view_count desc, sort asc, create_time desc
    </select>
    
    <select id="selectCommunityNearbyById" parameterType="Long" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectCommunityNearbyListByLocation" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        <where>
            deleted = 0
            AND status = 1
            AND audit_status = 1
            <if test="communityId != null and residentialId != null">
                AND (
                    (community_id = #{communityId} AND residential_id IS NULL)
                    OR
                    (residential_id = #{residentialId})
                )
            </if>
        </where>
        order by view_count desc, sort asc, create_time desc
    </select>
    
    <select id="selectCommunityNearbyListByDistance" resultMap="CommunityNearbyResult">
        SELECT 
            n.id, n.name, n.address, ST_AsText(n.location) as location, n.category, n.images, n.description, 
            n.contact_phone, n.business_hours, n.view_count, n.call_count, n.navigate_count, n.is_recommended, 
            n.is_official, n.region_id, n.tags, n.community_id, n.residential_id, n.submit_user_id, 
            n.audit_status, n.audit_user_id, n.audit_time, n.audit_remark, n.sort, n.status, 
            n.create_by, n.create_time, n.update_by, n.update_time, n.deleted,
            ST_Distance_Sphere(
                n.location,
                ST_GeomFromText(#{userLocation}, 0)
            ) as distance
        FROM 
            community_nearby n
        WHERE 
            n.deleted = 0
            AND n.status = 1
            AND n.audit_status = 1
            AND ST_Distance_Sphere(
                n.location,
                ST_GeomFromText(#{userLocation}, 0)
            ) &lt;= #{maxDistance}
        ORDER BY
            view_count desc,
            distance ASC,
            n.sort ASC,
            n.create_time DESC
    </select>
    
    <select id="selectCommunityNearbyListBySubmitUserId" parameterType="Long" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        where submit_user_id = #{submitUserId} and deleted = 0
        order by create_time desc
    </select>
        
    <insert id="insertCommunityNearby" parameterType="CommunityNearby" useGeneratedKeys="true" keyProperty="id">
        insert into community_nearby
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="address != null">address,</if>
            <if test="location != null">location,</if>
            <if test="category != null">category,</if>
            <if test="images != null">images,</if>
            <if test="description != null">description,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="businessHours != null">business_hours,</if>
            <if test="regionId != null">region_id,</if>
            <if test="tags != null">tags,</if>
            <if test="communityId != null">community_id,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="submitUserId != null">submit_user_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="address != null">#{address},</if>
            <if test="location != null">ST_GeomFromText(#{location}, 0),</if>
            <if test="category != null">#{category},</if>
            <if test="images != null">#{images},</if>
            <if test="description != null">#{description},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="businessHours != null">#{businessHours},</if>
            <if test="regionId != null">#{regionId},</if>
            <if test="tags != null">#{tags},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="submitUserId != null">#{submitUserId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateCommunityNearby" parameterType="CommunityNearby">
        update community_nearby
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="location != null">location = ST_GeomFromText(#{location}, 0),</if>
            <if test="category != null">category = #{category},</if>
            <if test="images != null">images = #{images},</if>
            <if test="description != null">description = #{description},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="businessHours != null">business_hours = #{businessHours},</if>
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="submitUserId != null">submit_user_id = #{submitUserId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteCommunityNearbyById" parameterType="Long">
        update community_nearby set deleted = 1 where id = #{id}
    </update>

    <update id="deleteCommunityNearbyByIds" parameterType="String">
        update community_nearby set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新浏览次数 -->
    <update id="incrementViewCount" parameterType="Long">
        UPDATE community_nearby 
        SET view_count = view_count + 1 
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新拨打电话次数 -->
    <update id="incrementCallCount" parameterType="Long">
        UPDATE community_nearby 
        SET call_count = call_count + 1 
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新导航次数 -->
    <update id="incrementNavigateCount" parameterType="Long">
        UPDATE community_nearby 
        SET navigate_count = navigate_count + 1 
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 审核服务 -->
    <update id="auditCommunityNearby">
        UPDATE community_nearby 
        SET audit_status = #{auditStatus},
            audit_user_id = #{auditUserId},
            audit_time = #{auditTime},
            audit_remark = #{auditRemark}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据标签搜索 -->
    <select id="selectCommunityNearbyListByTags" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        <where>
            deleted = 0 AND status = 1 AND audit_status = 1
            <if test="tags != null and tags.size() > 0">
                AND (
                <foreach collection="tags" item="tag" separator=" OR ">
                    FIND_IN_SET(#{tag}, tags) > 0
                </foreach>
                )
            </if>
        </where>
        order by view_count desc, sort asc, create_time desc
    </select>

    <!-- 获取推荐服务列表 -->
    <select id="selectRecommendedCommunityNearby" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        <where>
            deleted = 0 
            AND status = 1 
            AND audit_status = 1 
            AND is_recommended = 1
            <if test="regionId != null">
                AND region_id = #{regionId}
            </if>
            <if test="communityId != null">
                AND community_id = #{communityId}
            </if>
            <if test="residentialId != null">
                AND residential_id = #{residentialId}
            </if>
        </where>
        order by view_count desc, sort asc, view_count desc, create_time desc
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取热门服务列表（根据浏览量） -->
    <select id="selectPopularCommunityNearby" resultMap="CommunityNearbyResult">
        <include refid="selectCommunityNearbyVo"/>
        <where>
            deleted = 0 
            AND status = 1 
            AND audit_status = 1
            <if test="regionId != null">
                AND region_id = #{regionId}
            </if>
            <if test="communityId != null">
                AND community_id = #{communityId}
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        order by view_count desc, call_count desc, navigate_count desc
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>
</mapper>
