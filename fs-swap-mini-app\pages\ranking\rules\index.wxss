.page-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.rules-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
}

.rules-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c5aa0;
  margin-bottom: 24rpx;
  text-align: center;
}

.rules-item {
  margin-bottom: 20rpx;
  padding-left: 24rpx;
  position: relative;
}

.rules-item::before {
  content: "•";
  color: #2c5aa0;
  position: absolute;
  left: 0;
  top: 0;
}
