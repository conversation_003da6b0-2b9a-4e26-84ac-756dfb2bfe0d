package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户联系方式对象 user_contact
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class UserContact extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 联系方式类型(1-手机号,2-微信号,3-微信二维码) */
    @Excel(name = "联系方式类型")
    private String contactType;

    /** 联系方式值 */
    @Excel(name = "联系方式值")
    private String contactValue;

    /** 是否可见(0-不可见 1-可见) */
    @Excel(name = "是否可见")
    private Boolean isVisible;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
