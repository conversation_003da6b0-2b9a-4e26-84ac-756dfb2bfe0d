package com.fs.swap.system.service.impl;

import java.util.Date;
import com.fs.swap.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fs.swap.system.mapper.MessageSettingsMapper;
import com.fs.swap.common.core.domain.entity.MessageSettings;
import com.fs.swap.system.service.IMessageSettingsService;

/**
 * 消息设置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024
 */
@Service
public class MessageSettingsServiceImpl implements IMessageSettingsService 
{
    @Autowired
    private MessageSettingsMapper messageSettingsMapper;

    /**
     * 查询用户消息设置
     * 
     * @param userId 用户ID
     * @return 消息设置
     */
    @Override
    public MessageSettings selectMessageSettingsByUserId(Long userId)
    {
        MessageSettings settings = messageSettingsMapper.selectMessageSettingsByUserId(userId);
        if (settings == null) {
            // 如果用户没有设置，初始化默认设置
            initUserMessageSettings(userId);
            settings = messageSettingsMapper.selectMessageSettingsByUserId(userId);
        }
        return settings;
    }

    /**
     * 新增消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    @Override
    public int insertMessageSettings(MessageSettings messageSettings)
    {
        messageSettings.setCreateTime(DateUtils.getNowDate());
        return messageSettingsMapper.insertMessageSettings(messageSettings);
    }

    /**
     * 修改消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    @Override
    public int updateMessageSettings(MessageSettings messageSettings)
    {
        messageSettings.setUpdateTime(DateUtils.getNowDate());
        return messageSettingsMapper.updateMessageSettings(messageSettings);
    }

    /**
     * 初始化用户消息设置（使用默认设置）
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int initUserMessageSettings(Long userId)
    {
        MessageSettings settings = new MessageSettings();
        settings.setUserId(userId);
        settings.setSystemNotification(1); // 默认开启系统通知
        settings.setChatMessage(1); // 默认开启聊天消息
        settings.setActivityNotification(1); // 默认开启活动通知
        settings.setOrderNotification(1); // 默认开启订单通知
        settings.setDoNotDisturb(0); // 默认关闭免打扰
        settings.setDoNotDisturbStartHour(22); // 默认免打扰开始时间22点
        settings.setDoNotDisturbEndHour(8); // 默认免打扰结束时间8点
        
        return insertMessageSettings(settings);
    }

    /**
     * 设置消息免打扰
     * 
     * @param userId 用户ID
     * @param type 消息类型或'all'表示全部
     * @param mute 是否免打扰
     * @return 结果
     */
    @Override
    public int setMessageMute(Long userId, String type, boolean mute)
    {
        MessageSettings settings = selectMessageSettingsByUserId(userId);
        if (settings == null) {
            return 0;
        }
        
        int muteValue = mute ? 0 : 1; // 0表示关闭（免打扰），1表示开启
        
        switch (type) {
            case "system":
                settings.setSystemNotification(muteValue);
                break;
            case "chat":
                settings.setChatMessage(muteValue);
                break;
            case "activity":
                settings.setActivityNotification(muteValue);
                break;
            case "order":
                settings.setOrderNotification(muteValue);
                break;
            case "all":
                settings.setSystemNotification(muteValue);
                settings.setChatMessage(muteValue);
                settings.setActivityNotification(muteValue);
                settings.setOrderNotification(muteValue);
                break;
            default:
                return 0;
        }
        
        return updateMessageSettings(settings);
    }
} 