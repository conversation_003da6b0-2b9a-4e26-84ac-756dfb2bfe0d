/* pages/community-nearby-publish/index.wxss */
page {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

.container {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 20rpx 24rpx;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.form-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 24rpx 0;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  padding: 0 28rpx 20rpx;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.field-container {
  padding: 12rpx 28rpx;
  position: relative;
}

.field-label {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required::after {
  content: '*';
  color: #ee0a24;
  margin-left: 4px;
}

.upload-container {
  margin-top: 12rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #969799;
  margin-top: 12rpx;
}

/* 修改上传组件样式以一行显示三张图片 */
.van-uploader {
  width: 100%;
}

.van-uploader__wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.van-uploader__upload,
.van-uploader__preview {
  width: calc(33.33% - 16rpx) !important;
  margin: 8rpx !important;
  box-sizing: border-box;
}

.van-uploader__upload,
.van-uploader__preview-image {
  height: 200rpx !important;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 确保图片不被拉伸 */
.van-uploader__preview-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  background-color: #f5f5f5;
}

/* 设置预览容器固定高度 */
.van-uploader__preview {
  height: 200rpx !important;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.textarea-container {
  position: relative;
  margin-bottom: 12rpx;
}

textarea {
  width: 100%;
  height: 240rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: #f7f8fa;
}

.word-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #969799;
}

.submit-section {
  padding: 40rpx 32rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #3B7FFF;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(59, 127, 255, 0.25);
  letter-spacing: 1rpx;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
  box-shadow: none;
}

.submit-btn.disabled {
  background-color: #cccccc !important;
  color: #ffffff !important;
  box-shadow: none !important;
  opacity: 0.6 !important;
  pointer-events: none !important;
}

/* 修改van-field样式 */
.van-field__label {
  width: 180rpx !important;
  font-size: 28rpx !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.van-field__value {
  font-size: 28rpx !important;
}

.van-field__placeholder {
  font-size: 28rpx !important;
}

.van-field__body {
  font-size: 28rpx !important;
}

.van-field__input {
  font-size: 28rpx !important;
  color: #333 !important;
}

.field-input {
  font-size: 28rpx !important;
  color: #333 !important;
}

/* 错误消息样式 */
.error-message {
  font-size: 24rpx;
  color: #ee0a24;
  margin-top: 8rpx;
  margin-left: 0;
  line-height: 1.4;
}

/* 标签选择器样式 */
.tag-picker-container {
  background: #ffffff;
  padding: 32rpx 24rpx;
  max-height: 60vh;
  overflow: hidden;
}

.tag-picker-header {
  text-align: center;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
}

.tag-picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.tag-picker-subtitle {
  font-size: 24rpx;
  color: #999999;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  max-height: 40vh;
  overflow-y: auto;
  padding: 16rpx 0;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #ffffff;
  transition: all 0.2s ease;
  min-width: 0;
  flex-shrink: 0;
}

.tag-item.selected {
  border-color: #3B7FFF;
  background-color: #f0f7ff;
  color: #3B7FFF;
}

.tag-item:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.tag-picker-footer {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.tag-confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #3B7FFF;
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 40rpx;
  border: none;
}

.tag-confirm-btn:active {
  background-color: #2968e8;
}
