package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 任务配置对象 task_config
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TaskConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务配置ID */
    private Long id;

    /** 任务编码 */
    @Excel(name = "任务编码")
    private String taskCode;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String taskDesc;

    /** 任务类型 */
    @Excel(name = "任务类型", readConverterExp = "DAILY=每日任务,ONCE=一次性任务,WEEKLY=每周任务,MONTHLY=每月任务")
    private String taskType;

    /** 触发事件 */
    @Excel(name = "触发事件")
    private String triggerEvent;

    /** 目标次数 */
    @Excel(name = "目标次数")
    private Integer targetCount;

    /** 奖励碳豆数 */
    @Excel(name = "奖励碳豆数")
    private Integer rewardSilver;

    /** 界面路径 */
    @Excel(name = "界面路径")
    private String page;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private String status;

    /** 创建者 */
    @Excel(name = "创建者")
    private String createBy;

    /** 更新者 */
    @Excel(name = "更新者")
    private String updateBy;

    /** 删除标志 */
    private Integer deleted;
} 