<view class="page-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="bg-circle circle1"></view>
    <view class="bg-circle circle2"></view>
    <view class="bg-circle circle3"></view>
  </view>

  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">本小区排行榜</text>
      <text class="page-subtitle">本期榜单时间：8月1日00:00:00 至 8月31日23:59:59</text>
    </view>
    <view class="rules-button" bindtap="onRulesTap">
      <van-icon name="question-o" size="12px" color="#3B7FFF" />
      <text class="rules-text">规则</text>
    </view>
  </view>

  <!-- 榜单类型切换选项卡 -->
  <view class="ranking-tabs">
    <view class="tab-item {{currentTab === 'current' ? 'active' : ''}}" 
          bindtap="onTabChange" 
          data-tab="current">
      <text class="tab-text">🏆 当前排名</text>
    </view>
    <view class="tab-item {{currentTab === 'previous' ? 'active' : ''}}" 
          bindtap="onTabChange" 
          data-tab="previous">
      <text class="tab-text">🏅 上期榜单</text>
    </view>
  </view>

  <!-- 上期榜单提示信息 -->
  <view class="previous-tip" wx:if="{{currentTab === 'previous'}}">
    <van-icon name="info-o" size="14px" color="#FF9500" />
    <text class="tip-text">显示上期获奖用户名单及奖励情况</text>
  </view>

  <!-- 完整排行榜列表 -->
  <view class="ranking-list">
    <view class="list-header">
      <text class="list-title">完整榜单</text>
      <text class="list-count">共{{rankingList.length}}人上榜</text>
    </view>

    <!-- 表头 -->
    <view class="table-header">
      <view class="header-rank">排名</view>
      <view class="header-user">用户</view>
      <view class="header-score">{{currentTab === 'current' ? '获得碳豆' : '获得碳豆'}}</view>
      <view class="header-reward">奖励</view>
    </view>

    <!-- 排行榜条目 -->
    <view class="ranking-item" 
          wx:for="{{rankingList}}" 
          wx:key="id">
      <view class="item-rank">
        <text class="rank-number">{{item.rank}}</text>
      </view>
      
      <view class="item-user">
        <image src="{{item.avatar || '/static/img/default_avatar.png'}}" 
               class="user-avatar"
               bindtap="onUserAvatarTap"
               data-userid="{{item.id}}"
               catchtap="onUserAvatarTap"></image>
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
        </view>
      </view>
      
      <view class="item-score">
        <text class="score-number">{{item.totalSilver}}</text>
        <text class="score-unit">碳豆</text>
      </view>

      <view class="item-reward">
        <text class="reward-text">{{item.reward || '--'}}</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{loading}}">
      <van-loading size="16px" />
      <text class="load-text">加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!loading && !hasMore && rankingList.length > 0}}">
      <text>— 已经到底了 —</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && rankingList.length === 0}}">
      <van-icon name="info-o" size="48px" color="#ccc" />
      <text class="empty-text">暂无排行榜数据</text>
    </view>
  </view>

  <!-- 我的排名卡片 -->
  <view class="my-ranking-card" wx:if="{{myRanking && currentTab === 'current'}}">
    <view class="card-header">
      <van-icon name="user-o" size="16px" color="#3B7FFF" />
      <text class="card-title">我的排名</text>
    </view>
    
    <view class="my-ranking-content">
      <view class="my-rank-info">
        <view class="my-rank-number">
          <text class="rank-text">第</text>
          <text class="rank-num">{{myRanking.rank || '--'}}</text>
          <text class="rank-text">名</text>
        </view>
        <view class="my-rank-detail">
          <text class="detail-text">本月累计获得</text>
          <text class="detail-score">{{myRanking.totalSilver || 0}}碳豆</text>
        </view>
      </view>
      
      <view class="my-user-info">
        <image src="{{myRanking.avatar || '/static/img/default_avatar.png'}}" class="my-avatar"></image>
        <text class="my-name">{{myRanking.name}}</text>
      </view>
    </view>
    
    <!-- 排名变化提示 -->
    <view class="rank-tip" wx:if="{{myRanking.rankChange}}">
      <text class="tip-text {{myRanking.rankChange > 0 ? 'up' : 'down'}}">
        {{myRanking.rankChange > 0 ? '↗' : '↘'}} 
        较昨日{{myRanking.rankChange > 0 ? '上升' : '下降'}}{{Math.abs(myRanking.rankChange)}}名
      </text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view> 