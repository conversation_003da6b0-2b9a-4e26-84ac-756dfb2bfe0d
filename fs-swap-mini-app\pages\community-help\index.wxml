<view class="container">
  <!-- 固定筛选区域 -->
  <view class="filter-header {{ filterFixed ? 'fixed' : '' }}">
    <!-- 搜索框区域 -->
    <view class="search-section">
      <van-search
        value="{{ searchValue }}"
        placeholder="搜索互助信息..."
        bind:change="onSearchChange"
        shape="round"
        background="transparent"
        clearable
        left-icon="search"
        custom-class="search-input"
        input-class="search-input-field"
      />
    </view>

    <!-- 筛选和排序区域 -->
    <view class="filter-sort-section">
      <!-- 发布类型筛选 -->
      <view class="filter-item {{ publishTypeFilter !== 'all' ? 'active' : '' }}" bindtap="onPublishTypeSelect">
        <text class="filter-text">{{ selectedPublishTypeName || '全部' }}</text>
        <van-icon name="arrow-down" size="8px" />
      </view>

      <!-- 分类筛选 -->
      <view class="filter-item {{ helpFilter !== 'all' ? 'active' : '' }}" bindtap="onCategorySelect">
        <text class="filter-text">{{ selectedCategoryName || '分类' }}</text>
        <van-icon name="arrow-down" size="8px" />
      </view>

      <!-- 发布时间排序 -->
      <view class="sort-item {{ sortType === 'createTime' ? 'active' : '' }}" bindtap="onSortChange" data-sort="createTime">
        <text class="sort-text">发布</text>
        <van-icon name="{{ sortType === 'createTime' ? (sortOrder === 'desc' ? 'arrow-down' : 'arrow-up') : 'exchange' }}" size="8px" />
      </view>

      <!-- 截止时间排序 -->
      <view class="sort-item {{ sortType === 'endTime' ? 'active' : '' }}" bindtap="onSortChange" data-sort="endTime">
        <text class="sort-text">截止</text>
        <van-icon name="{{ sortType === 'endTime' ? (sortOrder === 'desc' ? 'arrow-down' : 'arrow-up') : 'exchange' }}" size="8px" />
      </view>
    </view>
  </view>

  <!-- 列表数据区域 -->
  <view class="list-container {{ filterFixed ? 'with-fixed-filter' : '' }}">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading size="24px" color="#3B7FFF" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主体内容区域 -->
    <view wx:else class="content-area">
      <!-- 空状态 -->
      <view wx:if="{{filteredList.length === 0}}" class="empty-container">
        <van-icon name="info-o" size="48px" color="#cccccc" />
        <text class="empty-text">暂无相关互助信息</text>
        <text class="empty-hint">试试搜索其他关键词或切换分类</text>
      </view>

      <!-- 互助列表 -->
      <view wx:else class="help-list">
        <view
          class="help-item"
          wx:for="{{ filteredList }}"
          wx:key="id"
          bindtap="onHelpDetail"
          data-id="{{ item.id }}"
        >
          <!-- 主要内容区域 -->
          <view class="main-content">
            <!-- 左侧内容 -->
            <view class="content-left">
              <!-- 标题前的标签区域 -->
              <view class="title-tags-section">
                <view class="publish-type-tag {{ item.publishType === '1' ? 'request-tag' : 'offer-tag' }}">
                  <text class="tag-text">{{ item.publishType === '1' ? '发布需求' : '提供服务' }}</text>
                </view>
                <text class="tag-separator">|</text>
                <view class="category-tag">
                  <text class="category-text">{{ item.categoryName }}</text>
                </view>
              </view>

              <!-- 标题 -->
              <view wx:if="{{ item.title }}" class="help-title">{{ item.title }}</view>

              <!-- 内容描述 -->
              <view class="help-content">{{ item.content }}</view>
            </view>

            <!-- 右侧图片 -->
            <view wx:if="{{ item.firstImage }}" class="content-right">
              <view class="image-container" catchtap="onPreviewImage" data-current="{{ item.imageList }}" data-index="0">
                <van-image
                  width="100%"
                  height="100%"
                  src="{{ item.firstImage }}"
                  fit="cover"
                  radius="8"
                  lazy-load
                />
              </view>
            </view>
          </view>

          <!-- 底部信息栏 -->
          <view class="bottom-section">
            <!-- 用户信息在左侧 -->
            <view class="user-section">
              <image
                class="user-avatar"
                src="{{ item.avatar }}"
                mode="aspectFill"
                catchtap="onUserAvatarTap"
                data-user-id="{{ item.userId }}"
              />
              <view class="user-details">
                <text class="user-name">{{ item.nickname }}</text>
                <text class="publish-time">{{ item.createTime }}</text>
              </view>
            </view>

            <view class="stats">
              <view class="stat-item">
                <van-icon name="eye-o" size="14px" color="#8a8e99" />
                <text class="stat-text">{{ item.viewCount || 0 }}</text>
              </view>
              <!-- 截止日期 -->
              <view wx:if="{{ item.endTime }}" class="end-time">
                <van-icon name="clock-o" size="11px" color="#8a8e99" />
                <text class="end-time-text">截止：{{ item.endTime }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分类选择弹窗 -->
  <van-popup
    show="{{ showCategoryPicker }}"
    position="bottom"
    bind:close="onCategoryPickerClose"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-picker
      columns="{{ categoryColumns }}"
      value-key="name"
      bind:confirm="onCategoryConfirm"
      bind:cancel="onCategoryPickerClose"
      confirm-button-text="确认"
      cancel-button-text="取消"
      show-toolbar="{{ true }}"
      title="选择分类"
    />
  </van-popup>

  <!-- 发布类型选择弹窗 -->
  <van-popup
    show="{{ showPublishTypePicker }}"
    position="bottom"
    bind:close="onPublishTypePickerClose"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <van-picker
      columns="{{ publishTypeColumns }}"
      value-key="name"
      bind:confirm="onPublishTypeConfirm"
      bind:cancel="onPublishTypePickerClose"
      confirm-button-text="确认"
      cancel-button-text="取消"
      show-toolbar="{{ true }}"
      title="选择类型"
    />
  </van-popup>

  <!-- 悬浮操作按钮 -->
  <view class="floating-actions">
    <view class="fab-btn add-btn" bindtap="onShowPublishDialog" hover-class="fab-btn-hover">
      <van-icon name="plus" size="22px" color="#ffffff" />
    </view>
  </view>

  <!-- 发布选择弹框 -->
  <van-popup
    show="{{ showPublishDialog }}"
    position="bottom"
    bind:close="onClosePublishDialog"
    round
    custom-style="padding: 0 0 env(safe-area-inset-bottom) 0; border-radius: 24rpx 24rpx 0 0;"
  >
    <view class="publish-dialog">
      <!-- 第一步：选择发布类型 -->
      <view wx:if="{{ publishStep === 1 }}" class="publish-step-1">
        <view class="publish-dialog-header">
          <text class="publish-dialog-title">选择发布类型</text>
          <van-icon name="cross" size="18px" color="#999999" bindtap="onClosePublishDialog" />
        </view>
        <view class="publish-options">
          <view class="publish-option publish-option-request" bindtap="onPublishRequest">
            <view class="option-icon">🙋‍♀️</view>
            <view class="option-content">
              <text class="option-title">发布需求</text>
              <text class="option-subtitle">寻求帮助</text>
            </view>
            <van-icon name="arrow" size="16px" color="#ffffff" />
          </view>
          <view class="publish-option publish-option-offer" bindtap="onPublishOffer">
            <view class="option-icon">🤝</view>
            <view class="option-content">
              <text class="option-title">提供服务</text>
              <text class="option-subtitle">帮助他人</text>
            </view>
            <van-icon name="arrow" size="16px" color="#ffffff" />
          </view>
        </view>
      </view>

      <!-- 第二步：选择分类 -->
      <view wx:else class="publish-step-2">
        <view class="publish-dialog-header">
          <van-icon name="arrow-left" size="18px" color="#999999" bindtap="onBackToPublishType" />
          <text class="publish-dialog-title">选择分类</text>
          <van-icon name="cross" size="18px" color="#999999" bindtap="onClosePublishDialog" />
        </view>
        <view class="category-list">
          <!-- 发布需求时显示需求分类 -->
          <view wx:if="{{ selectedPublishType === '1' }}">
            <view
              class="category-item"
              wx:for="{{ requireCategories }}"
              wx:key="dictValue"
              bindtap="onSelectCategory"
              data-category-id="{{ item.dictValue }}"
              data-category-name="{{ item.dictLabel }}"
            >
              <text class="category-name">{{ item.dictLabel }}</text>
              <van-icon name="arrow" size="16px" color="#cccccc" />
            </view>
          </view>
          <!-- 提供服务时显示服务分类 -->
          <view wx:elif="{{ selectedPublishType === '2' }}">
            <view
              class="category-item"
              wx:for="{{ serviceCategories }}"
              wx:key="dictValue"
              bindtap="onSelectCategory"
              data-category-id="{{ item.dictValue }}"
              data-category-name="{{ item.dictLabel }}"
            >
              <text class="category-name">{{ item.dictLabel }}</text>
              <van-icon name="arrow" size="16px" color="#cccccc" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 登录检查组件 -->
  <login-action id="loginAction" />

  <!-- 小区认证组件 -->
  <residential-auth id="residentialAuth" />
</view>
