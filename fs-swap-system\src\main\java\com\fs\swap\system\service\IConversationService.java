package com.fs.swap.system.service;

import java.util.List;
import java.util.Map;
import com.fs.swap.common.core.domain.entity.Conversation;
import com.fs.swap.common.core.domain.dto.ConversationListDTO;

/**
 * 会话Service接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface IConversationService 
{
    /**
     * 查询会话
     * 
     * @param id 会话主键
     * @return 会话
     */
    public Conversation selectConversationById(String id);

    /**
     * 查询会话列表
     * 
     * @param conversation 会话
     * @return 会话集合
     */
    public List<Conversation> selectConversationList(Conversation conversation);

    /**
     * 获取用户会话列表
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    public List<ConversationListDTO> getUserConversations(Long userId);

    /**
     * 新增会话
     * 
     * @param conversation 会话
     * @return 结果
     */
    public int insertConversation(Conversation conversation);

    /**
     * 修改会话
     * 
     * @param conversation 会话
     * @return 结果
     */
    public int updateConversation(Conversation conversation);

    /**
     * 更新会话最后消息信息
     * 
     * @param conversationId 会话ID
     * @param lastMessageId 最后消息ID
     * @param lastMessageContent 最后消息内容
     * @param lastMessageTime 最后消息时间
     * @return 结果
     */
    public int updateLastMessage(String conversationId, String lastMessageId, 
                               String lastMessageContent, java.util.Date lastMessageTime);

    /**
     * 批量删除会话
     * 
     * @param ids 需要删除的会话主键
     * @return 结果
     */
    public int deleteConversationByIds(String[] ids);

    /**
     * 删除会话信息
     * 
     * @param id 会话主键
     * @return 结果
     */
    public int deleteConversationById(String id);

    /**
     * 检查会话是否存在
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    public boolean checkConversationExists(String conversationId);

    /**
     * 创建或获取单聊会话
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    public String createOrGetSingleConversation(Long userId1, Long userId2);

    /**
     * 根据参与者获取单聊会话ID
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    public String getSingleConversationId(Long userId1, Long userId2);

    /**
     * 生成会话ID
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    public String generateConversationId(Long userId1, Long userId2);
} 