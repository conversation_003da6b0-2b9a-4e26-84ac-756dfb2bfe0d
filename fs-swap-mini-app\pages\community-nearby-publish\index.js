// pages/community-nearby-publish/index.js
const util = require("../../utils/util")
const api = require("../../config/api")
const userUtils = require("../../utils/user")
const systemInfoService = require("../../services/systemInfo")
const app = getApp()

Page({
  data: {
    // 常量配置
    MAX_DESCRIPTION_LENGTH: 500,
    MAX_UPLOAD_COUNT: 9,

    // 表单数据
    formData: {
      name: '',
      description: '',
      images: '',
      address: '',
      location: '',
      category: '',
      categoryName: '',
      contactPhone: '',
      businessHours: '',
      tags: ''
    },

    // 图片上传
    fileList: [],

    // 选择器相关
    showCategoryPicker: false,
    showTagPicker: false,

    // 分类选项（从后端字典接口获取）
    categories: [],
    categoryData: [], // 存储完整的分类数据

    // 标签相关
    availableTags: [],
    selectedTags: [],
    selectedTagNames: '', // 显示选中标签的名称

    // 页面状态
    submitting: false,
    formValid: false,
    
    // 错误提示
    nameError: '',
    descriptionError: '',
    addressError: '',
    imageError: '',
    phoneError: ''
  },

  onLoad(options) {
    // 加载分类数据
    this.loadCategories()

    this.initFormValidation()
  },

  // 加载分类数据（使用systemInfo服务）
  async loadCategories() {
    try {
      // 获取分类数据
      const categories = await systemInfoService.getCommunityServiceCategories()
      
      if (categories && categories.length > 0) {
        // 为Picker组件准备数据格式 - 直接使用字符串数组
        const categoryOptions = categories.map(item => item.dictLabel)
        
        // 存储完整的分类数据用于后续处理
        this.setData({
          categories: categoryOptions,
          categoryData: categories // 存储完整数据
        })
      }
      
      // 获取标签数据
      const tags = await systemInfoService.getCommunityServiceTags()
      
      if (tags && tags.length > 0) {
        // 为每个标签添加selected属性
        const tagsWithSelected = tags.map(tag => ({
          ...tag,
          selected: false
        }))
        this.setData({
          availableTags: tagsWithSelected
        })
      }
      
    } catch (error) {
      console.error('获取系统信息失败:', error)
      // systemInfo 服务已有默认配置兜底，无需额外处理
    }
  },

  // 初始化表单验证
  initFormValidation() {
    const {
      name,
      description,
      address,
      category,
      contactPhone
    } = this.data.formData;

    // 添加必填字段验证
    const isNameValid = name && name.trim().length > 0 && name.trim().length <= 50;
    const isDescriptionValid = description && typeof description === 'string' &&
                              description.trim().length > 0 &&
                              description.length <= this.data.MAX_DESCRIPTION_LENGTH;
    const isAddressValid = address && address.trim().length > 0;
    const isCategoryValid = category !== '';
    const isImageValid = this.data.fileList.length > 0;
    
    // 联系电话验证（可选字段）
    let isPhoneValid = true;
    if (contactPhone && contactPhone.trim()) {
      const phoneRegex = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8}|400\d{7}|800\d{7}|95\d{3,5})$/;
      isPhoneValid = phoneRegex.test(contactPhone.replace(/\s+/g, ''));
    }

    const formValid = isNameValid && isDescriptionValid && isAddressValid &&
                      isCategoryValid && isImageValid && isPhoneValid;

    // 错误提示
    let nameError = '';
    let descriptionError = '';
    let addressError = '';
    let imageError = '';
    let phoneError = '';

    if (name && name.trim().length > 50) {
      nameError = '名称不能超过50个字符';
    }
    
    if (description && description.length > this.data.MAX_DESCRIPTION_LENGTH) {
      descriptionError = `描述不能超过${this.data.MAX_DESCRIPTION_LENGTH}个字符`;
    }
    
    if (!isAddressValid && address !== '') {
      addressError = '请选择正确的地址';
    }
    
    if (this.data.fileList.length === 0) {
      imageError = '请至少上传一张图片';
    }
    
    if (contactPhone && contactPhone.trim() && !isPhoneValid) {
      phoneError = '请输入正确的联系电话';
    }

    this.setData({
      formValid,
      nameError,
      descriptionError,
      addressError,
      imageError,
      phoneError
    });
  },

  // 名称输入变化
  onNameChange(e) {
    this.setData({
      'formData.name': e.detail
    });
    this.initFormValidation();
  },

  // 描述输入变化
  onDescriptionChange(e) {
    // 直接获取输入框的值
    const value = e.detail.value;

    // 确保值是字符串，如果是空值则设为空字符串
    let description = '';
    if (value !== undefined && value !== null) {
      description = value.toString();
    }

    this.setData({
      'formData.description': description
    });
    this.initFormValidation();
  },

  // 联系电话输入变化
  onContactPhoneChange(e) {
    this.setData({
      'formData.contactPhone': e.detail
    });
    this.initFormValidation();
  },

  // 营业时间输入变化
  onBusinessHoursChange(e) {
    this.setData({
      'formData.businessHours': e.detail
    });
  },

  // 选择位置
  chooseLocation() {
    const residential = require('../../services/residential.js');
    
    wx.chooseLocation({
      success: (res) => {
        // 验证经纬度数据
        if (!res.longitude || !res.latitude) {
          wx.showToast({
            title: '位置信息无效，请重新选择',
            icon: 'none'
          });
          return;
        }
        
        const pointLocation = residential.createPointLocation(res.longitude, res.latitude);
        // 验证生成的POINT格式
        if (!pointLocation || pointLocation.trim() === '') {
          wx.showToast({
            title: '位置格式错误，请重新选择',
            icon: 'none'
          });
          return;
        }
        
        this.setData({
          'formData.address': res.address,
          'formData.location': pointLocation
        });
        this.initFormValidation();
        
        },
      fail: (err) => {
        console.error('选择位置失败:', err);
        
        if (err.errMsg === 'chooseLocation:fail cancel') {
          return; // 用户取消，不显示错误
        }
        
        // 其他错误情况
        let errorMsg = '选择位置失败';
        if (err.errMsg.includes('permission')) {
          errorMsg = '需要位置权限才能选择地址，请在设置中开启';
        } else if (err.errMsg.includes('network')) {
          errorMsg = '网络连接失败，请检查网络后重试';
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 隐藏分类选择器
  hideCategoryPicker() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 确认分类选择
  onCategoryConfirm(e) {
    const { value, index } = e.detail;
    // 从categoryData中获取对应的分类信息
    const selectedCategory = this.data.categoryData[index]
    if (selectedCategory) {
      this.setData({
        'formData.category': selectedCategory.dictValue, // 存储dictValue用于提交
        'formData.categoryName': selectedCategory.dictLabel, // 存储dictLabel用于显示
        showCategoryPicker: false
      });
      this.initFormValidation();
      } else {
      console.error('分类选择失败，找不到对应的分类数据')
    }
  },

  // 显示标签选择器
  showTagPicker() {
    if (this.data.availableTags.length === 0) {
      wx.showToast({
        title: '标签数据加载中，请稍后重试',
        icon: 'none'
      });
      return;
    }
    this.setData({
      showTagPicker: true
    });
  },

  // 隐藏标签选择器
  hideTagPicker() {
    this.setData({
      showTagPicker: false
    });
  },

  // 切换标签选择状态
  onTagToggle(e) {
    const tagValue = e.currentTarget.dataset.value;
    
    // 直接修改availableTags数组中对应标签的selected状态
    const availableTags = this.data.availableTags.map(tag => {
      if (tag.dictValue === tagValue) {
        return { ...tag, selected: !tag.selected }
      }
      return tag
    })
    
    // 获取所有选中的标签
    const selectedTags = availableTags.filter(tag => tag.selected).map(tag => tag.dictValue)
    
    // 检查是否超过5个标签限制
    if (selectedTags.length > 5) {
      wx.showToast({
        title: '最多选择5个标签',
        icon: 'none'
      });
      return;
    }
    
    // 计算选中标签的名称
    const selectedTagNames = this.getSelectedTagNames(selectedTags);
    
    // 更新状态
    this.setData({
      availableTags: availableTags,
      selectedTags: selectedTags,
      selectedTagNames: selectedTagNames,
      'formData.tags': selectedTags.join(',')
    })
  },

  // 获取选中标签的名称
  getSelectedTagNames: function(selectedTags) {
    if (!selectedTags || selectedTags.length === 0) {
      return '';
    }
    
    const tagNames = selectedTags.map(tagValue => {
      const tag = this.data.availableTags.find(item => item.dictValue === tagValue);
      return tag ? tag.dictLabel : tagValue;
    });
    
    if (tagNames.length > 2) {
      return `${tagNames[0]}、${tagNames[1]} 等${tagNames.length}个`;
    } else {
      return tagNames.join('、');
    }
  },

  // 确认标签选择
  onTagConfirm() {
    this.setData({
      showTagPicker: false
    });
  },

  // 上传图片后
  async afterRead(event) {
    const { file } = event.detail;
    // 转换为数组处理，支持单文件和多文件
    const files = Array.isArray(file) ? file : [file];

    // 检查文件总数量限制
    if (this.data.fileList.length + files.length > this.data.MAX_UPLOAD_COUNT) {
      wx.showToast({
        title: `最多上传${this.data.MAX_UPLOAD_COUNT}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      // 使用 Promise.all 同时上传多个文件
      const uploadTasks = files.map(async (file) => {
        const uploadedFile = await util.uploadFile({
          filePath: file.url,
          type: '6' // 使用类型6表示周边推荐图片
        });

        if (!uploadedFile || !uploadedFile.filePath) {
          throw new Error('文件上传失败，未获取到文件路径');
        }

        return {
          url: file.url,
          name: '周边图片',
          isImage: true,
          filePath: uploadedFile.filePath
        };
      });

      const uploadedFiles = await Promise.all(uploadTasks);

      // 更新文件列表，添加所有上传成功的文件
      this.setData({
        fileList: [...this.data.fileList, ...uploadedFiles]
      });

      this.initFormValidation();
      
      wx.hideLoading();
      wx.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 1000
      });
    } catch (error) {
      wx.hideLoading();
      console.error('图片上传失败:', error);
      
      let errorMsg = '上传失败，请重试';
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMsg = '网络连接失败，请检查网络后重试';
        } else if (error.message.includes('格式')) {
          errorMsg = '图片格式不支持，请选择JPG/PNG格式';
        } else {
          errorMsg = error.message;
        }
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 删除图片
  onDeleteFile(e) {
    const { index } = e.detail;
    if (index < 0 || index >= this.data.fileList.length) return;

    const fileList = this.data.fileList.filter((_, idx) => idx !== index);

    this.setData({
      fileList
    });
    this.initFormValidation();
  },

  // 处理表单提交
  async handleSubmit() {
    // 防重复提交检查 - 多重保护
    if (this.data.submitting) {
      return;
    }

    // 防抖检查 - 1秒内不允许重复点击
    const now = Date.now();
    if (this.lastSubmitTime && (now - this.lastSubmitTime) < 1000) {
      wx.showToast({
        title: '请勿频繁点击',
        icon: 'none',
        duration: 1000
      });
      return;
    }
    this.lastSubmitTime = now;

    // 表单验证
    if (!this.data.formValid) {
      // 显示具体的错误信息
      const errors = [
        this.data.nameError,
        this.data.descriptionError,
        this.data.addressError,
        this.data.imageError,
        this.data.phoneError
      ].filter(Boolean);

      if (errors.length > 0) {
        wx.showToast({
          title: errors[0],
          icon: 'none',
          duration: 3000
        });
        return;
      }

      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    try {
      // 立即设置提交状态，防止重复点击
      this.setData({
        submitting: true
      });

      wx.showLoading({
        title: '提交中...',
        mask: true  // 添加遮罩层，防止用户操作
      });

      // 检查是否有图片上传
      if (this.data.fileList.length === 0) {
        wx.showToast({
          title: '请至少上传一张图片',
          icon: 'none'
        });
        this.setData({
          submitting: false
        });
        wx.hideLoading();
        return;
      }

      // 处理图片路径
      const images = this.data.fileList.map(file => file.filePath).join(',');

      // 确保description是字符串
      let description = this.data.formData.description;
      if (description === undefined || description === null) {
        description = '';
      } else if (typeof description === 'object') {
        description = description.toString();
      }

      const nearbyData = {
        name: this.data.formData.name.trim(),
        description: description.trim(),
        images: images,
        address: this.data.formData.address.trim(),
        location: this.data.formData.location,
        category: this.data.formData.category,
        contactPhone: this.data.formData.contactPhone.trim(),
        businessHours: this.data.formData.businessHours.trim(),
        tags: this.data.formData.tags.trim()
      };

      // 验证关键字段
      if (!nearbyData.location || nearbyData.location.trim() === '') {
        wx.showToast({
          title: '位置信息缺失，请重新选择位置',
          icon: 'none'
        });
        this.setData({ submitting: false });
        wx.hideLoading();
        return;
      }
      
      // 验证location格式
      if (!nearbyData.location.startsWith('POINT(')) {
        wx.showToast({
          title: '位置格式错误，请重新选择位置',
          icon: 'none'
        });
        this.setData({ submitting: false });
        wx.hideLoading();
        return;
      }

      const res = await api.submitCommunityNearby(nearbyData);
      
      wx.hideLoading();

      if (res.code === 200) {
        // 提交成功后立即清空表单，防止重复提交
        this.setData({
          submitting: false,
          formData: {
            name: '',
            description: '',
            images: '',
            address: '',
            location: '',
            category: '',
            categoryName: '',
            contactPhone: '',
            businessHours: '',
            tags: ''
          },
          fileList: []
        });

        wx.showToast({
          title: '提交成功，等待审核',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          }
        });
      } else {
        throw new Error(res.msg || '提交失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交失败:', error);
      
      // 根据错误类型显示不同的提示
      let errorMsg = '提交失败，请重试';
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMsg = '网络连接失败，请检查网络后重试';
        } else if (error.message.includes('已存在')) {
          errorMsg = '该地点已存在，请勿重复提交';
        } else if (error.message.includes('图片')) {
          errorMsg = '图片上传失败，请重新上传';
        } else if (error.message.includes('重复提交')) {
          errorMsg = '请勿重复提交，请稍后再试';
        } else {
          errorMsg = error.message;
        }
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 3000
      });
    } finally {
      // 确保提交状态被重置
      this.setData({
        submitting: false
      });
    }
  },

  // 返回按钮
  onNavigateBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.navigateTo({
          url: '/pages/community-service/index'
        })
      }
    })
  }
})
