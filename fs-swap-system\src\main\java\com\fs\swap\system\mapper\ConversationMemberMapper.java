package com.fs.swap.system.mapper;

import java.util.List;
import com.fs.swap.common.core.domain.entity.ConversationMember;
import org.apache.ibatis.annotations.Param;

/**
 * 会话成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface ConversationMemberMapper 
{
    /**
     * 查询会话成员
     * 
     * @param id 会话成员主键
     * @return 会话成员
     */
    public ConversationMember selectConversationMemberById(Long id);

    /**
     * 查询会话成员列表
     * 
     * @param conversationMember 会话成员
     * @return 会话成员集合
     */
    public List<ConversationMember> selectConversationMemberList(ConversationMember conversationMember);

    /**
     * 根据会话ID和用户ID查询会话成员
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 会话成员
     */
    public ConversationMember selectByConversationAndUser(@Param("conversationId") String conversationId, 
                                                         @Param("userId") Long userId);

    /**
     * 查询会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 会话成员集合
     */
    public List<ConversationMember> selectMembersByConversation(@Param("conversationId") String conversationId);

    /**
     * 查询用户参与的所有会话
     * 
     * @param userId 用户ID
     * @return 会话成员集合
     */
    public List<ConversationMember> selectConversationsByUser(@Param("userId") Long userId);

    /**
     * 新增会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    public int insertConversationMember(ConversationMember conversationMember);

    /**
     * 批量新增会话成员
     * 
     * @param members 会话成员列表
     * @return 结果
     */
    public int batchInsertConversationMembers(@Param("members") List<ConversationMember> members);

    /**
     * 修改会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    public int updateConversationMember(ConversationMember conversationMember);

    /**
     * 更新未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param unreadCount 未读数量
     * @return 结果
     */
    public int updateUnreadCount(@Param("conversationId") String conversationId, 
                                @Param("userId") Long userId, 
                                @Param("unreadCount") Integer unreadCount);

    /**
     * 增加未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param increment 增加数量
     * @return 结果
     */
    public int incrementUnreadCount(@Param("conversationId") String conversationId, 
                                   @Param("userId") Long userId, 
                                   @Param("increment") Integer increment);

    /**
     * 清零未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param lastReadMessageId 最后已读消息ID
     * @return 结果
     */
    public int clearUnreadCount(@Param("conversationId") String conversationId, 
                               @Param("userId") Long userId,
                               @Param("lastReadMessageId") String lastReadMessageId);

    /**
     * 设置会话免打扰
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param isMuted 是否免打扰
     * @return 结果
     */
    public int updateMuteStatus(@Param("conversationId") String conversationId, 
                               @Param("userId") Long userId, 
                               @Param("isMuted") Integer isMuted);

    /**
     * 删除会话成员
     * 
     * @param id 会话成员主键
     * @return 结果
     */
    public int deleteConversationMemberById(Long id);

    /**
     * 批量删除会话成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConversationMemberByIds(Long[] ids);

    /**
     * 删除会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    public int deleteByConversationId(@Param("conversationId") String conversationId);

    /**
     * 用户离开会话（软删除）
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    public int leaveConversation(@Param("conversationId") String conversationId, 
                                @Param("userId") Long userId);

    /**
     * 检查用户是否在会话中
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    public int checkMemberExists(@Param("conversationId") String conversationId, 
                                @Param("userId") Long userId);
} 