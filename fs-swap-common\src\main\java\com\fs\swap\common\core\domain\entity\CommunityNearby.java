package com.fs.swap.common.core.domain.entity;

import com.fs.swap.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fs.swap.common.annotation.Excel;

/**
 * 社区服务-周边推荐对象 community_nearby
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public class CommunityNearby extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 周边ID */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 位置坐标(POINT类型) */
    @Excel(name = "位置坐标(POINT类型)")
    private String location;

    /** 分类(1:商户服务,2:休闲娱乐,3:公共设施) */
    @Excel(name = "分类(1:商户服务,2:休闲娱乐,3:公共设施)")
    private String category;

    

    /** 图片(多张图片用逗号分隔) */
    @Excel(name = "图片(多张图片用逗号分隔)")
    private String images;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 营业时间 */
    @Excel(name = "营业时间")
    private String businessHours;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 拨打电话次数 */
    @Excel(name = "拨打电话次数")
    private Integer callCount;

    /** 导航次数 */
    @Excel(name = "导航次数")
    private Integer navigateCount;

    /** 是否推荐(0:否,1:是) */
    @Excel(name = "是否推荐")
    private Integer isRecommended;

    /** 是否官方发布(0:否,1:是) */
    @Excel(name = "是否官方发布")
    private Integer isOfficial;

    /** 区域ID */
    @Excel(name = "区域ID")
    private Long regionId;

    /** 标签列表(逗号分隔,使用字典) */
    @Excel(name = "标签")
    private String tags;

    /** 关联社区ID */
    @Excel(name = "关联社区ID")
    private Long communityId;

    /** 关联小区ID */
    @Excel(name = "关联小区ID")
    private Long residentialId;

    /** 提交用户ID */
    @Excel(name = "提交用户ID")
    private Long submitUserId;

    /** 审核状态(0:待审核,1:已通过,2:已拒绝) */
    @Excel(name = "审核状态(0:待审核,1:已通过,2:已拒绝)")
    private Long auditStatus;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long auditUserId;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private java.util.Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 状态(0:停用,1:正常) */
    @Excel(name = "状态(0:停用,1:正常)")
    private Long status;

    /** 是否删除(0:否,1:是) */
    private Long deleted;

    /** 距离（米） */
    private Double distance;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    
    public void setImages(String images) 
    {
        this.images = images;
    }

    public String getImages() 
    {
        return images;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setBusinessHours(String businessHours) 
    {
        this.businessHours = businessHours;
    }

    public String getBusinessHours() 
    {
        return businessHours;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setCallCount(Integer callCount) 
    {
        this.callCount = callCount;
    }

    public Integer getCallCount() 
    {
        return callCount;
    }
    public void setNavigateCount(Integer navigateCount) 
    {
        this.navigateCount = navigateCount;
    }

    public Integer getNavigateCount() 
    {
        return navigateCount;
    }
    public void setIsRecommended(Integer isRecommended) 
    {
        this.isRecommended = isRecommended;
    }

    public Integer getIsRecommended() 
    {
        return isRecommended;
    }
    public void setIsOfficial(Integer isOfficial) 
    {
        this.isOfficial = isOfficial;
    }

    public Integer getIsOfficial() 
    {
        return isOfficial;
    }
    public void setRegionId(Long regionId) 
    {
        this.regionId = regionId;
    }

    public Long getRegionId() 
    {
        return regionId;
    }
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    public void setCommunityId(Long communityId) 
    {
        this.communityId = communityId;
    }

    public Long getCommunityId() 
    {
        return communityId;
    }
    public void setResidentialId(Long residentialId) 
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId() 
    {
        return residentialId;
    }
    public void setSubmitUserId(Long submitUserId) 
    {
        this.submitUserId = submitUserId;
    }

    public Long getSubmitUserId() 
    {
        return submitUserId;
    }
    public void setAuditStatus(Long auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Long getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditUserId(Long auditUserId) 
    {
        this.auditUserId = auditUserId;
    }

    public Long getAuditUserId() 
    {
        return auditUserId;
    }
    public void setAuditTime(java.util.Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public java.util.Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }
    public void setSort(Integer sort) 
    {
        this.sort = sort;
    }

    public Integer getSort() 
    {
        return sort;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setDeleted(Long deleted) 
    {
        this.deleted = deleted;
    }

    public Long getDeleted() 
    {
        return deleted;
    }
    public void setDistance(Double distance) 
    {
        this.distance = distance;
    }

    public Double getDistance() 
    {
        return distance;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("address", getAddress())
            .append("location", getLocation())
            .append("category", getCategory())

            .append("images", getImages())
            .append("description", getDescription())
            .append("contactPhone", getContactPhone())
            .append("businessHours", getBusinessHours())
            .append("viewCount", getViewCount())
            .append("callCount", getCallCount())
            .append("navigateCount", getNavigateCount())
            .append("isRecommended", getIsRecommended())
            .append("isOfficial", getIsOfficial())
            .append("regionId", getRegionId())
            .append("tags", getTags())
            .append("communityId", getCommunityId())
            .append("residentialId", getResidentialId())
            .append("submitUserId", getSubmitUserId())
            .append("auditStatus", getAuditStatus())
            .append("auditUserId", getAuditUserId())
            .append("auditTime", getAuditTime())
            .append("auditRemark", getAuditRemark())
            .append("sort", getSort())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleted", getDeleted())
            .append("distance", getDistance())
            .toString();
    }
}
