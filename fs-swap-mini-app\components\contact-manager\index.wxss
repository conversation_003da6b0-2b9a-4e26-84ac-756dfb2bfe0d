/* 联系方式管理组件样式 */

/* 联系方式弹窗样式 */
.contact-picker-popup {
  background: #f7f8fa;
  border-radius: 20px 20px 0 0;
  padding: 0;
  min-height: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.contact-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.contact-picker-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 8px;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-icon:active {
  background: #f5f5f5;
}

.contact-picker-content {
  padding: 16px;
  background: #f7f8fa;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  background: #fff;
  border-radius: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.contact-item-left {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.contact-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.contact-item-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.contact-item-value {
  font-size: 14px;
  color: #666;
}

.contact-item-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qr-preview-mini {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qr-mini-image {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  border: 1px solid #e5e5e5;
  background: #f9f9f9;
}

.contact-item-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.contact-item-status {
  font-size: 12px;
  color: #999;
  padding: 2px 8px;
  background: #f5f5f5;
  border-radius: 8px;
}

.contact-item-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 0 16px;
}

.contact-picker-footer {
  padding: 16px 24px 24px;
  background: #f7f8fa;
}

.contact-picker-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: rgba(153, 153, 153, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(153, 153, 153, 0.15);
}

.contact-picker-tip text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 联系方式编辑弹窗样式 */
.contact-edit-container {
  padding: 20px;
}

.contact-tips {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  margin-top: 8px;
  padding: 0 16px;
}

/* 二维码上传样式 */
.qr-upload-container {
  padding: 20px;
  text-align: center;
}

.qr-preview-wrapper {
  width: 200px;
  height: 200px;
  margin: 0 auto 16px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.qr-preview-wrapper:active {
  border-color: #4080FF;
  background: #f0f8ff;
}

.qr-preview {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.qr-placeholder-text {
  margin-top: 8px;
  font-size: 14px;
}
