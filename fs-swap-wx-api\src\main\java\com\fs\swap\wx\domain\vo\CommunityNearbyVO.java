package com.fs.swap.wx.domain.vo;

import lombok.Data;

/**
 * 社区服务-周边推荐VO
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
public class CommunityNearbyVO {

    /**
     * 周边ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 地址
     */
    private String address;

    /**
     * 位置坐标
     */
    private String location;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 分类
     */
    private String category;

    

    /**
     * 图片
     */
    private String images;

    /**
     * 图片列表
     */
    private String[] imageUrls;

    /**
     * 描述
     */
    private String description;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 距离（米）
     */
    private Double distance;

    /**
     * 距离（格式化）
     */
    private String distanceText;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 拨打电话次数
     */
    private Integer callCount;

    /**
     * 导航次数
     */
    private Integer navigateCount;

    /**
     * 是否推荐(0:否,1:是)
     */
    private Integer isRecommended;

    /**
     * 是否官方发布(0:否,1:是)
     */
    private Integer isOfficial;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 标签列表
     */
    private String tags;

    /**
     * 标签数组（解析后）
     */
    private String[] tagArray;

    /**
     * 社区ID
     */
    private Long communityId;

    /**
     * 小区ID
     */
    private Long residentialId;

    /**
     * 提交用户ID
     */
    private Long submitUserId;

    /**
     * 审核状态(0:待审核,1:已通过,2:已拒绝)
     */
    private Long auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 创建时间
     */
    private java.util.Date createTime;
}
