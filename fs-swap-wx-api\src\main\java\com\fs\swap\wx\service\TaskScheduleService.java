package com.fs.swap.wx.service;

import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.enums.TaskStatus;
import com.fs.swap.common.enums.TaskType;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.constant.CacheConstants;
import com.fs.swap.system.mapper.TaskConfigMapper;
import com.fs.swap.system.mapper.UserTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 任务定时调度服务
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class TaskScheduleService {
    
    private static final Logger log = LoggerFactory.getLogger(TaskScheduleService.class);
    
    @Autowired
    private TaskConfigMapper taskConfigMapper;
    
    @Autowired
    private UserTaskMapper userTaskMapper;
    
    @Autowired
    private RedisCache redisCache;

    /**
     * 每日0点重置每日任务
     * 每天凌晨0点1分执行
     */
    @Scheduled(cron = "0 1 0 * * ?")
    public void resetDailyTasks() {
        String lockKey = CacheConstants.TASK_SCHEDULE_LOCK_PREFIX + "resetDailyTasks";
        
        redisCache.nxLockWithExpire(lockKey, CacheConstants.LOCK_EXPIRE_SECONDS, () -> {
            log.info("开始执行每日任务重置...");
            
            try {
                // 获取所有每日任务配置
                TaskConfig queryConfig = new TaskConfig();
                queryConfig.setTaskType(TaskType.DAILY.getCode());
                queryConfig.setStatus("1");
                List<TaskConfig> dailyTasks = taskConfigMapper.selectTaskConfigList(queryConfig);
                
                if (dailyTasks.isEmpty()) {
                    log.info("没有找到需要重置的每日任务");
                    return;
                }
                
                Date yesterday = DateUtils.addDays(DateUtils.getNowDate(), -1);
                
                for (TaskConfig taskConfig : dailyTasks) {
                    // 查询昨天未完成的每日任务，标记为过期
                    UserTask queryTask = new UserTask();
                    queryTask.setTaskCode(taskConfig.getTaskCode());
                    queryTask.setTaskDate(yesterday);
                    queryTask.setStatus(TaskStatus.IN_PROGRESS.getCode());
                    
                    List<UserTask> incompleteTasks = userTaskMapper.selectUserTaskList(queryTask);
                    
                    for (UserTask task : incompleteTasks) {
                        task.setStatus(TaskStatus.EXPIRED.getCode());
                        userTaskMapper.updateUserTask(task);
                    }
                    
                    log.info("任务 {} 过期任务数量: {}", taskConfig.getTaskCode(), incompleteTasks.size());
                }
                
                // 清理任务相关缓存
                clearTaskCache();
                
                log.info("每日任务重置完成，处理了 {} 个任务类型", dailyTasks.size());
                
            } catch (Exception e) {
                log.error("每日任务重置失败", e);
            }
        }, () -> {
            log.info("每日任务重置已在其他节点执行，跳过本次执行");
        });
    }
    
    /**
     * 每周一0点重置每周任务
     * 每周一凌晨0点2分执行
     */
    @Scheduled(cron = "0 2 0 ? * MON")
    public void resetWeeklyTasks() {
        String lockKey = CacheConstants.TASK_SCHEDULE_LOCK_PREFIX + "resetWeeklyTasks";
        
        redisCache.nxLockWithExpire(lockKey, CacheConstants.LOCK_EXPIRE_SECONDS, () -> {
            log.info("开始执行每周任务重置...");
            
            try {
                // 获取所有每周任务配置
                TaskConfig queryConfig = new TaskConfig();
                queryConfig.setTaskType(TaskType.WEEKLY.getCode());
                queryConfig.setStatus("1");
                List<TaskConfig> weeklyTasks = taskConfigMapper.selectTaskConfigList(queryConfig);
                
                if (weeklyTasks.isEmpty()) {
                    log.info("没有找到需要重置的每周任务");
                    return;
                }
                
                for (TaskConfig taskConfig : weeklyTasks) {
                    // 查询上周未完成的每周任务，标记为过期
                    UserTask queryTask = new UserTask();
                    queryTask.setTaskCode(taskConfig.getTaskCode());
                    queryTask.setStatus(TaskStatus.IN_PROGRESS.getCode());
                    
                    List<UserTask> incompleteTasks = userTaskMapper.selectUserTaskList(queryTask);
                    
                    for (UserTask task : incompleteTasks) {
                        // 检查是否是上周的任务（通过创建时间判断）
                        Date lastWeek = DateUtils.addDays(DateUtils.getNowDate(), -7);
                        if (task.getCreateTime().before(lastWeek)) {
                            task.setStatus(TaskStatus.EXPIRED.getCode());
                            userTaskMapper.updateUserTask(task);
                        }
                    }
                    
                    log.info("任务 {} 过期任务数量: {}", taskConfig.getTaskCode(), incompleteTasks.size());
                }
                
                // 清理任务相关缓存
                clearTaskCache();
                
                log.info("每周任务重置完成，处理了 {} 个任务类型", weeklyTasks.size());
                
            } catch (Exception e) {
                log.error("每周任务重置失败", e);
            }
        }, () -> {
            log.info("每周任务重置已在其他节点执行，跳过本次执行");
        });
    }
    
    /**
     * 每月1号0点重置每月任务
     * 每月1号凌晨0点3分执行
     */
    @Scheduled(cron = "0 3 0 1 * ?")
    public void resetMonthlyTasks() {
        String lockKey = CacheConstants.TASK_SCHEDULE_LOCK_PREFIX + "resetMonthlyTasks";
        
        redisCache.nxLockWithExpire(lockKey, CacheConstants.LOCK_EXPIRE_SECONDS, () -> {
            log.info("开始执行每月任务重置...");
            
            try {
                // 获取所有每月任务配置
                TaskConfig queryConfig = new TaskConfig();
                queryConfig.setTaskType(TaskType.MONTHLY.getCode());
                queryConfig.setStatus("1");
                List<TaskConfig> monthlyTasks = taskConfigMapper.selectTaskConfigList(queryConfig);
                
                if (monthlyTasks.isEmpty()) {
                    log.info("没有找到需要重置的每月任务");
                    return;
                }
                
                for (TaskConfig taskConfig : monthlyTasks) {
                    // 查询上月未完成的每月任务，标记为过期
                    UserTask queryTask = new UserTask();
                    queryTask.setTaskCode(taskConfig.getTaskCode());
                    queryTask.setStatus(TaskStatus.IN_PROGRESS.getCode());
                    
                    List<UserTask> incompleteTasks = userTaskMapper.selectUserTaskList(queryTask);
                    
                    for (UserTask task : incompleteTasks) {
                        // 检查是否是上月的任务（通过创建时间判断）
                        Date lastMonth = DateUtils.addMonths(DateUtils.getNowDate(), -1);
                        if (task.getCreateTime().before(lastMonth)) {
                            task.setStatus(TaskStatus.EXPIRED.getCode());
                            userTaskMapper.updateUserTask(task);
                        }
                    }
                    
                    log.info("任务 {} 过期任务数量: {}", taskConfig.getTaskCode(), incompleteTasks.size());
                }
                
                // 清理任务相关缓存
                clearTaskCache();
                
                log.info("每月任务重置完成，处理了 {} 个任务类型", monthlyTasks.size());
                
            } catch (Exception e) {
                log.error("每月任务重置失败", e);
            }
        }, () -> {
            log.info("每月任务重置已在其他节点执行，跳过本次执行");
        });
    }
    
    /**
     * 每小时清理任务缓存
     * 每小时执行一次，清理可能的缓存不一致
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanTaskCache() {
        String lockKey = CacheConstants.TASK_SCHEDULE_LOCK_PREFIX + "cleanTaskCache";
        
        redisCache.nxLockWithExpire(lockKey, CacheConstants.LOCK_EXPIRE_SECONDS, () -> {
            log.info("开始清理任务系统缓存...");
            
            try {
                clearTaskCache();
                log.info("任务系统缓存清理完成");
                
            } catch (Exception e) {
                log.error("任务系统缓存清理失败", e);
            }
        }, () -> {
            log.info("任务缓存清理已在其他节点执行，跳过本次执行");
        });
    }
    
    /**
     * 清理任务相关缓存
     */
    private void clearTaskCache() {
        try {
            // 清理任务配置缓存
            redisCache.deleteObject(CacheConstants.TASK_CONFIG_CACHE_PREFIX + "*");
            
            // 清理任务事件缓存
            redisCache.deleteObject(CacheConstants.TASK_EVENT_CACHE_PREFIX + "*");
            
            // 清理用户任务缓存
            redisCache.deleteObject(CacheConstants.USER_TASK_CACHE_PREFIX + "*");
            
            // 清理任务统计缓存
            redisCache.deleteObject(CacheConstants.TASK_STATS_CACHE_PREFIX + "*");
            
            log.info("任务缓存清理完成");
            
        } catch (Exception e) {
            log.warn("清理任务缓存时出现异常: {}", e.getMessage());
        }
    }
} 