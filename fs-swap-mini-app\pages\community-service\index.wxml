<!--pages/community-service/index.wxml-->
<view class="container">
  <!-- 顶部筛选区域 -->
  <view class="filter-header">
    <!-- 搜索框 -->
    <view class="search-section">
      <van-search
        value="{{ searchValue }}"
        placeholder="搜索服务"
        bind:change="onSearchChange"
        bind:search="onSearch"
        bind:focus="onSearchFocus"
        bind:blur="onSearchBlur"
        shape="round"
        background="transparent"
        clearable
        left-icon="search"
        custom-class="search-input"
        input-class="search-input-field"
      />
    </view>
    
    <!-- 分类标签 -->
    <view class="category-section">
      <scroll-view class="category-tabs" scroll-x="true" show-scrollbar="false" enhanced="true" scroll-with-animation="true">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-tab {{nearbyFilter === item.id ? 'active' : ''}}" 
          bindtap="onNearbyFilterChange" 
          data-filter="{{item.id}}"
        >
          <text class="tab-text">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 地图区域 -->
  <view class="map-container">
    <map
      id="map"
      class="map"
      longitude="{{mapCenter.longitude}}"
      latitude="{{mapCenter.latitude}}"
      scale="{{mapScale}}"
      markers="{{markers}}"
      circles="{{serviceCircles}}"
      bindmarkertap="onMarkerTap"
      bindregionchange="onRegionChange"
      bindtap="onMapTap"
      show-location="{{true}}"
      enable-3D="{{false}}"
      enable-overlooking="{{false}}"
      enable-satellite="{{false}}"
      enable-traffic="{{false}}"
    >
      <!-- 定位按钮 -->
      <view class="location-btn" bindtap="onLocationTap">
        <van-icon name="home-o" size="20px" color="#000"/>
      </view>
      <!-- 地图控制按钮 - 已删除加减按钮 -->

      <!-- 地图信息提示 -->
      <cover-view class="map-info" wx:if="{{selectedId && !showTransportCard}}">
        <cover-view class="info-content">
          <cover-view class="info-text">点击列表项查看详情</cover-view>
        </cover-view>
      </cover-view>
    </map>
  </view>

  <!-- 遮罩层 -->
  <view class="detail-mask {{showDetail ? 'show' : ''}}" bindtap="closeDetail"></view>

  <!-- 服务信息卡片 -->
  <view class="transport-card {{showTransportCard ? 'show' : ''}}" wx:if="{{selectedService}}">
    <view class="transport-header">
      <view class="service-basic-info" bindtap="onServiceBasicInfoTap" data-item="{{selectedService}}">
        <view class="service-icon {{selectedService.category}}">
          <image
            src="{{selectedService.imageUrl || '/static/img/<EMAIL>'}}"
            mode="aspectFill"
            class="service-image"
            lazy-load="true"
          />
        </view>
        <view class="basic-info-content">
          <text class="service-name">{{selectedService.name}}</text>
          <view class="service-meta">
            <view class="rating-distance">
              <text class="distance">{{selectedService.distanceText}}</text>
              <text class="view-count" wx:if="{{selectedService.viewCount}}">已获取{{selectedService.viewCount}}次</text>
              <text class="update-time" wx:if="{{selectedService.updateTime}}">{{selectedService.updateTime}}</text>
            </view>
          </view>
          
          <view class="service-address">
            <van-icon name="location-o" size="12px" color="#999" />
            <text class="address-text">{{selectedService.address}}</text>
          </view>
          <view class="business-hours" wx:if="{{selectedService.businessHours}}">
            <!-- <text class="hours-text">营业中 {{selectedService.businessHours}}</text> -->
          </view>
        </view>
      </view>
      <view class="close-transport" bindtap="closeTransportCard">
        <van-icon name="cross" size="18px" color="#999" />
      </view>
      
      <!-- 操作按钮 -->
      <view class="transport-actions">
        <view class="transport-action-btn call-btn" bindtap="onCallService" data-item="{{selectedService}}">
          <van-icon name="phone-o" size="18px" color="#ffffff" />
          <text>拨打电话</text>
        </view>
        <view class="transport-action-btn nav-btn" bindtap="onNavigateToService" data-item="{{selectedService}}">
          <van-icon name="guide-o" size="18px" color="#ffffff" />
          <text>导航前往</text>
        </view>
      </view>
    </view>
    
    <!-- 返回列表按钮 -->
    <view class="back-to-list" bindtap="closeTransportCard">
      <van-icon name="arrow-left" size="16px" color="#666666" />
      <text class="back-text">返回列表</text>
    </view>
  </view>

  <!-- 底部列表区域 -->
  <view class="list-container {{listExpanded ? 'expanded' : ''}} {{showDetail ? 'show-detail' : ''}} {{listCollapsed ? 'collapsed' : ''}}" 
        style="height: {{listHeight}}px; transform: translateY({{listTranslateY}}px);"
        catchtouchmove="onListContainerTouchMove">
    
    <!-- 拖拽手柄 -->
    <view class="drag-handle" bindtouchstart="onDragStart" bindtouchmove="onDragMove" bindtouchend="onDragEnd">
      <view class="handle-bar"></view>
    </view>

    <!-- 收起状态提示 -->
    <view class="expand-hint {{showExpandHint ? 'show' : ''}}" bindtap="expandList">
      <view class="hint-content">
        <text class="hint-text">查看全部 {{filteredList.length}} 个结果</text>
      </view>
    </view>

    <!-- 服务详情区域 -->
    <view class="service-detail {{showDetail ? 'show' : ''}}" wx:if="{{selectedService}}">
      <view class="detail-header">
        <view class="detail-title">
          <view class="service-icon {{selectedService.category}}">
            <image
              src="{{selectedService.imageUrl || '/static/img/<EMAIL>'}}"
              mode="aspectFill"
              class="service-image"
              lazy-load="true"
            />
          </view>
          <view class="title-info">
            <text class="service-name">{{selectedService.name}}</text>
            <view class="service-status" wx:if="{{selectedService.isOpen !== undefined}}">
              <text class="status-text {{selectedService.isOpen ? 'open' : 'closed'}}">
                {{selectedService.isOpen ? '营业中' : '已打烊'}}
              </text>
            </view>
            <view class="service-meta">
              <view class="rating-distance">
                <text class="distance">{{selectedService.distanceText}}</text>
                <text class="view-count" wx:if="{{selectedService.viewCount}}">已获取{{selectedService.viewCount}}次</text>
              </view>
            </view>
          </view>
        </view>
        <view class="close-detail" bindtap="closeDetail">
          <van-icon name="cross" size="20px" color="#999" />
        </view>
      </view>

      <view class="detail-content">
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <van-icon name="location-o" size="16px" color="#666" />
            <text class="address-text">{{selectedService.address}}</text>
          </view>
          <view class="info-row" wx:if="{{selectedService.phone}}">
            <van-icon name="phone-o" size="16px" color="#666" />
            <text class="info-text">{{selectedService.phone}}</text>
          </view>
          <view class="info-row">
            <van-icon name="guide-o" size="16px" color="#666" />
            <text class="info-text">{{selectedService.distanceText}}</text>
          </view>
        </view>

        <view class="detail-section" wx:if="{{selectedService.tags && selectedService.tags.length > 0}}">
          <view class="section-title">服务标签</view>
          <view class="service-tags">
            <text class="tag" wx:for="{{selectedService.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>

        <view class="detail-actions">
          <view class="action-button call-button" bindtap="onCallService" data-item="{{selectedService}}">
            <van-icon name="phone-o" size="20px" color="#ffffff" />
            <text class="button-text">拨打电话</text>
          </view>
          <view class="action-button nav-button" bindtap="onNavigateToService" data-item="{{selectedService}}">
            <van-icon name="guide-o" size="20px" color="#ffffff" />
            <text class="button-text">导航前往</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选结果提示 -->
    <view class="filter-result {{listCollapsed ? 'hidden' : ''}}" wx:if="{{searchValue || nearbyFilter !== 'all'}}">
      <view class="result-info">
        <van-icon name="info-o" size="14px" color="#666" />
        <text class="result-text">
          <block wx:if="{{searchValue}}">搜索"{{searchValue}}" </block>
          <block wx:if="{{nearbyFilter !== 'all'}}">{{categoryNames[nearbyFilter]}} </block>
          的结果
        </text>
      </view>
      <view class="clear-filter" bindtap="clearAllFilters">
        <van-icon name="clear" size="14px" color="#999" />
        <text class="clear-text">清除</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading size="24px" color="#3B7FFF" />
      <text class="loading-text">正在搜索附近服务...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{filteredList.length === 0}}" class="empty-container">
      <van-icon name="search" size="48px" color="#cccccc" />
      <text class="empty-text">暂无相关服务</text>
      <text class="empty-tip">试试调整筛选条件或搜索关键词</text>
      <view class="empty-actions">
        <view class="empty-btn" bindtap="clearAllFilters">
          <van-icon name="replay" size="16px" />
          <text>重置筛选</text>
        </view>
        <view class="empty-btn" bindtap="onAddButtonTap">
          <van-icon name="plus" size="16px" />
          <text>添加服务</text>
        </view>
      </view>
    </view>

    <!-- 服务列表 -->
    <scroll-view wx:else class="service-list {{listCollapsed ? 'hidden' : ''}}" 
                 scroll-y="{{true}}" 
                 enhanced="{{true}}" 
                 show-scrollbar="{{false}}"
                 enable-back-to-top="{{false}}"
                 scroll-with-animation="{{true}}"
                 bindscroll="onListScroll"
                 bindtouchstart="onListTouchStart"
                 bindtouchmove="onListTouchMove"
                 bindtouchend="onListTouchEnd">
      <view 
        class="service-item {{selectedId === item.id ? 'selected' : ''}}" 
        wx:for="{{filteredList}}" 
        wx:key="id"
        data-item="{{item}}"
      >
        <!-- 左侧图标 -->
        <view class="service-icon-wrapper">
          <view class="service-icon {{item.category}}">
            <image
              src="{{item.imageUrl || '/static/img/<EMAIL>'}}"
              mode="aspectFill"
              class="service-image"
              binderror="onImageError"
              data-index="{{index}}"
              lazy-load="true"
            />
          </view>
          <view class="service-status" wx:if="{{item.isOpen !== undefined}}">
            <text class="status-text {{item.isOpen ? 'open' : 'closed'}}">{{item.isOpen ? '营业中' : '已打烊'}}</text>
          </view>
        </view>
        
        <!-- 主要信息 -->
        <view class="service-info">
          <view class="service-header">
            <text class="service-name">{{item.name}}</text>
            <view class="service-badge" wx:if="{{item.isRecommended}}">
              <text class="badge-text">推荐</text>
            </view>
          </view>
          
          <view class="service-meta">
            <view class="rating-distance">
              <text class="distance">{{item.distanceText}}</text>
              <text class="view-count" wx:if="{{item.viewCount}}">已获取{{item.viewCount}}次</text>
              <text class="update-time" wx:if="{{item.updateTime}}">{{item.updateTime}}</text>
            </view>
          </view>
          
          <view class="service-address">
            <van-icon name="location-o" size="12px" color="#999" />
            <text class="address-text">{{item.address}}</text>
          </view>
          
          <!-- 获取按钮 -->
          <view class="service-action">
            <view class="get-btn" bindtap="onGetServiceTap" data-item="{{item}}">
              <text class="get-btn-text">获取</text>
            </view>
          </view>
          
          <view class="service-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 悬浮操作按钮 -->
  <view class="floating-actions {{listExpanded ? 'list-expanded' : ''}} {{showDetail ? 'detail-shown' : ''}}" wx:if="{{!listExpanded}}">
    <view class="fab-btn refresh-btn {{refreshing ? 'loading' : ''}}" bindtap="refreshData">
      <van-icon name="{{refreshing ? 'loading' : 'replay'}}" size="20px" color="#ffffff" />
    </view>
    <view class="fab-btn my-submissions-btn" bindtap="onMySubmissionsButtonTap">
      <van-icon name="orders-o" size="20px" color="#ffffff" />
    </view>
    <view class="fab-btn add-btn" bindtap="onAddButtonTap">
      <van-icon name="plus" size="24px" color="#ffffff" />
    </view>
  </view>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess"></login-action>

  <!-- 小区认证组件 -->
  <residential-auth id="residentialAuth" />
</view>