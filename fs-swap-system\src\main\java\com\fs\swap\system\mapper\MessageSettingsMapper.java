package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.MessageSettings;
import org.apache.ibatis.annotations.Param;

/**
 * 消息设置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface MessageSettingsMapper 
{
    /**
     * 查询用户消息设置
     * 
     * @param userId 用户ID
     * @return 消息设置
     */
    public MessageSettings selectMessageSettingsByUserId(@Param("userId") Long userId);

    /**
     * 新增消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    public int insertMessageSettings(MessageSettings messageSettings);

    /**
     * 修改消息设置
     * 
     * @param messageSettings 消息设置
     * @return 结果
     */
    public int updateMessageSettings(MessageSettings messageSettings);

    /**
     * 删除用户消息设置
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteMessageSettingsByUserId(@Param("userId") Long userId);
} 