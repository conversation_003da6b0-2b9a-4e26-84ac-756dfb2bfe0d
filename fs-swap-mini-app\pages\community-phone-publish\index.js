// pages/community-phone-publish/index.js
const util = require("../../utils/util")
const api = require("../../config/api")
const systemInfoService = require("../../services/systemInfo")
const userUtils = require("../../utils/user")
const app = getApp()

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      phoneNumber: '',
      category: '',
      categoryName: '',
      description: ''
    },

    // 选择器相关
    showCategoryPicker: false,

    // 分类选项（从系统字典接口获取）
    categories: [],
    categoryData: [], // 存储完整的分类数据

    // 页面状态
    submitting: false,
    formValid: false,
    
    // 错误提示
    nameError: '',
    phoneError: '',
    lastSubmitTime: null
  },

  onLoad(options) {
    // 加载分类数据
    this.loadCategories()

    this.initFormValidation()
  },

  // 加载分类数据（从系统信息接口获取）
  loadCategories: async function() {
    try {
      // 使用统一的系统信息服务获取电话分类数据
      const categoryData = await systemInfoService.getCommunityPhoneCategories()
      
      if (categoryData && categoryData.length > 0) {
        // 为Picker组件准备数据格式 - 直接使用字符串数组
        const categoryOptions = categoryData.map(item => item.dictLabel)
        
        // 存储完整的分类数据用于后续处理
        this.setData({
          categories: categoryOptions,
          categoryData: categoryData // 存储完整数据
        })
        
        } else {
        console.warn('系统接口未返回电话分类数据，使用默认数据')
        this.loadDefaultCategories()
      }
      
    } catch (error) {
      console.error('获取电话分类数据失败:', error)
      this.loadDefaultCategories()
    }
  },

  // 加载默认分类数据（作为系统接口失败时的fallback）
  loadDefaultCategories: function() {
    const categoryOptions = ['物业服务', '便民服务', '紧急救援']
    const categoryData = [
      { dictValue: 'property', dictLabel: '物业服务' },
      { dictValue: 'service', dictLabel: '便民服务' },
      { dictValue: 'emergency', dictLabel: '紧急救援' }
    ]
    
    this.setData({
      categories: categoryOptions,
      categoryData: categoryData
    })
    
    },

  // 初始化表单验证
  initFormValidation() {
    const { name, phoneNumber, category } = this.data.formData;

    // 添加必填字段验证
    const isNameValid = name && name.trim().length > 0 && name.trim().length <= 50;
    // 改进手机号验证：支持手机号、固话、400、800等
    const phoneRegex = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8}|400\d{7}|800\d{7}|95\d{3,5})$/;
    const isPhoneNumberValid = phoneNumber && phoneRegex.test(phoneNumber.replace(/\s+/g, ''));
    const isCategoryValid = category !== '';

    const formValid = isNameValid && isPhoneNumberValid && isCategoryValid;

    // 错误提示
    let nameError = '';
    let phoneError = '';
    
    if (name && name.trim().length > 50) {
      nameError = '名称不能超过50个字符';
    }
    
    if (phoneNumber && !phoneRegex.test(phoneNumber.replace(/\s+/g, ''))) {
      phoneError = '请输入正确的电话号码';
    }

    this.setData({
      formValid,
      nameError,
      phoneError
    });
  },

  // 名称输入
  onNameChange(e) {
    this.setData({
      'formData.name': e.detail
    });
    this.initFormValidation();
  },

  // 电话号码输入
  onPhoneNumberChange(e) {
    this.setData({
      'formData.phoneNumber': e.detail
    });
    this.initFormValidation();
  },

  // 描述输入
  onDescriptionChange(e) {
    this.setData({
      'formData.description': e.detail
    });
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  // 隐藏分类选择器
  hideCategoryPicker() {
    this.setData({ showCategoryPicker: false });
  },

  // 确认分类选择
  onCategoryConfirm(e) {
    const { value, index } = e.detail;
    
    // 从完整的分类数据中获取对应的值
    const selectedCategory = this.data.categoryData[index];
    
    this.setData({
      'formData.category': selectedCategory ? selectedCategory.dictValue : '',
      'formData.categoryName': selectedCategory ? selectedCategory.dictLabel : '',
      showCategoryPicker: false
    });
    this.initFormValidation();
  },

  // 表单提交
  handleSubmit() {
    if (!this.data.formValid || this.data.submitting) {
      return;
    }

    // 防重复提交检查
    const now = Date.now();
    if (this.data.lastSubmitTime && (now - this.data.lastSubmitTime) < 3000) {
      wx.showToast({
        title: '请勿重复提交',
        icon: 'none'
      });
      return;
    }

    const { name, phoneNumber, category } = this.data.formData;

    if (!name.trim()) {
      wx.showToast({
        title: '请输入电话名称',
        icon: 'none'
      });
      return;
    }

    if (!phoneNumber.trim()) {
      wx.showToast({
        title: '请输入电话号码',
        icon: 'none'
      });
      return;
    }

    if (!category) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      });
      return;
    }

    this.setData({
      submitting: true,
      lastSubmitTime: now
    });

    // 构建提交数据
    const submitData = {
      name: name.trim(),
      phoneNumber: phoneNumber.trim(),
      category: category,
      description: this.data.formData.description ? this.data.formData.description.trim() : ''
    };

    // 调用API提交
    api.submitCommunityPhone(submitData).then(res => {
      if (res.code === 200) {
        wx.showToast({
          title: '提交成功，等待审核',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          }
        });
      } else {
        throw new Error(res.msg || '提交失败');
      }
    }).catch(error => {
      console.error('提交失败:', error);
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        submitting: false
      });
    });
  }
})
