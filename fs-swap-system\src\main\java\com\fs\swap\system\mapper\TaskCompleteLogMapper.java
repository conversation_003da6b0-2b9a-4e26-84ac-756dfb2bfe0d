package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.TaskCompleteLog;
import java.util.List;

/**
 * 任务完成记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface TaskCompleteLogMapper {
    /**
     * 查询任务完成记录
     * 
     * @param id 任务完成记录主键
     * @return 任务完成记录
     */
    public TaskCompleteLog selectTaskCompleteLogById(Long id);

    /**
     * 查询任务完成记录列表
     * 
     * @param taskCompleteLog 任务完成记录
     * @return 任务完成记录集合
     */
    public List<TaskCompleteLog> selectTaskCompleteLogList(TaskCompleteLog taskCompleteLog);

    /**
     * 新增任务完成记录
     * 
     * @param taskCompleteLog 任务完成记录
     * @return 结果
     */
    public int insertTaskCompleteLog(TaskCompleteLog taskCompleteLog);

    /**
     * 修改任务完成记录
     * 
     * @param taskCompleteLog 任务完成记录
     * @return 结果
     */
    public int updateTaskCompleteLog(TaskCompleteLog taskCompleteLog);

    /**
     * 删除任务完成记录
     * 
     * @param id 任务完成记录主键
     * @return 结果
     */
    public int deleteTaskCompleteLogById(Long id);

    /**
     * 批量删除任务完成记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskCompleteLogByIds(Long[] ids);
} 