import request from '@/utils/request'

// 查询社区服务-周边推荐列表
export function listNearby(query) {
  return request({
    url: '/operation/nearby/list',
    method: 'get',
    params: query
  })
}

// 查询社区服务-周边推荐详细
export function getNearby(id) {
  return request({
    url: '/operation/nearby/' + id,
    method: 'get'
  })
}

// 新增社区服务-周边推荐
export function addNearby(data) {
  return request({
    url: '/operation/nearby',
    method: 'post',
    data: data
  })
}

// 修改社区服务-周边推荐
export function updateNearby(data) {
  return request({
    url: '/operation/nearby',
    method: 'put',
    data: data
  })
}

// 删除社区服务-周边推荐
export function delNearby(id) {
  return request({
    url: '/operation/nearby/' + id,
    method: 'delete'
  })
}

// 审核周边推荐
export function auditNearby(data) {
  return request({
    url: '/operation/nearby/audit',
    method: 'put',
    data: data
  })
}
