package com.fs.swap.wx.domain.convert;

import com.fs.swap.common.core.domain.entity.CommunityPhone;
import com.fs.swap.wx.domain.vo.CommunityPhoneVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 社区服务-常用电话对象转换器
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@Mapper
public interface CommunityPhoneConvert {
    
    CommunityPhoneConvert INSTANCE = Mappers.getMapper(CommunityPhoneConvert.class);
    
    /**
     * 实体转VO
     */
    @Mapping(target = "description", source = "description", qualifiedByName = "nullToEmpty")
    CommunityPhoneVO toVO(CommunityPhone entity);
    
    /**
     * 实体列表转VO列表
     */
    List<CommunityPhoneVO> toVOList(List<CommunityPhone> entityList);
    
    /**
     * 将null转换为空字符串
     */
    @Named("nullToEmpty")
    default String nullToEmpty(String value) {
        return value != null ? value : "";
    }
}
