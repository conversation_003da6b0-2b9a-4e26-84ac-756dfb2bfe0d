<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.UserFeedbackMapper">
    
    <resultMap type="UserFeedback" id="UserFeedbackResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="images"    column="images"    />
        <result property="contact"    column="contact"    />
        <result property="createTime"    column="create_time"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatar"    column="avatar"    />
    </resultMap>

    <sql id="selectUserFeedbackVo">
        select uf.id, uf.user_id, uf.content, uf.images, uf.contact, uf.create_time,
               ui.nickname, ui.avatar
        from user_feedback uf
        left join user_info ui on uf.user_id = ui.id
    </sql>

    <select id="selectUserFeedbackList" parameterType="UserFeedback" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        <where>  
            <if test="userId != null "> and uf.user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and uf.content like concat('%', #{content}, '%')</if>
            <if test="contact != null  and contact != ''"> and uf.contact = #{contact}</if>
        </where>
        order by uf.create_time desc
    </select>
    
    <select id="selectUserFeedbackById" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where uf.id = #{id}
    </select>

    <select id="selectUserFeedbackListByUserId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where uf.user_id = #{userId}
        order by uf.create_time desc
    </select>
        
    <insert id="insertUserFeedback" parameterType="UserFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into user_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="images != null">images,</if>
            <if test="contact != null">contact,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="images != null">#{images},</if>
            <if test="contact != null">#{contact},</if>
            now()
         </trim>
    </insert>

    <update id="updateUserFeedback" parameterType="UserFeedback">
        update user_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="images != null">images = #{images},</if>
            <if test="contact != null">contact = #{contact},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserFeedbackById" parameterType="Long">
        delete from user_feedback where id = #{id}
    </delete>

    <delete id="deleteUserFeedbackByIds" parameterType="String">
        delete from user_feedback where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
