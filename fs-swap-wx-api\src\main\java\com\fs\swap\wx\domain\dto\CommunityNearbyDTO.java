package com.fs.swap.wx.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 社区服务-周边推荐提交DTO
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
public class CommunityNearbyDTO {

    /**
     * 服务名称
     */
    @NotBlank(message = "服务名称不能为空")
    private String name;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 位置坐标（经度,纬度格式）
     */
    @NotBlank(message = "位置坐标不能为空")
    private String location;

    /**
     * 分类
     */
    @NotBlank(message = "分类不能为空")
    private String category;

    /**
     * 图片列表(JSON格式)
     */
    private String images;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 标签列表(逗号分隔)
     */
    private String tags;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 营业时间
     */
    private String businessHours;
}
