package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 月度排行榜对象 monthly_ranking
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyRanking extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 年月，格式：yyyy-MM */
    @Excel(name = "年月")
    private String yearMonth;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long residentialId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 当月累计碳豆数量 */
    @Excel(name = "累计碳豆")
    private Long totalSilver;

    // 以下字段不在数据库中，用于前端显示
    /** 用户昵称 */
    private String nickname;

    /** 用户头像 */
    private String avatar;
    
    /** 排名位置（动态计算，不存储在数据库） */
    private Integer rankPosition;
} 