package com.fs.swap.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.common.utils.StringUtils;
import com.fs.swap.common.core.domain.entity.UserInfo;
import com.fs.swap.system.mapper.UserInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fs.swap.system.mapper.ConversationMapper;
import com.fs.swap.common.core.domain.entity.Conversation;
import com.fs.swap.common.core.domain.dto.ConversationListDTO;
import com.fs.swap.system.service.IConversationService;
import com.fs.swap.system.service.IConversationMemberService;

/**
 * 会话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024
 */
@Service
public class ConversationServiceImpl implements IConversationService 
{
    @Autowired
    private ConversationMapper conversationMapper;
    
    @Autowired
    private IConversationMemberService conversationMemberService;
    
    @Autowired
    private UserInfoMapper userInfoMapper;

    /**
     * 查询会话
     * 
     * @param id 会话主键
     * @return 会话
     */
    @Override
    public Conversation selectConversationById(String id)
    {
        return conversationMapper.selectConversationById(id);
    }

    /**
     * 查询会话列表
     * 
     * @param conversation 会话
     * @return 会话集合
     */
    @Override
    public List<Conversation> selectConversationList(Conversation conversation)
    {
        return conversationMapper.selectConversationList(conversation);
    }

    /**
     * 获取用户会话列表
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    @Override
    public List<ConversationListDTO> getUserConversations(Long userId)
    {
        // 直接返回SQL查询结果，SQL已经处理了单聊会话的title和avatar动态设置
        return conversationMapper.getUserConversations(userId);
    }

    /**
     * 新增会话
     * 
     * @param conversation 会话
     * @return 结果
     */
    @Override
    public int insertConversation(Conversation conversation)
    {
        if (StringUtils.isEmpty(conversation.getId())) {
            return 0;
        }
        
        Date now = DateUtils.getNowDate();
        conversation.setCreateTime(now);
        conversation.setUpdateTime(now);
        
        if (StringUtils.isEmpty(conversation.getType())) {
            conversation.setType("single");
        }
        
        return conversationMapper.insertConversation(conversation);
    }

    /**
     * 修改会话
     * 
     * @param conversation 会话
     * @return 结果
     */
    @Override
    public int updateConversation(Conversation conversation)
    {
        conversation.setUpdateTime(DateUtils.getNowDate());
        return conversationMapper.updateConversation(conversation);
    }

    /**
     * 更新会话最后消息信息
     * 
     * @param conversationId 会话ID
     * @param lastMessageId 最后消息ID
     * @param lastMessageContent 最后消息内容
     * @param lastMessageTime 最后消息时间
     * @return 结果
     */
    @Override
    public int updateLastMessage(String conversationId, String lastMessageId, 
                               String lastMessageContent, Date lastMessageTime)
    {
        return conversationMapper.updateLastMessage(conversationId, lastMessageId, 
                                                   lastMessageContent, lastMessageTime);
    }

    /**
     * 批量删除会话
     * 
     * @param ids 需要删除的会话主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteConversationByIds(String[] ids)
    {
        // 删除会话时同时删除相关的会话成员
        for (String id : ids) {
            conversationMemberService.deleteByConversationId(id);
        }
        return conversationMapper.deleteConversationByIds(ids);
    }

    /**
     * 删除会话信息
     * 
     * @param id 会话主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteConversationById(String id)
    {
        // 删除会话时同时删除相关的会话成员
        conversationMemberService.deleteByConversationId(id);
        return conversationMapper.deleteConversationById(id);
    }

    /**
     * 检查会话是否存在
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    @Override
    public boolean checkConversationExists(String conversationId)
    {
        return conversationMapper.checkConversationExists(conversationId) > 0;
    }

    /**
     * 创建或获取单聊会话
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    @Override
    @Transactional
    public String createOrGetSingleConversation(Long userId1, Long userId2)
    {
        // 首先尝试查找已存在的会话
        String existingConversationId = getSingleConversationId(userId1, userId2);
        if (StringUtils.isNotEmpty(existingConversationId)) {
            return existingConversationId;
        }
        
        // 生成新的会话ID
        String conversationId = generateConversationId(userId1, userId2);
        
        // 创建会话记录，title和avatar保持为空
        // 在获取会话列表时动态设置
        Conversation conversation = new Conversation();
        conversation.setId(conversationId);
        conversation.setType("single");
        conversation.setTitle(""); // 单聊的标题动态设置
        conversation.setAvatar(""); // 单聊的头像动态设置
        
        insertConversation(conversation);
        
        // 添加会话成员
        conversationMemberService.addMemberToConversation(conversationId, userId1);
        conversationMemberService.addMemberToConversation(conversationId, userId2);
        
        return conversationId;
    }

    /**
     * 根据参与者获取单聊会话ID
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    @Override
    public String getSingleConversationId(Long userId1, Long userId2)
    {
        return conversationMapper.getSingleConversationId(userId1, userId2);
    }

    /**
     * 生成会话ID
     * 
     * @param userId1 用户1
     * @param userId2 用户2
     * @return 会话ID
     */
    @Override
    public String generateConversationId(Long userId1, Long userId2)
    {
        // 生成会话ID格式：conv_小ID_大ID，确保ID的一致性
        Long smallerId = Math.min(userId1, userId2);
        Long largerId = Math.max(userId1, userId2);
        return "conv_" + smallerId + "_" + largerId;
    }
} 