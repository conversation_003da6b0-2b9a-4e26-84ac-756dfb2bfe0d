<!--任务中心页面-->
<view class="task-container">
  <!-- 任务列表 -->
  <view class="task-list">
    <view class="task-item" wx:for="{{tasks}}" wx:key="id">
      <view class="task-icon">
        <text class="reward-amount">{{item.rewardSilver || 0}}</text>
        <text class="reward-label">碳豆</text>
      </view>
      <view class="task-content">
        <view class="task-title">{{item.taskName || '任务名称'}}</view>
        <view class="task-desc">{{item.taskDesc || '任务描述'}}</view>
        
        <!-- 进度条 -->
        <view class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill" 
                  style="width: {{(item.progressPercent || 0)}}%;"></view>
          </view>
          <text class="progress-text">{{item.currentCount || 0}}/{{item.targetCount || 0}}</text>
        </view>
        
        <!-- 任务状态 -->
        <view class="task-status">
          <text class="status-text status-{{item.status || 0}}">{{item.statusName || '进行中'}}</text>
          <text class="task-type">{{item.taskTypeName || '普通任务'}}</text>
        </view>
      </view>
      
      <view class="task-action">
        <!-- 操作按钮 -->
        <button wx:if="{{item.canClaim}}" 
                class="action-btn claim-btn {{isClaiming ? 'disabled' : ''}}" 
                bindtap="claimReward" 
                data-task-id="{{item.id}}"
                disabled="{{isClaiming}}">
          领取
        </button>
        <button wx:elif="{{item.isCompleted}}" 
                class="action-btn completed-btn" 
                disabled>
          已领取
        </button>
        <button wx:else 
                class="action-btn progress-btn" 
                bindtap="goToTask" 
                data-task-code="{{item.taskCode}}">
          去完成
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && tasks.length === 0}}">
    <image class="empty-icon" src="/static/img/empty_task.png"></image>
    <text class="empty-text">暂无任务</text>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{loading}}">
  <text>加载中...</text>
</view> 