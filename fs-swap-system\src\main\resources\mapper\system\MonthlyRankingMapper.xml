<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.MonthlyRankingMapper">
    
    <resultMap type="MonthlyRanking" id="MonthlyRankingResult">
        <result property="id"            column="id"            />
        <result property="yearMonth"     column="year_month"    />
        <result property="residentialId" column="residential_id"/>
        <result property="userId"        column="user_id"       />
        <result property="totalSilver"   column="total_silver"  />
        <result property="createTime"    column="create_time"   />
        <result property="updateTime"    column="update_time"   />
        <result property="nickname"      column="nickname"      />
        <result property="avatar"        column="avatar"        />
    </resultMap>

    <sql id="selectMonthlyRankingVo">
        select id, `year_month`, residential_id, user_id, total_silver, create_time, update_time 
        from monthly_ranking
    </sql>

    <select id="selectMonthlyRankingList" parameterType="MonthlyRanking" resultMap="MonthlyRankingResult">
        <include refid="selectMonthlyRankingVo"/>
        <where>  
            <if test="yearMonth != null and yearMonth != ''"> and `year_month` = #{yearMonth}</if>
            <if test="residentialId != null "> and residential_id = #{residentialId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="totalSilver != null "> and total_silver = #{totalSilver}</if>
        </where>
        order by `year_month` desc, residential_id, total_silver desc
    </select>
    
    <select id="selectMonthlyRankingById" parameterType="Long" resultMap="MonthlyRankingResult">
        <include refid="selectMonthlyRankingVo"/>
        where id = #{id}
    </select>

    <select id="selectRankingWithUserInfo" resultMap="MonthlyRankingResult">
        SELECT 
            mr.id,
            mr.`year_month`,
            mr.residential_id,
            mr.user_id,
            mr.total_silver,
            ui.nickname,
            ui.avatar,
            ra.name as residential_name
        FROM monthly_ranking mr
        LEFT JOIN user_info ui ON mr.user_id = ui.id
        LEFT JOIN residential_area ra ON mr.residential_id = ra.id
        WHERE mr.`year_month` = #{yearMonth} AND mr.residential_id = #{residentialId}
        ORDER BY mr.total_silver DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectUserRankingInfo" resultMap="MonthlyRankingResult">
        SELECT
            mr.id,
            mr.`year_month`,
            mr.residential_id,
            mr.user_id,
            mr.total_silver,
            ui.nickname,
            ui.avatar,
            ra.name as residential_name
        FROM monthly_ranking mr
        LEFT JOIN user_info ui ON mr.user_id = ui.id
        LEFT JOIN residential_area ra ON mr.residential_id = ra.id
        WHERE mr.`year_month` = #{yearMonth} AND mr.user_id = #{userId}
    </select>

    <insert id="insertMonthlyRanking" parameterType="MonthlyRanking" useGeneratedKeys="true" keyProperty="id">
        insert into monthly_ranking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">`year_month`,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalSilver != null">total_silver,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">#{yearMonth},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalSilver != null">#{totalSilver},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateMonthlyRanking" parameterType="MonthlyRanking">
        update monthly_ranking
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearMonth != null and yearMonth != ''">`year_month` = #{yearMonth},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalSilver != null">total_silver = #{totalSilver},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyRankingById" parameterType="Long">
        delete from monthly_ranking where id = #{id}
    </delete>

    <delete id="deleteMonthlyRankingByIds" parameterType="String">
        delete from monthly_ranking where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByYearMonth" parameterType="String">
        delete from monthly_ranking where `year_month` = #{yearMonth}
    </delete>

    <!-- 插入或更新用户碳豆数据（需要小区ID） -->
    <insert id="insertOrUpdateUserSilver">
        INSERT INTO monthly_ranking (`year_month`, residential_id, user_id, total_silver, create_time)
        VALUES (#{yearMonth}, #{residentialId}, #{userId}, #{silverChange}, NOW())
        ON DUPLICATE KEY UPDATE 
            total_silver = total_silver + #{silverChange},
            update_time = NOW()
    </insert>

    <!-- 删除指定年月和小区的记录 -->
    <delete id="deleteByResidentialAndYearMonth">
        delete from monthly_ranking
        where `year_month` = #{yearMonth} and residential_id = #{residentialId}
    </delete>

    <!-- 计算在指定小区中碳豆数量比指定值高的用户数量 -->
    <select id="countUsersWithHigherSilver" resultType="int">
        SELECT COUNT(*)
        FROM monthly_ranking
        WHERE `year_month` = #{yearMonth}
        AND residential_id = #{residentialId}
        AND total_silver > #{totalSilver}
    </select>

</mapper>