package com.fs.swap.admin.controller.operation;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.system.service.ITaskStatisticsService;

/**
 * 任务统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/operation/task-statistics")
public class TaskStatisticsController extends BaseController {
    
    @Autowired
    private ITaskStatisticsService taskStatisticsService;

    /**
     * 获取任务统计概览
     */
    @PreAuthorize("@ss.hasPermi('operation:task-statistics:list')")
    @GetMapping("/overview")
    public AjaxResult getOverview() {
        Map<String, Object> overview = taskStatisticsService.getSystemTaskStatistics();
        return AjaxResult.success(overview);
    }

    /**
     * 获取任务统计列表
     */
    @PreAuthorize("@ss.hasPermi('operation:task-statistics:list')")
    @GetMapping("/list")
    public AjaxResult list(@RequestParam(required = false) String statisticsType,
                          @RequestParam(required = false) String taskType,
                          @RequestParam(required = false) String beginTime,
                          @RequestParam(required = false) String endTime) {
        
        // 获取真实的系统统计数据
        Map<String, Object> systemStats = taskStatisticsService.getSystemTaskStatistics();
        
        // 构建统计列表数据
        List<Map<String, Object>> statisticsList = new ArrayList<>();
        
        if ("completion".equals(statisticsType) || statisticsType == null) {
            // 任务完成统计
            Map<String, Object> completionData = new HashMap<>();
            completionData.put("date", java.time.LocalDate.now().toString());
            completionData.put("taskType", taskType != null ? taskType : "ALL");
            completionData.put("completedCount", systemStats.get("completedUserTasks"));
            completionData.put("targetCount", systemStats.get("totalUserTasks"));
            completionData.put("completionRate", systemStats.get("systemCompletionRate"));
            statisticsList.add(completionData);
        }
        
        if ("activity".equals(statisticsType)) {
            // 用户活跃统计
            Map<String, Object> activityData = new HashMap<>();
            activityData.put("date", java.time.LocalDate.now().toString());
            activityData.put("activeUsers", systemStats.get("totalUserTasks")); // 参与任务的用户数
            activityData.put("newUsers", 0); // 可以扩展新用户统计
            activityData.put("participationRate", systemStats.get("systemCompletionRate"));
            statisticsList.add(activityData);
        }
        
        // 如果没有指定类型，返回完成统计
        if (statisticsList.isEmpty()) {
            Map<String, Object> defaultData = new HashMap<>();
            defaultData.put("date", java.time.LocalDate.now().toString());
            defaultData.put("taskType", "ALL");
            defaultData.put("completedCount", systemStats.get("completedUserTasks"));
            defaultData.put("targetCount", systemStats.get("totalUserTasks"));
            defaultData.put("completionRate", systemStats.get("systemCompletionRate"));
            statisticsList.add(defaultData);
        }
        
        // 构建图表数据
        Map<String, Object> chartData = new HashMap<>();
        
        // 趋势图数据 - 基于真实统计
        Map<String, Object> trendData = new HashMap<>();
        List<String> dates = new ArrayList<>();
        List<Object> completed = new ArrayList<>();
        List<Object> target = new ArrayList<>();
        
        // 生成最近7天的数据（可以根据实际需求调整为真实的历史数据查询）
        for (int i = 6; i >= 0; i--) {
            dates.add(java.time.LocalDate.now().minusDays(i).toString());
            // 这里可以扩展为查询具体日期的统计数据
            completed.add(systemStats.get("completedUserTasks"));
            target.add(systemStats.get("totalUserTasks"));
        }
        
        trendData.put("dates", dates);
        trendData.put("completed", completed);
        trendData.put("target", target);
        
        // 分布饼图数据 - 基于真实的任务类型分布
        @SuppressWarnings("unchecked")
        Map<String, Long> configTypeDistribution = (Map<String, Long>) systemStats.get("configTypeDistribution");
        List<Map<String, Object>> distributionData = new ArrayList<>();
        
        if (configTypeDistribution != null && !configTypeDistribution.isEmpty()) {
            for (Map.Entry<String, Long> entry : configTypeDistribution.entrySet()) {
                Map<String, Object> item = new HashMap<>();
                item.put("name", getTaskTypeDisplayName(entry.getKey()));
                item.put("value", entry.getValue());
                distributionData.add(item);
            }
        } else {
            // 如果没有数据，显示默认项
            Map<String, Object> defaultItem = new HashMap<>();
            defaultItem.put("name", "暂无数据");
            defaultItem.put("value", 0);
            distributionData.add(defaultItem);
        }
        
        chartData.put("trend", trendData);
        chartData.put("distribution", distributionData);
        
        // 返回结果
        AjaxResult result = AjaxResult.success();
        result.put("rows", statisticsList);
        result.put("total", statisticsList.size());
        result.put("chartData", chartData);
        result.put("systemStats", systemStats); // 添加系统统计概览
        
        return result;
    }
    
    /**
     * 获取任务类型显示名称
     */
    private String getTaskTypeDisplayName(String taskType) {
        switch (taskType) {
            case "DAILY": return "每日任务";
            case "ONCE": return "一次性任务";
            case "WEEKLY": return "每周任务";
            case "MONTHLY": return "每月任务";
            default: return taskType;
        }
    }

    /**
     * 导出任务统计
     */
    @PreAuthorize("@ss.hasPermi('operation:task-statistics:export')")
    @Log(title = "任务统计", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response,
                      @RequestParam(required = false) String statisticsType,
                      @RequestParam(required = false) String taskType,
                      @RequestParam(required = false) String beginTime,
                      @RequestParam(required = false) String endTime) {
        
        Map<String, Object> systemStats = taskStatisticsService.getSystemTaskStatistics();
        
        // 这里可以根据需要实现具体的导出逻辑
        // 暂时返回成功，实际项目中需要生成Excel文件
        try {
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"code\":200,\"msg\":\"导出功能开发中\"}");
        } catch (Exception e) {
            logger.error("导出任务统计失败", e);
        }
    }

    /**
     * 获取任务排行榜
     */
    @PreAuthorize("@ss.hasPermi('operation:task-statistics:list')")
    @GetMapping("/ranking")
    public AjaxResult getRanking(@RequestParam String taskCode,
                               @RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> ranking = taskStatisticsService.getTaskRanking(taskCode, limit);
        return AjaxResult.success(ranking);
    }

    /**
     * 获取用户任务统计
     */
    @PreAuthorize("@ss.hasPermi('operation:task-statistics:list')")
    @GetMapping("/user")
    public AjaxResult getUserStatistics(@RequestParam Long userId) {
        Map<String, Object> userStats = taskStatisticsService.getUserTaskStatistics(userId);
        return AjaxResult.success(userStats);
    }
} 