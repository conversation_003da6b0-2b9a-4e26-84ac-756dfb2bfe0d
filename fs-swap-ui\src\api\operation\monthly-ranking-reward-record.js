import request from '@/utils/request'

// 查询月度排行榜奖励发放记录列表
export function listRewardRecord(query) {
  return request({
    url: '/operation/monthly-ranking-reward-record/list',
    method: 'get',
    params: query
  })
}

// 查询月度排行榜奖励发放记录详细
export function getRewardRecord(id) {
  return request({
    url: '/operation/monthly-ranking-reward-record/' + id,
    method: 'get'
  })
}

// 生成指定月份的奖励记录
export function generateRewards(yearMonth) {
  return request({
    url: '/operation/monthly-ranking-reward-record/generate',
    method: 'post',
    params: { yearMonth }
  })
}

// 批量发放奖励
export function batchIssueRewards() {
  return request({
    url: '/operation/monthly-ranking-reward-record/batchIssue',
    method: 'post'
  })
}

// 发放单个奖励
export function issueReward(id) {
  return request({
    url: '/operation/monthly-ranking-reward-record/issue/' + id,
    method: 'post'
  })
}

// 删除指定月份的奖励记录
export function deleteRewardsByMonth(yearMonth) {
  return request({
    url: '/operation/monthly-ranking-reward-record/deleteByMonth',
    method: 'delete',
    params: { yearMonth }
  })
} 
 