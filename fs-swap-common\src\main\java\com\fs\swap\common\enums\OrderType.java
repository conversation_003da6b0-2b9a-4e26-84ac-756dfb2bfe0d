package com.fs.swap.common.enums;

/**
 * 订单类型枚举
 */
public enum OrderType implements BaseEnum {
    /**
     * 商品订单
     */
    PRODUCT("PRODUCT", "商品"),
    
    /**
     * 互助订单
     */
    COMMUNITY_HELP("COMMUNITY_HELP", "互助");

    private final String code;
    private final String info;

    OrderType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据编码获取订单类型枚举
     *
     * @param code 编码
     * @return 订单类型枚举
     */
    public static OrderType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OrderType type : OrderType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
