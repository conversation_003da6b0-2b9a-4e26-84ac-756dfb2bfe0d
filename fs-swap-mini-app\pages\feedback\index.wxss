/* pages/feedback/index.wxss */
.container {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 16rpx;
  padding-bottom: 120rpx;
}

.feedback-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  padding: 24rpx 24rpx 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 内容输入区域 */
.content-section {
  padding: 24rpx;
  position: relative;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}

.content-textarea::placeholder {
  color: #999;
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: #999;
}

/* 图片上传区域 */
.upload-section {
  padding: 24rpx;
}

.upload-tip {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 联系方式区域 */
.contact-section {
  padding: 24rpx;
}

.contact-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.contact-input::placeholder {
  color: #999;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);
  border-top: 1rpx solid rgba(255, 255, 255, 0.8);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3B7FFF, #1E5AFF);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(59, 127, 255, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.3rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(59, 127, 255, 0.2);
}

.submit-btn[disabled] {
  background: linear-gradient(135deg, #e0e0e0, #d0d0d0);
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 上传组件样式优化 */
.van-uploader__preview {
  height: 160rpx !important;
  overflow: hidden;
}

.van-uploader__upload {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2rpx dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.van-uploader__upload:active {
  background: linear-gradient(135deg, #f0f0f0, #f8f9fa);
  border-color: #3B7FFF;
  color: #3B7FFF;
}
