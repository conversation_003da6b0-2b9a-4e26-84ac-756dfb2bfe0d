/* components/contact-modal/index.wxss */
/* 联系方式弹窗样式 */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.contact-modal-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1001;
}

.contact-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.contact-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.contact-modal-close {
  font-size: 20px;
  color: #999999;
  cursor: pointer;
  padding: 4px;
}

.contact-modal-body {
  padding: 20px 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 10px 12px;
  border-radius: 8px;
  background-color: #f8f9fc;
  transition: all 0.2s ease;
}

.contact-item:hover {
  background-color: #f0f2f8;
}

.contact-label {
  font-size: 14px;
  color: #666666;
  width: 85px;
  font-weight: 500;
}

.contact-value {
  flex: 1;
  font-size: 14px;
  color: #333333;
  margin: 0 10px;
  word-break: break-all;
  font-weight: 400;
}

.contact-call, .contact-copy, .contact-preview {
  padding: 6px 8px;
  color: #3B7FFF;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.contact-call:active, .contact-copy:active, .contact-preview:active {
  background-color: rgba(59, 127, 255, 0.1);
}

.contact-modal-footer {
  padding: 12px 16px 16px;
  display: flex;
  justify-content: center;
}

.contact-modal-btn {
  background-color: #3B7FFF;
  color: #ffffff;
  border: none;
  border-radius: 24px;
  padding: 8px 32px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(59, 127, 255, 0.3);
  transition: all 0.2s ease;
}

.contact-modal-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(59, 127, 255, 0.2);
}

.empty-contacts {
  text-align: center;
  padding: 30px 0;
  color: #999999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-contacts::before {
  content: '';
  display: block;
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23cccccc'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 8c2.67 0 8 1.33 8 4v2H4v-2c0-2.67 5.33-4 8-4zm0-2c-2.69 0-5.77 1.28-6 2h12c-.2-.71-3.3-2-6-2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

/* 微信二维码弹窗样式 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.qrcode-modal-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1001;
}

.qrcode-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.qrcode-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.qrcode-modal-close {
  font-size: 20px;
  color: #999999;
  cursor: pointer;
  padding: 4px;
}

.qrcode-modal-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  margin-bottom: 16px;
}

.qrcode-tip {
  font-size: 14px;
  color: #666666;
  text-align: center;
}
