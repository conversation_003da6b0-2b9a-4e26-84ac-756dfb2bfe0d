package com.fs.swap.wx.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 社区服务-常用电话DTO
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
public class CommunityPhoneDTO {

    @NotBlank(message = "名称不能为空")
    private String name;

    @NotBlank(message = "电话号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$", message = "电话号码格式不正确")
    private String phoneNumber;

    @NotBlank(message = "分类不能为空")
    private String category;

    private String description;
}
