package com.fs.swap.admin.controller.operation;

import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardConfig;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.system.service.IMonthlyRankingRewardConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月度排行榜奖励配置Controller
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@RestController
@RequestMapping("/operation/monthly-ranking-reward-config")
public class MonthlyRankingRewardConfigController extends BaseController {
    
    @Autowired
    private IMonthlyRankingRewardConfigService monthlyRankingRewardConfigService;

    /**
     * 查询月度排行榜奖励配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        startPage();
        List<MonthlyRankingRewardConfig> list = monthlyRankingRewardConfigService.selectMonthlyRankingRewardConfigList(monthlyRankingRewardConfig);
        return getDataTable(list);
    }

    /**
     * 导出月度排行榜奖励配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:export')")
    @Log(title = "月度排行榜奖励配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        List<MonthlyRankingRewardConfig> list = monthlyRankingRewardConfigService.selectMonthlyRankingRewardConfigList(monthlyRankingRewardConfig);
        ExcelUtil<MonthlyRankingRewardConfig> util = new ExcelUtil<MonthlyRankingRewardConfig>(MonthlyRankingRewardConfig.class);
        util.exportExcel(response, list, "月度排行榜奖励配置数据");
    }

    /**
     * 获取月度排行榜奖励配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(monthlyRankingRewardConfigService.selectMonthlyRankingRewardConfigById(id));
    }

    /**
     * 获取启用的奖励配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:list')")
    @GetMapping("/active")
    public AjaxResult getActiveConfigs() {
        List<MonthlyRankingRewardConfig> list = monthlyRankingRewardConfigService.selectActiveRewardConfigs();
        return AjaxResult.success(list);
    }

    /**
     * 新增月度排行榜奖励配置
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:add')")
    @Log(title = "月度排行榜奖励配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        // 验证排名范围
        if (monthlyRankingRewardConfig.getRankStart() > monthlyRankingRewardConfig.getRankEnd()) {
            return AjaxResult.error("排名起始位置不能大于结束位置");
        }
        
        return toAjax(monthlyRankingRewardConfigService.insertMonthlyRankingRewardConfig(monthlyRankingRewardConfig));
    }

    /**
     * 修改月度排行榜奖励配置
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:edit')")
    @Log(title = "月度排行榜奖励配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MonthlyRankingRewardConfig monthlyRankingRewardConfig) {
        // 验证排名范围
        if (monthlyRankingRewardConfig.getRankStart() > monthlyRankingRewardConfig.getRankEnd()) {
            return AjaxResult.error("排名起始位置不能大于结束位置");
        }
        
        return toAjax(monthlyRankingRewardConfigService.updateMonthlyRankingRewardConfig(monthlyRankingRewardConfig));
    }

    /**
     * 删除月度排行榜奖励配置
     */
    @PreAuthorize("@ss.hasPermi('operation:monthly-ranking-reward-config:remove')")
    @Log(title = "月度排行榜奖励配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(monthlyRankingRewardConfigService.deleteMonthlyRankingRewardConfigByIds(ids));
    }
} 