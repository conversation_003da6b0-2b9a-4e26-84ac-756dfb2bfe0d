<!--pages/community-service-detail/index.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading size="24px" color="#3B7FFF" />
    <text class="loading-text">正在加载...</text>
  </view>

  <!-- 服务详情内容 -->
  <view wx:elif="{{serviceDetail}}" class="detail-content">
    <!-- 服务图片轮播 -->
    <view class="image-section" wx:if="{{serviceDetail.imageUrls && serviceDetail.imageUrls.length > 0}}">
      <swiper 
        class="image-swiper" 
        indicator-dots="{{serviceDetail.imageUrls.length > 1}}"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ffffff"
        autoplay="{{false}}"
        circular="{{true}}"
        duration="300"
        bindchange="onSwiperChange"
      >
        <swiper-item wx:for="{{serviceDetail.imageUrls}}" wx:key="*this">
          <image 
            src="{{item}}" 
            mode="aspectFill" 
            class="service-image"
            bindtap="onImagePreview"
            data-url="{{item}}"
            data-urls="{{serviceDetail.imageUrls}}"
            lazy-load="true"
          />
        </swiper-item>
      </swiper>
      <view class="image-count" wx:if="{{serviceDetail.imageUrls.length > 1}}">
        {{currentImageIndex + 1}}/{{serviceDetail.imageUrls.length}}
      </view>
    </view>

    <!-- 默认图片 -->
    <view wx:else class="default-image-section">
      <image 
        src="/static/img/<EMAIL>" 
        mode="aspectFill" 
        class="default-service-image"
      />
    </view>

    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="service-header">
        <view class="service-title-row">
          <text class="service-name">{{serviceDetail.name}}</text>
          <view class="service-badges">
            <view class="badge official" wx:if="{{serviceDetail.isOfficial}}">
              <van-icon name="certificate" size="12px" />
              <text>官方</text>
            </view>
            <view class="badge recommended" wx:if="{{serviceDetail.isRecommended}}">
              <van-icon name="star" size="12px" />
              <text>推荐</text>
            </view>
          </view>
        </view>
        
        <view class="service-category">
          <van-icon name="{{getCategoryIcon(serviceDetail.category)}}" size="14px" color="#666" />
          <text class="category-text">{{getCategoryName(serviceDetail.category)}}</text>
        </view>

        <view class="service-stats">
          <view class="stat-item">
            <van-icon name="eye-o" size="14px" color="#999" />
            <text class="stat-text">{{serviceDetail.viewCount || 0}}次浏览</text>
          </view>
          <view class="stat-item">
            <van-icon name="phone-o" size="14px" color="#999" />
            <text class="stat-text">{{serviceDetail.callCount || 0}}次拨打</text>
          </view>
          <view class="stat-item">
            <van-icon name="guide-o" size="14px" color="#999" />
            <text class="stat-text">{{serviceDetail.navigateCount || 0}}次导航</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 位置信息卡片 -->
    <view class="info-card">
      <view class="card-title">
        <van-icon name="location-o" size="16px" color="#3B7FFF" />
        <text>位置信息</text>
      </view>
      <view class="location-info">
        <view class="address-row">
          <text class="address-text">{{serviceDetail.address}}</text>
        </view>
        <view class="distance-row" wx:if="{{serviceDetail.distance}}">
          <text class="distance-text">距离您约 {{formatDistance(serviceDetail.distance)}}</text>
        </view>
      </view>
      
      <!-- 小地图 -->
      <view class="mini-map-container" wx:if="{{mapMarkers && mapMarkers.length > 0}}">
        <map
          class="mini-map"
          longitude="{{mapMarkers[0].longitude}}"
          latitude="{{mapMarkers[0].latitude}}"
          scale="16"
          markers="{{mapMarkers}}"
          bindtap="onMapTap"
          show-location="{{false}}"
          enable-3D="{{false}}"
          enable-overlooking="{{false}}"
          enable-satellite="{{false}}"
          enable-traffic="{{false}}"
        />
        <view class="map-overlay" bindtap="onMapTap">
          <van-icon name="guide-o" size="20px" color="#3B7FFF" />
          <text class="map-overlay-text">点击查看大地图</text>
        </view>
      </view>
    </view>

    <!-- 联系信息卡片 -->
    <view class="info-card" wx:if="{{serviceDetail.contactPhone || serviceDetail.businessHours}}">
      <view class="card-title">
        <van-icon name="phone-o" size="16px" color="#3B7FFF" />
        <text>联系信息</text>
      </view>
      <view class="contact-info">
        <view class="contact-row" wx:if="{{serviceDetail.contactPhone}}">
          <view class="contact-label">
            <van-icon name="phone-o" size="14px" color="#666" />
            <text>联系电话</text>
          </view>
          <text class="contact-value phone-number" bindtap="onCallService">{{serviceDetail.contactPhone}}</text>
        </view>
        <view class="contact-row" wx:if="{{serviceDetail.businessHours}}">
          <view class="contact-label">
            <van-icon name="clock-o" size="14px" color="#666" />
            <text>营业时间</text>
          </view>
          <text class="contact-value">{{serviceDetail.businessHours}}</text>
        </view>
      </view>
    </view>

    <!-- 服务描述卡片 -->
    <view class="info-card" wx:if="{{serviceDetail.description}}">
      <view class="card-title">
        <van-icon name="notes-o" size="16px" color="#3B7FFF" />
        <text>服务描述</text>
      </view>
      <view class="description-content">
        <text class="description-text">{{serviceDetail.description}}</text>
      </view>
    </view>

    <!-- 服务标签卡片 -->
    <view class="info-card" wx:if="{{serviceDetail.tagArray && serviceDetail.tagArray.length > 0}}">
      <view class="card-title">
        <van-icon name="label-o" size="16px" color="#3B7FFF" />
        <text>服务标签</text>
      </view>
      <view class="tags-container">
        <view class="tag" wx:for="{{serviceDetail.tagArray}}" wx:key="*this">
          {{item}}
        </view>
      </view>
    </view>

    <!-- 提交信息卡片 -->
    <view class="info-card submit-info" wx:if="{{serviceDetail.createTime}}">
      <view class="card-title">
        <van-icon name="info-o" size="16px" color="#999" />
        <text>提交信息</text>
      </view>
      <view class="submit-details">
        <view class="submit-row">
          <text class="submit-label">提交时间</text>
          <text class="submit-value">{{formatTime(serviceDetail.createTime)}}</text>
        </view>
        <view class="submit-row" wx:if="{{serviceDetail.updateTime && serviceDetail.updateTime !== serviceDetail.createTime}}">
          <text class="submit-label">更新时间</text>
          <text class="submit-value">{{formatTime(serviceDetail.updateTime)}}</text>
        </view>
        <view class="submit-row" wx:if="{{auditStatusText}}">
          <text class="submit-label">审核状态</text>
          <text class="submit-value status-{{serviceDetail.auditStatus}}">{{auditStatusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <van-icon name="warning-o" size="48px" color="#cccccc" />
    <text class="error-text">{{error}}</text>
    <view class="error-actions">
      <view class="error-btn" bindtap="loadServiceDetail">
        <van-icon name="replay" size="16px" />
        <text>重新加载</text>
      </view>
      <view class="error-btn" bindtap="onNavigateBack">
        <van-icon name="arrow-left" size="16px" />
        <text>返回</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{serviceDetail}}">
    <view class="action-btn secondary" bindtap="onCallService" wx:if="{{serviceDetail.contactPhone}}">
      <van-icon name="phone-o" size="20px" />
      <text>拨打电话</text>
    </view>
    <view class="action-btn primary" bindtap="onNavigateToService">
      <van-icon name="guide-o" size="20px" />
      <text>导航前往</text>
    </view>
  </view>
</view> 