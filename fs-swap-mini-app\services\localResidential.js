/**
 * 本地小区管理服务
 * 支持未登录状态下的小区选择和存储
 */
const CONFIG = require('../config/config');

/**
 * 本地小区管理类
 */
class LocalResidentialService {
  constructor() {
    this.currentResidential = null;
    this.initialized = false;
  }

  /**
   * 初始化本地小区服务
   * 每次都从本地存储加载，保证currentResidential始终和本地一致
   */
  init() {
    // 每次都从本地存储加载，防止冷启动后currentResidential为null
    this.loadFromStorage();
    this.initialized = true;
  }

  /**
   * 从本地存储加载小区信息
   */
  loadFromStorage() {
    try {
      const stored = wx.getStorageSync(CONFIG.STORAGE_KEYS.LOCAL_RESIDENTIAL);
      if (stored) {
        this.currentResidential = stored;
      }
    } catch (error) {
      console.error('加载本地小区信息失败:', error);
    }
  }

  /**
   * 保存小区信息到本地存储
   * @param {Object} residential 小区信息
   */
  saveToStorage(residential) {
    try {
      wx.setStorageSync(CONFIG.STORAGE_KEYS.LOCAL_RESIDENTIAL, residential);
      this.currentResidential = residential;
    } catch (error) {
      console.error('保存本地小区信息失败:', error);
    }
  }

  /**
   * 获取当前选择的小区信息
   * 优先返回用户登录后的小区，其次返回本地选择的小区
   * @returns {Object|null} 小区信息
   */
  getCurrentResidential() {
    const app = getApp();

    // 如果用户已登录且有小区信息，优先使用用户的小区
    const hasLogin = app.safeGetGlobalData('hasLogin', false)
    const userInfo = app.safeGetGlobalData('userInfo')

    if (hasLogin && userInfo && userInfo.currentResidentialId && userInfo.currentResidentialId > 0) {
      return {
        id: userInfo.currentResidentialId,
        name: userInfo.currentResidentialName,
        isUserResidential: true
      };
    }
    // 否则返回本地选择的小区
    return this.currentResidential;
  }

  /**
   * 获取当前小区ID（用于API请求）
   * @returns {number|null} 小区ID
   */
  getCurrentResidentialId() {
    const residential = this.getCurrentResidential();
    return residential ? residential.id : null;
  }

  /**
   * 设置本地选择的小区
   * @param {Object} residential 小区信息
   */
  setLocalResidential(residential) {
    if (!residential || !residential.id) {
      console.error('无效的小区信息');
      return false;
    }

    const residentialData = {
      id: residential.id,
      name: residential.name,
      address: residential.address,
      location: residential.location,
      selectedAt: Date.now(), // 记录选择时间
      isUserResidential: false // 标记为本地选择的小区
    };

    this.saveToStorage(residentialData);
    
    // 触发全局事件通知其他组件
    this.notifyResidentialChange(residentialData);
    
    return true;
  }

  /**
   * 通知小区变化
   * @param {Object|null} residential 小区信息
   */
  notifyResidentialChange(residential) {
    const app = getApp();
    if (app && app.notifyResidentialChange) {
      app.notifyResidentialChange(residential);
    }
  }

  /**
   * 获取用于API请求的小区参数
   * @returns {Object} 包含小区信息的参数对象
   */
  getResidentialParams() {
    const residentialId = this.getCurrentResidentialId();
    return residentialId ? { residentialId } : {};
  }
}

// 创建单例实例
const localResidentialService = new LocalResidentialService();

module.exports = localResidentialService;
