/* pages/activity/detail/index.wxss */
.container {
  background-color: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 120px; /* 增加底部内边距，确保内容不被底部操作栏遮挡 */
}

/* 加载状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #999999;
}

.error-container text {
  margin-top: 16px;
  font-size: 14px;
}

/* 活动封面 */
.activity-cover {
  position: relative;
  height: 240px;
  width: 100%;
}

.swiper {
  width: 100%;
  height: 100%;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-status {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

/* 活动基本信息 */
.activity-info {
  background-color: #ffffff;
  padding: 20px 16px;
  margin-bottom: 12px;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.activity-title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
  line-height: 1.4;
}

.activity-meta {
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666666;
}

/* 为可点击的地址项添加样式 */
.meta-item[bindtap] {
  position: relative;
  padding: 8px;
  margin: -8px 0;
  border-radius: 6px;
}

.meta-item[bindtap]:active {
  background-color: #f0f0f0;
}

.meta-item text {
  margin-left: 8px;
  flex: 1;
}

.location-btn {
  padding: 4px;
  color: #3B7FFF;
}

/* 地图容器样式 */
.location-map-container {
  width: 100%;
  height: 180px;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.location-map-container:active {
  opacity: 0.9;
}

.map-overlay {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background-color: rgba(59, 127, 255, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

.location-map {
  width: 100%;
  height: 100%;
}

.organizer-info {
  display: flex;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f5f5f5;
}

.organizer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  cursor: pointer;
}

.organizer-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-right: 12px;
  cursor: pointer;
}

.organizer-avatar:active, .organizer-name:active {
  opacity: 0.8;
}

.organizer-time {
  font-size: 12px;
  color: #999999;
}

/* 活动描述 */
.activity-section {
  background-color: #ffffff;
  padding: 20px 16px;
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  height: 16px;
  width: 4px;
  background-color: #3B7FFF;
  border-radius: 2px;
}

.activity-description {
  font-size: 15px;
  color: #666666;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 报名列表 */
.signup-list {
  margin-bottom: 16px;
}

.signup-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.signup-user {
  display: flex;
  flex: 1;
}

.signup-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  cursor: pointer;
}

.signup-info {
  flex: 1;
}

.signup-name {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
  cursor: pointer;
}

.signup-avatar:active, .signup-name:active {
  opacity: 0.8;
}

.signup-time, .signup-contact, .signup-additional {
  font-size: 13px;
  color: #999999;
  margin-bottom: 4px;
}

.signup-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.signup-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* 评论列表 */
.comment-list {
  margin-bottom: 16px;
}

.empty-comment {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}

.comment-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  cursor: pointer;
}

.comment-info {
  flex: 1;
}

.comment-name {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  cursor: pointer;
}

.comment-avatar:active, .comment-name:active {
  opacity: 0.8;
}

.comment-time {
  font-size: 12px;
  color: #999999;
}

.comment-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 8px;
  padding-left: 40px;
}

.reply-to {
  color: #3B7FFF;
  margin-right: 4px;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.comment-reply, .comment-delete {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999999;
}

.comment-reply text, .comment-delete text {
  margin-left: 4px;
}

.comment-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  background-color: #f5f5f5;
  border-radius: 20px;
  margin-top: 16px;
  color: #666666;
}

.comment-button text {
  margin-left: 6px;
}

/* 加载更多和没有更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 16px 0;
  color: #999999;
  font-size: 14px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 12px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1px solid #f5f5f5;
  backdrop-filter: blur(10px);
}

.action-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
}

/* 发布者操作按钮样式 */
.owner-actions {
  justify-content: space-around;
}

/* 用户操作按钮样式 */
.user-actions {
  flex-direction: column;
  gap: 10px;
}

.dual-buttons {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  width: 100%;
}

/* 按钮通用样式 */
.action-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.action-btn:active {
  transform: scale(0.98) !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-icon {
  margin-right: 6px !important;
  display: flex !important;
  align-items: center !important;
}

/* 主要报名按钮样式 */
.signup-btn {
  height: 44px !important;
  font-size: 16px !important;
  background: linear-gradient(135deg, #4e8cff, #3B7FFF) !important;
  border: none !important;
  width: 100% !important;
}

/* 取消报名按钮样式 */
.cancel-signup-btn {
  flex: 1 !important;
  background-color: #ff9800 !important;
  border: none !important;
}

/* 联系发布者按钮样式 */
.contact-btn {
  flex: 1 !important;
  background-color: #5c6bc0 !important;
  border: none !important;
}

/* 编辑按钮样式 */
.edit-btn {
  flex: 1 !important;
}

/* 取消活动按钮样式 */
.cancel-btn {
  flex: 1 !important;
}

/* 删除按钮样式 */
.delete-btn {
  flex: 1 !important;
}

/* 弹窗样式 */
.comment-popup, .signup-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
}

.popup-footer {
  margin-top: auto;
  padding: 16px 0;
}




