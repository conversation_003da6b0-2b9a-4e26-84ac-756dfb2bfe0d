package com.fs.swap.system.service.impl;

import com.fs.swap.common.core.domain.entity.TaskCompleteLog;
import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.system.mapper.TaskCompleteLogMapper;
import com.fs.swap.system.mapper.TaskConfigMapper;
import com.fs.swap.system.mapper.UserTaskMapper;
import com.fs.swap.system.service.ITaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class TaskServiceImpl implements ITaskService {
    
    private static final Logger log = LoggerFactory.getLogger(TaskServiceImpl.class);
    
    @Autowired
    private TaskConfigMapper taskConfigMapper;
    
    @Autowired
    private UserTaskMapper userTaskMapper;
    
    @Autowired
    private TaskCompleteLogMapper taskCompleteLogMapper;

    // =================== 任务配置管理 ===================
    
    @Override
    public TaskConfig selectTaskConfigById(Long id) {
        return taskConfigMapper.selectTaskConfigById(id);
    }

    @Override
    public List<TaskConfig> selectTaskConfigList(TaskConfig taskConfig) {
        return taskConfigMapper.selectTaskConfigList(taskConfig);
    }

    @Override
    public int insertTaskConfig(TaskConfig taskConfig) {
        return taskConfigMapper.insertTaskConfig(taskConfig);
    }

    @Override
    public int updateTaskConfig(TaskConfig taskConfig) {
        return taskConfigMapper.updateTaskConfig(taskConfig);
    }

    @Override
    public int deleteTaskConfigByIds(Long[] ids) {
        return taskConfigMapper.deleteTaskConfigByIds(ids);
    }

    @Override
    public int deleteTaskConfigById(Long id) {
        return taskConfigMapper.deleteTaskConfigById(id);
    }

    // =================== 用户任务管理 ===================
    
    @Override
    public UserTask selectUserTaskById(Long id) {
        return userTaskMapper.selectUserTaskById(id);
    }

    @Override
    public List<UserTask> selectUserTaskList(UserTask userTask) {
        return userTaskMapper.selectUserTaskList(userTask);
    }

    @Override
    public int insertUserTask(UserTask userTask) {
        return userTaskMapper.insertUserTask(userTask);
    }

    @Override
    public int updateUserTask(UserTask userTask) {
        return userTaskMapper.updateUserTask(userTask);
    }

    @Override
    public int deleteUserTaskByIds(Long[] ids) {
        return userTaskMapper.deleteUserTaskByIds(ids);
    }

    @Override
    public int deleteUserTaskById(Long id) {
        return userTaskMapper.deleteUserTaskById(id);
    }

    // =================== 任务完成日志管理 ===================
    
    @Override
    public TaskCompleteLog selectTaskCompleteLogById(Long id) {
        return taskCompleteLogMapper.selectTaskCompleteLogById(id);
    }

    @Override
    public List<TaskCompleteLog> selectTaskCompleteLogList(TaskCompleteLog taskCompleteLog) {
        return taskCompleteLogMapper.selectTaskCompleteLogList(taskCompleteLog);
    }

    @Override
    public int insertTaskCompleteLog(TaskCompleteLog taskCompleteLog) {
        return taskCompleteLogMapper.insertTaskCompleteLog(taskCompleteLog);
    }

    @Override
    public int updateTaskCompleteLog(TaskCompleteLog taskCompleteLog) {
        return taskCompleteLogMapper.updateTaskCompleteLog(taskCompleteLog);
    }

    @Override
    public int deleteTaskCompleteLogByIds(Long[] ids) {
        return taskCompleteLogMapper.deleteTaskCompleteLogByIds(ids);
    }

    @Override
    public int deleteTaskCompleteLogById(Long id) {
        return taskCompleteLogMapper.deleteTaskCompleteLogById(id);
    }
} 