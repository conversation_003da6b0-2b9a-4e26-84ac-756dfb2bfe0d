<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="话题分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择话题分类" clearable>
          <el-option
            v-for="dict in dict.type.community_help_topic"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布类型" prop="publishType">
        <el-select v-model="queryParams.publishType" placeholder="请选择发布类型" clearable>
          <el-option
            v-for="dict in dict.type.community_help_publish_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.community_help_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间" prop="createTime">
        <el-date-picker clearable
          v-model="daterangeCreateTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="截止时间" prop="endTime">
        <el-date-picker clearable
          v-model="daterangeEndTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:communityHelp:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:communityHelp:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:communityHelp:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:communityHelp:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleBatchAudit" v-hasPermi="['operation:communityHelp:audit']">
          <el-button type="info" plain size="mini" :disabled="multiple">
            批量审核<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1">批量通过</el-dropdown-item>
            <el-dropdown-item command="3">批量拒绝</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="communityHelpList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="话题分类" align="center" prop="category">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.community_help_topic" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="发布类型" align="center" prop="publishType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.community_help_publish_type" :value="scope.row.publishType"/>
        </template>
      </el-table-column>
      <el-table-column label="发布者" align="center" prop="nickname" />
      <el-table-column label="浏览次数" align="center" prop="viewCount" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.community_help_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="截止时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:communityHelp:edit']"
          >修改</el-button>
          <el-dropdown @command="(command) => handleAudit(scope.row, command)" v-hasPermi="['operation:communityHelp:audit']">
            <el-button size="mini" type="text">
              审核<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">通过</el-dropdown-item>
              <el-dropdown-item command="3">拒绝</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:communityHelp:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改邻里互助对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="话题分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择话题分类">
            <el-option
              v-for="dict in dict.type.community_help_topic"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发布类型" prop="publishType">
          <el-select v-model="form.publishType" placeholder="请选择发布类型">
            <el-option
              v-for="dict in dict.type.community_help_publish_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入详细内容" :rows="4" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="截止时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="请选择截止时间"
            value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option
              v-for="dict in dict.type.community_help_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="邻里互助详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="detailData">
        <el-descriptions-item label="标题">{{ detailData.title }}</el-descriptions-item>
        <el-descriptions-item label="话题分类">
          <dict-tag :options="dict.type.community_help_topic" :value="detailData.category"/>
        </el-descriptions-item>
        <el-descriptions-item label="发布类型">
          <dict-tag :options="dict.type.community_help_publish_type" :value="detailData.publishType"/>
        </el-descriptions-item>
        <el-descriptions-item label="发布者">{{ detailData.nickname }}</el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{ detailData.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.community_help_status" :value="detailData.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="截止时间">{{ parseTime(detailData.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="详细内容" :span="2">{{ detailData.content }}</el-descriptions-item>
        <el-descriptions-item label="联系方式" :span="2">{{ detailData.contactInfo }}</el-descriptions-item>
        <el-descriptions-item label="审核备注" :span="2" v-if="detailData.auditRemark">{{ detailData.auditRemark }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 图片展示 -->
      <div v-if="detailImages.length > 0" style="margin-top: 20px;">
        <h4>图片</h4>
        <el-image
          v-for="(image, index) in detailImages"
          :key="index"
          :src="image"
          :preview-src-list="detailImages"
          style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
          fit="cover"
        />
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核邻里互助" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" label-width="80px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="3">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCommunityHelp, getCommunityHelp, delCommunityHelp, addCommunityHelp, updateCommunityHelp, auditCommunityHelp, batchAuditCommunityHelp } from "@/api/operation/communityHelp";

export default {
  name: "CommunityHelp",
  dicts: ['community_help_topic', 'community_help_publish_type', 'community_help_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 邻里互助表格数据
      communityHelpList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      detailData: null,
      detailImages: [],
      // 审核弹出层
      auditOpen: false,
      auditForm: {
        id: null,
        status: '1',
        auditRemark: ''
      },
      // 日期范围
      daterangeCreateTime: [],
      daterangeEndTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        category: null,
        publishType: null,
        status: null,
        createTime: null,
        endTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "详细内容不能为空", trigger: "blur" }
        ],
        category: [
          { required: true, message: "话题分类不能为空", trigger: "change" }
        ],
        publishType: [
          { required: true, message: "发布类型不能为空", trigger: "change" }
        ],
        contactInfo: [
          { required: true, message: "联系方式不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "截止时间不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询邻里互助列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeEndTime && '' != this.daterangeEndTime) {
        this.queryParams.params["beginEndTime"] = this.daterangeEndTime[0];
        this.queryParams.params["endEndTime"] = this.daterangeEndTime[1];
      }
      listCommunityHelp(this.queryParams).then(response => {
        this.communityHelpList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        content: null,
        images: null,
        category: null,
        publishType: null,
        contactInfo: null,
        viewCount: null,
        status: "1",
        communityId: null,
        residentialId: null,
        userId: null,
        endTime: null,
        deleted: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeEndTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加邻里互助";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCommunityHelp(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改邻里互助";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCommunityHelp(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCommunityHelp(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除邻里互助编号为"' + ids + '"的数据项？').then(function() {
        return delCommunityHelp(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/communityHelp/export', {
        ...this.queryParams
      }, `communityHelp_${new Date().getTime()}.xlsx`)
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      getCommunityHelp(row.id).then(response => {
        this.detailData = response.data;
        // 处理图片数据
        this.detailImages = [];
        if (this.detailData.images) {
          try {
            this.detailImages = JSON.parse(this.detailData.images);
          } catch (e) {
            // 如果不是JSON格式，按逗号分割
            this.detailImages = this.detailData.images.split(',').filter(img => img.trim());
          }
        }
        this.detailOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row, status) {
      this.auditForm = {
        id: row.id,
        status: status,
        auditRemark: ''
      };
      this.auditOpen = true;
    },
    /** 批量审核操作 */
    handleBatchAudit(status) {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审核的数据");
        return;
      }
      this.auditForm = {
        ids: this.ids,
        status: status,
        auditRemark: ''
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      if (this.auditForm.ids) {
        // 批量审核
        batchAuditCommunityHelp(this.auditForm.ids, this.auditForm.status, this.auditForm.auditRemark).then(() => {
          this.$modal.msgSuccess("审核成功");
          this.auditOpen = false;
          this.getList();
        });
      } else {
        // 单个审核
        auditCommunityHelp(this.auditForm.id, this.auditForm.status, this.auditForm.auditRemark).then(() => {
          this.$modal.msgSuccess("审核成功");
          this.auditOpen = false;
          this.getList();
        });
      }
    }
  }
};
</script>
