package com.fs.swap.system.service;

import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.domain.entity.MonthlyRanking;
import java.util.List;

/**
 * 月度排行榜Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface IMonthlyRankingService {
    
    /**
     * 获取月度排行榜列表（按小区分组）
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @param residentialId 小区ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 排行榜数据
     */
    TableDataInfo getRankingList(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize);

    /**
     * 查询月度排行榜列表（管理端使用）
     *
     * @param monthlyRanking 月度排行榜查询条件
     * @return 月度排行榜集合
     */
    List<MonthlyRanking> selectMonthlyRankingList(MonthlyRanking monthlyRanking);

    /**
     * 重置指定月份的排行榜
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @param residentialId 小区ID，为null时重置所有小区
     */
    void resetRanking(String yearMonth, Long residentialId);
} 