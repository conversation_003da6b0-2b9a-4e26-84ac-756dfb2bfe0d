package com.fs.swap.system.service.impl;

import java.util.List;

import com.fs.swap.common.core.domain.entity.CommunityPhone;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.CommunityPhoneMapper;
import com.fs.swap.system.service.ICommunityPhoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 社区服务-常用电话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
public class CommunityPhoneServiceImpl implements ICommunityPhoneService 
{
    @Autowired
    private CommunityPhoneMapper communityPhoneMapper;

    /**
     * 查询社区服务-常用电话
     * 
     * @param id 社区服务-常用电话主键
     * @return 社区服务-常用电话
     */
    @Override
    public CommunityPhone selectCommunityPhoneById(Long id)
    {
        return communityPhoneMapper.selectCommunityPhoneById(id);
    }

    /**
     * 查询社区服务-常用电话列表
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 社区服务-常用电话
     */
    @Override
    public List<CommunityPhone> selectCommunityPhoneList(CommunityPhone communityPhone)
    {
        return communityPhoneMapper.selectCommunityPhoneList(communityPhone);
    }

    /**
     * 新增社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    @Override
    public int insertCommunityPhone(CommunityPhone communityPhone)
    {
        communityPhone.setCreateTime(DateUtils.getNowDate());
        return communityPhoneMapper.insertCommunityPhone(communityPhone);
    }

    /**
     * 修改社区服务-常用电话
     * 
     * @param communityPhone 社区服务-常用电话
     * @return 结果
     */
    @Override
    public int updateCommunityPhone(CommunityPhone communityPhone)
    {
        communityPhone.setUpdateTime(DateUtils.getNowDate());
        return communityPhoneMapper.updateCommunityPhone(communityPhone);
    }

    /**
     * 批量删除社区服务-常用电话
     * 
     * @param ids 需要删除的社区服务-常用电话主键
     * @return 结果
     */
    @Override
    public int deleteCommunityPhoneByIds(Long[] ids)
    {
        return communityPhoneMapper.deleteCommunityPhoneByIds(ids);
    }

    /**
     * 删除社区服务-常用电话信息
     * 
     * @param id 社区服务-常用电话主键
     * @return 结果
     */
    @Override
    public int deleteCommunityPhoneById(Long id)
    {
        return communityPhoneMapper.deleteCommunityPhoneById(id);
    }
    
    /**
     * 根据用户所在位置查询适用的电话列表
     * 如果参数为空，则返回所有可用的电话
     *
     * @param regionId 区域ID，可为空
     * @param communityId 社区ID，可为空
     * @param residentialId 小区ID，可为空
     * @return 电话列表
     */
    @Override
    public List<CommunityPhone> selectCommunityPhoneListByLocation(Long regionId, Long communityId, Long residentialId)
    {
        return communityPhoneMapper.selectCommunityPhoneListByLocation(regionId, communityId, residentialId);
    }
    
    /**
     * 根据提交用户ID查询电话列表
     *
     * @param submitUserId 提交用户ID
     * @return 电话列表
     */
    @Override
    public List<CommunityPhone> selectCommunityPhoneListBySubmitUserId(Long submitUserId)
    {
        return communityPhoneMapper.selectCommunityPhoneListBySubmitUserId(submitUserId);
    }
    
    /**
     * 审核电话
     *
     * @param id 电话ID
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditCommunityPhone(Long id, Integer auditStatus, Long auditUserId, String auditRemark)
    {
        return communityPhoneMapper.auditCommunityPhone(id, auditStatus, auditUserId, DateUtils.getNowDate(), auditRemark);
    }
    
    /**
     * 批量审核电话
     *
     * @param ids 电话ID列表
     * @param auditStatus 审核状态
     * @param auditUserId 审核人ID
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAuditCommunityPhone(List<Long> ids, Integer auditStatus, Long auditUserId, String auditRemark)
    {
        int count = 0;
        for (Long id : ids) {
            count += auditCommunityPhone(id, auditStatus, auditUserId, auditRemark);
        }
        return count;
    }
    
    /**
     * 用户提交电话信息
     *
     * @param communityPhone 电话信息
     * @param submitUserId 提交用户ID
     * @return 结果
     */
    @Override
    public int submitCommunityPhone(CommunityPhone communityPhone, Long submitUserId)
    {
        // 设置提交相关信息
        communityPhone.setSubmitUserId(submitUserId);
        communityPhone.setAuditStatus(0L); // 待审核
        communityPhone.setStatus(1L); // 正常状态
        communityPhone.setDeleted(0L); // 未删除
        communityPhone.setSort(0); // 默认排序
        
        // 设置创建信息
        communityPhone.setCreateTime(DateUtils.getNowDate());
        if (submitUserId != null) {
            // 这里可以根据用户ID获取用户名，暂时使用用户ID作为创建者
            communityPhone.setCreateBy(submitUserId.toString());
        }
        
        return communityPhoneMapper.insertCommunityPhone(communityPhone);
    }
    
    /**
     * 更新拨打次数
     *
     * @param id 电话ID
     * @return 结果
     */
    @Override
    public int incrementCallCount(Long id)
    {
        return communityPhoneMapper.incrementCallCount(id);
    }
    
    /**
     * 根据分类统计电话数量
     *
     * @param regionId 区域ID
     * @param communityId 社区ID
     * @param residentialId 小区ID
     * @return 统计结果
     */
    @Override
    public List<CommunityPhone> getPhoneStatsByCategory(Long regionId, Long communityId, Long residentialId)
    {
        return communityPhoneMapper.getPhoneStatsByCategory(regionId, communityId, residentialId);
    }
}
