package com.fs.swap.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;

/**
 * 会话对象 conversation
 * 
 * <AUTHOR>
 * @date 2024
 */
public class Conversation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会话ID */
    private String id;

    /** 会话类型：single-单聊，group-群聊 */
    @Excel(name = "会话类型", readConverterExp = "single=单聊,group=群聊")
    private String type;

    /** 会话标题 */
    @Excel(name = "会话标题")
    private String title;

    /** 会话头像 */
    @Excel(name = "会话头像")
    private String avatar;

    /** 最后一条消息ID */
    @Excel(name = "最后一条消息ID")
    private String lastMessageId;

    /** 最后消息内容 */
    private String lastMessageContent;

    /** 最后消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastMessageTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    public void setLastMessageId(String lastMessageId) 
    {
        this.lastMessageId = lastMessageId;
    }

    public String getLastMessageId() 
    {
        return lastMessageId;
    }

    public String getLastMessageContent() {
        return lastMessageContent;
    }

    public void setLastMessageContent(String lastMessageContent) {
        this.lastMessageContent = lastMessageContent;
    }

    public Date getLastMessageTime() {
        return lastMessageTime;
    }

    public void setLastMessageTime(Date lastMessageTime) {
        this.lastMessageTime = lastMessageTime;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }
    public void setUpdateTime(Date updateTime) 
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() 
    {
        return updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("title", getTitle())
            .append("avatar", getAvatar())
            .append("lastMessageId", getLastMessageId())
            .append("lastMessageContent", getLastMessageContent())
            .append("lastMessageTime", getLastMessageTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 