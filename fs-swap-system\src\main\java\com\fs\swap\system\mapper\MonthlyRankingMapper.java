package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.MonthlyRanking;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 月度排行榜Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface MonthlyRankingMapper {
    
    /**
     * 查询月度排行榜
     * 
     * @param id 月度排行榜主键
     * @return 月度排行榜
     */
    public MonthlyRanking selectMonthlyRankingById(Long id);

    /**
     * 查询月度排行榜列表
     * 
     * @param monthlyRanking 月度排行榜
     * @return 月度排行榜集合
     */
    public List<MonthlyRanking> selectMonthlyRankingList(MonthlyRanking monthlyRanking);

    /**
     * 查询指定年月的排行榜（带用户信息）
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @param limit 限制数量
     * @return 排行榜列表
     */
    public List<MonthlyRanking> selectRankingWithUserInfo(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId, @Param("limit") Integer limit);

    /**
     * 分页查询指定年月的排行榜（带用户信息和排名）
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @param offset 偏移量
     * @param pageSize 每页数量
     * @return 排行榜列表
     */
    public List<MonthlyRanking> selectRankingWithUserInfoPaged(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 统计指定年月和小区的排行榜记录总数
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @return 记录总数
     */
    public long countRankingByResidentialAndYearMonth(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId);

    /**
     * 查询用户在指定月份的排名信息
     * 
     * @param userId 用户ID
     * @param yearMonth 年月
     * @return 用户排名信息
     */
    public MonthlyRanking selectUserRankingInfo(@Param("userId") Long userId, @Param("yearMonth") String yearMonth);

    /**
     * 新增月度排行榜
     * 
     * @param monthlyRanking 月度排行榜
     * @return 结果
     */
    public int insertMonthlyRanking(MonthlyRanking monthlyRanking);

    /**
     * 修改月度排行榜
     * 
     * @param monthlyRanking 月度排行榜
     * @return 结果
     */
    public int updateMonthlyRanking(MonthlyRanking monthlyRanking);

    /**
     * 删除月度排行榜
     * 
     * @param id 月度排行榜主键
     * @return 结果
     */
    public int deleteMonthlyRankingById(Long id);

    /**
     * 批量删除月度排行榜
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyRankingByIds(Long[] ids);

    /**
     * 根据年月删除排行榜数据
     * 
     * @param yearMonth 年月
     * @return 结果
     */
    public int deleteByYearMonth(String yearMonth);

    /**
     * 插入或更新用户碳豆数据（需要小区ID）
     * 
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @param userId 用户ID
     * @param silverChange 碳豆变化数量
     * @return 结果
     */
    public int insertOrUpdateUserSilver(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId, @Param("userId") Long userId, @Param("silverChange") Long silverChange);

    /**
     * 删除指定年月和小区的记录
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @return 结果
     */
    public int deleteByResidentialAndYearMonth(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId);

    /**
     * 计算在指定小区中碳豆数量比指定值高的用户数量
     *
     * @param yearMonth 年月
     * @param residentialId 小区ID
     * @param totalSilver 碳豆数量
     * @return 用户数量
     */
    public int countUsersWithHigherSilver(@Param("yearMonth") String yearMonth, @Param("residentialId") Long residentialId, @Param("totalSilver") Long totalSilver);
} 