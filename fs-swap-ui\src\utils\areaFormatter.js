/**
 * 区域数据格式化工具
 * 提供通用的区域、社区、小区数据格式化方法
 */
import areaDataService from '@/services/areaDataService'

/**
 * 格式化区域显示
 * @param {Object} _row 行数据
 * @param {Object} _column 列定义
 * @param {*} cellValue 单元格值
 * @returns {string} 格式化后的显示文本
 */
export function regionFormatter(_row, _column, cellValue) {
  if (!cellValue) return '-';

  try {
    // 确保区域数据已加载
    if (areaDataService.getRegions().length === 0) {
      return `${cellValue}`;
    }

    // 获取完整的区域路径名称（省/市/区/街道）
    const fullName = areaDataService.getRegionFullName(cellValue);
    if (fullName) {
      return fullName;
    }

    // 如果获取完整路径失败，尝试获取单个区域名称
    const region = areaDataService.getRegionById(cellValue);
    if (!region) {
      return `${cellValue}`;
    }

    return region.name;
  } catch (error) {
    return `${cellValue}`;
  }
}

/**
 * 格式化社区显示
 * @param {Object} _row 行数据
 * @param {Object} _column 列定义
 * @param {*} cellValue 单元格值
 * @returns {string} 格式化后的显示文本
 */
export function communityFormatter(_row, _column, cellValue) {
  if (!cellValue) return '-';

  try {
    // 确保社区数据已加载
    if (areaDataService.getCommunities().length === 0) {
      return `${cellValue}`;
    }

    const community = areaDataService.getCommunityById(cellValue);
    if (!community) {
      return `${cellValue}`;
    }

    return community.name;
  } catch (error) {
    return `${cellValue}`;
  }
}

/**
 * 格式化小区显示
 * @param {Object} _row 行数据
 * @param {Object} _column 列定义
 * @param {*} cellValue 单元格值
 * @returns {string} 格式化后的显示文本
 */
export function residentialFormatter(_row, _column, cellValue) {
  if (!cellValue) return '-';

  try {
    // 确保小区数据已加载
    if (areaDataService.getResidentials().length === 0) {
      return `${cellValue}`;
    }

    const residential = areaDataService.getResidentialById(cellValue);
    if (!residential) {
      return `${cellValue}`;
    }

    return residential.name;
  } catch (error) {
    return `${cellValue}`;
  }
}

/**
 * 获取区域名称
 * @param {Number|String} regionId 区域ID
 * @returns {String} 区域名称
 */
export function getRegionName(regionId) {
  if (!regionId) return '-';

  try {
    const region = areaDataService.getRegionById(Number(regionId));
    return region ? region.name : String(regionId);
  } catch (error) {
    return String(regionId);
  }
}

/**
 * 获取社区名称
 * @param {Number|String} communityId 社区ID
 * @returns {String} 社区名称
 */
export function getCommunityName(communityId) {
  if (!communityId) return '-';

  try {
    const community = areaDataService.getCommunityById(Number(communityId));
    return community ? community.name : String(communityId);
  } catch (error) {
    return String(communityId);
  }
}

/**
 * 获取小区名称
 * @param {Number|String} residentialId 小区ID
 * @returns {String} 小区名称
 */
export function getResidentialName(residentialId) {
  if (!residentialId) return '-';

  try {
    const residential = areaDataService.getResidentialById(Number(residentialId));
    return residential ? residential.name : String(residentialId);
  } catch (error) {
    return String(residentialId);
  }
}

export default {
  regionFormatter,
  communityFormatter,
  residentialFormatter,
  getRegionName,
  getCommunityName,
  getResidentialName
}
