<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.TaskCompleteLogMapper">
    
    <resultMap type="TaskCompleteLog" id="TaskCompleteLogResult">
        <result property="id"             column="id"               />
        <result property="userId"         column="user_id"          />
        <result property="taskCode"       column="task_code"        />
        <result property="eventType"      column="event_type"       />
        <result property="businessId"     column="business_id"      />
        <result property="rewardSilver"   column="reward_silver"    />
        <result property="deleted"        column="deleted"          />
        <result property="createTime"     column="create_time"      />
    </resultMap>

    <sql id="selectTaskCompleteLogVo">
        select id, user_id, task_code, event_type, business_id, reward_silver, deleted, create_time
        from task_complete_log
    </sql>

    <select id="selectTaskCompleteLogList" parameterType="TaskCompleteLog" resultMap="TaskCompleteLogResult">
        <include refid="selectTaskCompleteLogVo"/>
        <where>  
            deleted = 0
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTaskCompleteLogById" parameterType="Long" resultMap="TaskCompleteLogResult">
        <include refid="selectTaskCompleteLogVo"/>
        where id = #{id} and deleted = 0
    </select>
        
    <insert id="insertTaskCompleteLog" parameterType="TaskCompleteLog" useGeneratedKeys="true" keyProperty="id">
        insert into task_complete_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="eventType != null and eventType != ''">event_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="rewardSilver != null">reward_silver,</if>
            deleted,
            create_time
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="eventType != null and eventType != ''">#{eventType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="rewardSilver != null">#{rewardSilver},</if>
            0,
            now()
         </trim>
    </insert>

    <update id="updateTaskCompleteLog" parameterType="TaskCompleteLog">
        update task_complete_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="eventType != null and eventType != ''">event_type = #{eventType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="rewardSilver != null">reward_silver = #{rewardSilver},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTaskCompleteLogById" parameterType="Long">
        update task_complete_log set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteTaskCompleteLogByIds" parameterType="String">
        update task_complete_log set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 