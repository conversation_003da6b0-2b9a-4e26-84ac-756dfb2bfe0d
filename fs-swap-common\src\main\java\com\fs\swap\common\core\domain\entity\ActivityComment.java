package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 活动评论对象 activity_comment
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
public class ActivityComment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评论ID */
    private Long id;

    /** 活动ID */
    @Excel(name = "活动ID")
    private Long activityId;

    /** 评论用户ID */
    @Excel(name = "评论用户ID")
    private Long userId;

    /** 评论内容 */
    @Excel(name = "评论内容")
    private String content;

    /** 父评论ID，用于回复功能 */
    @Excel(name = "父评论ID")
    private Long parentId;

    /** 状态（0-正常 1-禁用） */
    @Excel(name = "状态", readConverterExp = "0=-正常,1=-禁用")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    // 非数据库字段
    /** 用户昵称 */
    private String nickname;
    /** 用户头像 */
    private String avatar;
    /** 父评论用户昵称 */
    private String parentNickname;
}
