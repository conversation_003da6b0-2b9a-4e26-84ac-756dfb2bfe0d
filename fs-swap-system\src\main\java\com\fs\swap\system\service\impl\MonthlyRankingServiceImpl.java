package com.fs.swap.system.service.impl;

import com.fs.swap.common.constant.CacheConstants;
import com.fs.swap.common.core.domain.entity.MonthlyRanking;
import com.fs.swap.common.core.domain.entity.MonthlyRankingRewardConfig;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.utils.DateUtils;
import com.fs.swap.system.mapper.MonthlyRankingMapper;
import com.fs.swap.system.service.IMonthlyRankingRewardConfigService;
import com.fs.swap.system.service.IMonthlyRankingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 月度排行榜Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class MonthlyRankingServiceImpl implements IMonthlyRankingService {
    
    private static final Logger log = LoggerFactory.getLogger(MonthlyRankingServiceImpl.class);
    
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private MonthlyRankingMapper monthlyRankingMapper;

    @Autowired
    private IMonthlyRankingRewardConfigService monthlyRankingRewardConfigService;

    @Override
    public TableDataInfo getRankingList(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize) {
        if (yearMonth == null || yearMonth.isEmpty()) {
            yearMonth = DateUtils.dateTimeNow("yyyy-MM");
        }

        List<MonthlyRanking> list;
        long total = 0;
        
        // 优先从Redis获取实时数据
        list = getRankingFromRedis(yearMonth, residentialId, pageNum, pageSize);
        total = getRedisRankingCount(yearMonth, residentialId);
        
        // 如果Redis没有数据，从数据库获取
        if (list.isEmpty()) {
            list = getRankingFromDatabase(yearMonth, residentialId, pageNum, pageSize);
            total = getDatabaseRankingCount(yearMonth, residentialId);
        }

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(convertToMapList(list));
        rspData.setTotal(total);
        
        return rspData;
    }

    @Override
    @Transactional
    public void resetRanking(String yearMonth, Long residentialId) {
        // 删除Redis数据
        if (residentialId != null) {
            String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;
            redisCache.deleteObject(zsetKey);
        } else {
            // 删除所有小区的Redis数据
            String pattern = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":";
            redisCache.deleteObjectByPattern(pattern);
        }
        
        // 删除数据库数据
        if (residentialId != null) {
            monthlyRankingMapper.deleteByResidentialAndYearMonth(yearMonth, residentialId);
        } else {
            monthlyRankingMapper.deleteByYearMonth(yearMonth);
        }
        
        log.info("重置月度排行榜完成: yearMonth={}, residentialId={}", yearMonth, residentialId);
    }

    @Override
    public List<MonthlyRanking> selectMonthlyRankingList(MonthlyRanking monthlyRanking) {
        return monthlyRankingMapper.selectMonthlyRankingList(monthlyRanking);
    }

    /**
     * 从Redis获取排行榜数据（按小区分组）
     */
    private List<MonthlyRanking> getRankingFromRedis(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize) {
        String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;
        long start = (long) (pageNum - 1) * pageSize;
        long end = start + pageSize - 1;
        
        Set<ZSetOperations.TypedTuple<String>> rankingSet = redisCache.zReverseRangeWithScores(zsetKey, start, end);
        
        List<MonthlyRanking> list = new ArrayList<>();
        int rank = (int) start + 1;
        
        if (rankingSet != null) {
            for (ZSetOperations.TypedTuple<String> tuple : rankingSet) {
                Long userId = Long.valueOf(Objects.requireNonNull(tuple.getValue()));
                
                // 从数据库获取用户信息
                MonthlyRanking ranking = monthlyRankingMapper.selectUserRankingInfo(userId, yearMonth);
                if (ranking == null) {
                    ranking = new MonthlyRanking();
                    ranking.setUserId(userId);
                    ranking.setYearMonth(yearMonth);
                    ranking.setResidentialId(residentialId);
                }
                
                ranking.setRankPosition(rank++);
                ranking.setTotalSilver(Objects.requireNonNull(tuple.getScore()).longValue());
                list.add(ranking);
            }
        }
        
        return list;
    }

    /**
     * 从数据库获取排行榜数据（按小区分组）
     */
    private List<MonthlyRanking> getRankingFromDatabase(String yearMonth, Long residentialId, Integer pageNum, Integer pageSize) {
        // 获取所有数据，然后按totalSilver排序并分页
        List<MonthlyRanking> allRankings = monthlyRankingMapper.selectRankingWithUserInfo(yearMonth, residentialId, null);

        // 按碳豆数量降序排序
        allRankings.sort((r1, r2) -> Long.compare(r2.getTotalSilver(), r1.getTotalSilver()));

        // 计算分页
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, allRankings.size());

        if (start >= allRankings.size()) {
            return new ArrayList<>();
        }

        List<MonthlyRanking> pagedRankings = allRankings.subList(start, end);

        // 设置排名
        for (int i = 0; i < pagedRankings.size(); i++) {
            pagedRankings.get(i).setRankPosition(start + i + 1);
        }

        return pagedRankings;
    }

    /**
     * 获取Redis排行榜总数（按小区分组）
     */
    private long getRedisRankingCount(String yearMonth, Long residentialId) {
        String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;
        return redisCache.zSize(zsetKey);
    }

    /**
     * 获取数据库排行榜总数（按小区分组）
     */
    private long getDatabaseRankingCount(String yearMonth, Long residentialId) {
        MonthlyRanking query = new MonthlyRanking();
        query.setYearMonth(yearMonth);
        query.setResidentialId(residentialId);
        List<MonthlyRanking> list = monthlyRankingMapper.selectMonthlyRankingList(query);
        return list.size();
    }

    /**
     * 转换为Map列表（优化：循环前统一获取奖励配置）
     */
    private List<Map<String, Object>> convertToMapList(List<MonthlyRanking> rankings) {
        // 优化：循环前统一获取所有奖励配置，避免在循环中重复查询
        List<MonthlyRankingRewardConfig> rewardConfigs = null;
        try {
            rewardConfigs = monthlyRankingRewardConfigService.selectActiveRewardConfigs();
        } catch (Exception e) {
            log.warn("获取奖励配置失败: {}", e.getMessage());
            rewardConfigs = new ArrayList<>();
        }

        // 构建排行榜数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (MonthlyRanking ranking : rankings) {
            Map<String, Object> row = new HashMap<>();
            row.put("userId", ranking.getUserId());
            row.put("nickname", ranking.getNickname());
            row.put("avatar", ranking.getAvatar());
            row.put("totalSilver", ranking.getTotalSilver());
            row.put("rank", ranking.getRankPosition()); // 使用已设置的排名

            // 根据排名获取奖励信息（从预加载的配置中查找）
            String rewardText = findRewardTextByRank(ranking.getRankPosition(), rewardConfigs);
            row.put("reward", rewardText);

            resultList.add(row);
        }

        return resultList;
    }

    /**
     * 从奖励配置列表中查找匹配的奖励文字
     */
    private String findRewardTextByRank(Integer rankPosition, List<MonthlyRankingRewardConfig> rewardConfigs) {
        if (rankPosition == null || rewardConfigs == null || rewardConfigs.isEmpty()) {
            return "--";
        }

        // 查找匹配的奖励配置
        for (MonthlyRankingRewardConfig config : rewardConfigs) {
            if (isRankMatched(rankPosition, config)) {
                return config.getRewardName();
            }
        }

        return "--";
    }

    /**
     * 判断排名是否匹配奖励配置
     */
    private boolean isRankMatched(Integer rankPosition, MonthlyRankingRewardConfig config) {
        if (rankPosition == null || config == null) {
            return false;
        }

        Integer startRank = config.getRankStart();
        Integer endRank = config.getRankEnd();

        if (startRank == null) {
            return false;
        }

        // 如果endRank为null，表示只匹配startRank这一个排名
        if (endRank == null) {
            return rankPosition.equals(startRank);
        }

        // 匹配排名范围
        return rankPosition >= startRank && rankPosition <= endRank;
    }



    /**
     * 更新用户碳豆（按小区分组） - 优化版本：先数据库后Redis
     */
    public void updateUserSilver(Long userId, Long residentialId, Long silverChange, String yearMonth) {
        try {
            // 1. 先更新数据库（权威数据源）
            monthlyRankingMapper.insertOrUpdateUserSilver(yearMonth, residentialId, userId, silverChange);
            
            // 2. 再更新Redis缓存（提升查询性能）
            String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;
            redisCache.zIncrementScore(zsetKey, userId.toString(), silverChange);
            
            log.debug("更新用户碳豆成功: userId={}, residentialId={}, silverChange={}, yearMonth={}",
                     userId, residentialId, silverChange, yearMonth);
                     
        } catch (Exception e) {
            log.error("更新用户碳豆失败: userId={}, residentialId={}, silverChange={}, yearMonth={}", 
                     userId, residentialId, silverChange, yearMonth, e);
            throw e;
        }
    }

    /**
     * 由于数据库表中已移除排名字段，此方法主要用于将数据库数据同步到Redis缓存
     */
    public void syncRankingToRedis(String yearMonth) {
        try {
            log.info("开始同步排行榜数据到Redis: {}", yearMonth);

            // 查询该月份所有的排行榜数据
            MonthlyRanking query = new MonthlyRanking();
            query.setYearMonth(yearMonth);
            List<MonthlyRanking> allRankings = monthlyRankingMapper.selectMonthlyRankingList(query);

            if (allRankings.isEmpty()) {
                log.warn("没有找到月份 {} 的排行榜数据", yearMonth);
                return;
            }

            // 按小区分组
            Map<Long, List<MonthlyRanking>> residentialGroups = new HashMap<>();
            for (MonthlyRanking ranking : allRankings) {
                residentialGroups.computeIfAbsent(ranking.getResidentialId(), k -> new ArrayList<>()).add(ranking);
            }

            int syncedCount = 0;
            // 为每个小区同步数据到Redis
            for (Map.Entry<Long, List<MonthlyRanking>> entry : residentialGroups.entrySet()) {
                Long residentialId = entry.getKey();
                List<MonthlyRanking> residentialRankings = entry.getValue();

                String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;

                // 清空现有Redis数据
                redisCache.deleteObject(zsetKey);

                // 将数据库数据同步到Redis
                for (MonthlyRanking ranking : residentialRankings) {
                    redisCache.zAdd(zsetKey, ranking.getUserId().toString(), ranking.getTotalSilver());
                    syncedCount++;
                }

                log.info("小区 {} 同步 {} 个用户数据到Redis", residentialId, residentialRankings.size());
            }

            log.info("同步排行榜数据到Redis完成: {} - 共同步 {} 条记录", yearMonth, syncedCount);

        } catch (Exception e) {
            log.error("同步排行榜数据到Redis失败: {}", yearMonth, e);
            throw e;
        }
    }

    /**
     * 获取用户排名信息
     */
    public Map<String, Object> getUserRankingInfo(Long userId, String yearMonth) {
        // 先获取用户的小区信息
        MonthlyRanking userInfo = monthlyRankingMapper.selectUserRankingInfo(userId, yearMonth);
        if (userInfo == null) {
            return new HashMap<>();
        }

        Long residentialId = userInfo.getResidentialId();

        // 优先从Redis获取用户排名信息
        String zsetKey = CacheConstants.MONTHLY_RANKING_CACHE_PREFIX + yearMonth + ":" + residentialId;
        Long rank = redisCache.zReverseRank(zsetKey, userId.toString());
        Double score = redisCache.zScore(zsetKey, userId.toString());

        Map<String, Object> result = new HashMap<>();
        if (rank != null && score != null) {
            // Redis有数据，使用Redis数据
            result.put("rank", rank + 1); // Redis排名从0开始，实际排名从1开始
            result.put("userId", userId);
            result.put("totalSilver", score.longValue());
            result.put("nickname", userInfo.getNickname());
            result.put("avatar", userInfo.getAvatar());
            result.put("residentialId", residentialId);
        } else if (userInfo.getTotalSilver() != null) {
            // Redis没有数据，从数据库实时计算排名
            Integer calculatedRank = calculateUserRankFromDatabase(yearMonth, residentialId, userInfo.getTotalSilver());
            result.put("rank", calculatedRank);
            result.put("userId", userId);
            result.put("totalSilver", userInfo.getTotalSilver());
            result.put("nickname", userInfo.getNickname());
            result.put("avatar", userInfo.getAvatar());
            result.put("residentialId", residentialId);
        }

        return result;
    }

    /**
     * 从数据库实时计算用户排名
     */
    private Integer calculateUserRankFromDatabase(String yearMonth, Long residentialId, Long userTotalSilver) {
        // 计算在同一小区中有多少用户的碳豆数量比当前用户多
        int higherRankCount = monthlyRankingMapper.countUsersWithHigherSilver(yearMonth, residentialId, userTotalSilver);
        return higherRankCount + 1; // 排名从1开始
    }
}