import request from '@/utils/request'

// 查询月度排行榜列表
export function listMonthlyRanking(query) {
  return request({
    url: '/operation/monthly-ranking/list',
    method: 'get',
    params: query
  })
}

// 重置月度排行榜
export function resetMonthlyRanking(yearMonth, residentialId) {
  const params = { yearMonth }
  if (residentialId) {
    params.residentialId = residentialId
  }
  return request({
    url: '/operation/monthly-ranking/reset',
    method: 'post',
    params: params
  })
} 