package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.core.domain.entity.TaskCompleteLog;
import com.fs.swap.system.service.ITaskService;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;

/**
 * 任务完成日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/operation/task-complete-log")
public class TaskCompleteLogController extends BaseController {
    
    @Autowired
    private ITaskService taskService;

    /**
     * 查询任务完成日志列表
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskCompleteLog taskCompleteLog) {
        startPage();
        List<TaskCompleteLog> list = taskService.selectTaskCompleteLogList(taskCompleteLog);
        return getDataTable(list);
    }

    /**
     * 导出任务完成日志列表
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:export')")
    @Log(title = "任务完成日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskCompleteLog taskCompleteLog) {
        List<TaskCompleteLog> list = taskService.selectTaskCompleteLogList(taskCompleteLog);
        ExcelUtil<TaskCompleteLog> util = new ExcelUtil<TaskCompleteLog>(TaskCompleteLog.class);
        util.exportExcel(response, list, "任务完成日志数据");
    }

    /**
     * 获取任务完成日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskService.selectTaskCompleteLogById(id));
    }

    /**
     * 新增任务完成日志
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:add')")
    @Log(title = "任务完成日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskCompleteLog taskCompleteLog) {
        return toAjax(taskService.insertTaskCompleteLog(taskCompleteLog));
    }

    /**
     * 修改任务完成日志
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:edit')")
    @Log(title = "任务完成日志", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TaskCompleteLog taskCompleteLog) {
        return toAjax(taskService.updateTaskCompleteLog(taskCompleteLog));
    }

    /**
     * 删除任务完成日志
     */
    @PreAuthorize("@ss.hasPermi('operation:task-complete-log:remove')")
    @Log(title = "任务完成日志", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskService.deleteTaskCompleteLogByIds(ids));
    }
} 