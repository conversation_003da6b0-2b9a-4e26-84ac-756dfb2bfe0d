package com.fs.swap.common.enums;

/**
 * 活动状态枚举
 */
public enum ActivityStatus implements BaseEnum {
    /**
     * 草稿
     */
    DRAFT("0", "草稿"),

    /**
     * 发布中
     */
    PUBLISHED("1", "发布中"),

    /**
     * 已结束
     */
    ENDED("2", "已结束"),

    /**
     * 已取消
     */
    CANCELED("3", "已取消");

    private final String code;
    private final String info;

    ActivityStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据状态码获取活动状态枚举
     *
     * @param code 状态码
     * @return 活动状态枚举
     */
    public static ActivityStatus getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ActivityStatus status : ActivityStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
