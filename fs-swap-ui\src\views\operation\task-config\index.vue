<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务编码" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="触发事件" prop="triggerEvent">
        <el-select v-model="queryParams.triggerEvent" placeholder="请选择触发事件" clearable>
          <el-option
            v-for="dict in dict.type.task_event_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:task-config:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:task-config:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:task-config:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:task-config:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务编码" align="center" prop="taskCode" width="150" />
      <el-table-column label="任务名称" align="center" prop="taskName" width="150" />
      <el-table-column label="任务描述" align="center" prop="taskDesc" :show-overflow-tooltip="true" />
      <el-table-column label="任务类型" align="center" prop="taskType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_type" :value="scope.row.taskType"/>
        </template>
      </el-table-column>
      <el-table-column label="触发事件" align="center" prop="triggerEvent" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_event_type" :value="scope.row.triggerEvent"/>
        </template>
      </el-table-column>
      <el-table-column label="目标次数" align="center" prop="targetCount" width="100" />
      <el-table-column label="奖励碳豆" align="center" prop="rewardSilver" width="100">
        <template slot-scope="scope">
          <span class="reward-silver">{{ scope.row.rewardSilver }}</span>
        </template>
      </el-table-column>
      <el-table-column label="界面路径" align="center" prop="page" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.page || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" prop="icon" width="80">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.icon"
            :src="scope.row.icon"
            :preview-src-list="[scope.row.icon]"
            fit="cover"
            style="width: 30px; height: 30px; border-radius: 4px;"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['operation:task-config:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['operation:task-config:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['operation:task-config:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务编码" prop="taskCode">
              <el-input v-model="form.taskCode" placeholder="请输入任务编码" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="form.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务描述" prop="taskDesc">
          <el-input v-model="form.taskDesc" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="form.taskType" placeholder="请选择任务类型">
                <el-option
                  v-for="dict in dict.type.task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触发事件" prop="triggerEvent">
              <el-select v-model="form.triggerEvent" placeholder="请选择触发事件">
                <el-option
                  v-for="dict in dict.type.task_event_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="目标次数" prop="targetCount">
              <el-input-number v-model="form.targetCount" :min="1" :max="9999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖励碳豆" prop="rewardSilver">
              <el-input-number v-model="form.rewardSilver" :min="0" :max="9999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="界面路径" prop="page">
              <el-input v-model="form.page" placeholder="请输入界面路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" :max="9999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 任务配置详情对话框 -->
    <el-dialog title="任务配置详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务编码">{{ viewForm.taskCode }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ viewForm.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务描述" :span="2">{{ viewForm.taskDesc }}</el-descriptions-item>
        <el-descriptions-item label="任务类型">
          <dict-tag :options="dict.type.task_type" :value="viewForm.taskType"/>
        </el-descriptions-item>
        <el-descriptions-item label="触发事件">
          <dict-tag :options="dict.type.task_event_type" :value="viewForm.triggerEvent"/>
        </el-descriptions-item>
        <el-descriptions-item label="目标次数">{{ viewForm.targetCount }}</el-descriptions-item>
        <el-descriptions-item label="奖励碳豆">
          <span class="reward-silver">{{ viewForm.rewardSilver }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="界面路径" :span="2">
          <span>{{ viewForm.page || '-' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="图标" :span="2">
          <el-image
            v-if="viewForm.icon"
            :src="viewForm.icon"
            :preview-src-list="[viewForm.icon]"
            fit="cover"
            style="width: 60px; height: 60px; border-radius: 8px;"
          />
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="排序">{{ viewForm.sortOrder }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewForm.status === '1' ? 'success' : 'danger'">
            {{ viewForm.status === '1' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewForm.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新者">{{ viewForm.updateBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listTaskConfig, getTaskConfig, delTaskConfig, addTaskConfig, updateTaskConfig } from '@/api/operation/task-config'

export default {
  name: 'TaskConfig',
  dicts: ['task_type', 'task_event_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务配置表格数据
      taskConfigList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskCode: null,
        taskName: null,
        taskType: null,
        triggerEvent: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单参数
      viewForm: {},
      // 表单校验
      rules: {
        taskCode: [
          { required: true, message: '任务编码不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '任务编码长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' },
          { min: 2, max: 100, message: '任务名称长度必须介于 2 和 100 之间', trigger: 'blur' }
        ],
        taskType: [
          { required: true, message: '任务类型不能为空', trigger: 'change' }
        ],
        triggerEvent: [
          { required: true, message: '触发事件不能为空', trigger: 'change' }
        ],
        targetCount: [
          { required: true, message: '目标次数不能为空', trigger: 'blur' },
          { type: 'number', min: 1, message: '目标次数必须大于0', trigger: 'blur' }
        ],
        rewardSilver: [
          { required: true, message: '奖励碳豆数不能为空', trigger: 'blur' },
          { type: 'number', min: 0, message: '奖励碳豆数不能小于0', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询任务配置列表 */
    getList() {
      this.loading = true
      listTaskConfig(this.queryParams).then(response => {
        this.taskConfigList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        taskCode: null,
        taskName: null,
        taskDesc: null,
        taskType: null,
        triggerEvent: null,
        targetCount: 1,
        rewardSilver: 0,
        icon: null,
        sortOrder: 0,
        status: '1'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加任务配置'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getTaskConfig(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改任务配置'
      })
    },
    /** 详情按钮操作 */
    handleView(row) {
      getTaskConfig(row.id).then(response => {
        this.viewForm = response.data
        this.viewOpen = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTaskConfig(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addTaskConfig(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除任务配置编号为"' + ids + '"的数据项？').then(function() {
        return delTaskConfig(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.status === '1' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.taskName + '"任务吗？').then(function() {
        return updateTaskConfig(row)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/task-config/export', {
        ...this.queryParams
      }, `task_config_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.reward-silver {
  color: #ff9800;
  font-weight: bold;
}
</style> 