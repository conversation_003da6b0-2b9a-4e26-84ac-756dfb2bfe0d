package com.fs.swap.system.service;

import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.common.core.domain.entity.TaskCompleteLog;

import java.util.List;

/**
 * 任务管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface ITaskService {
    
    /**
     * 查询任务配置
     * 
     * @param id 任务配置主键
     * @return 任务配置
     */
    public TaskConfig selectTaskConfigById(Long id);

    /**
     * 查询任务配置列表
     * 
     * @param taskConfig 任务配置
     * @return 任务配置集合
     */
    public List<TaskConfig> selectTaskConfigList(TaskConfig taskConfig);

    /**
     * 新增任务配置
     * 
     * @param taskConfig 任务配置
     * @return 结果
     */
    public int insertTaskConfig(TaskConfig taskConfig);

    /**
     * 修改任务配置
     * 
     * @param taskConfig 任务配置
     * @return 结果
     */
    public int updateTaskConfig(TaskConfig taskConfig);

    /**
     * 批量删除任务配置
     * 
     * @param ids 需要删除的任务配置主键集合
     * @return 结果
     */
    public int deleteTaskConfigByIds(Long[] ids);

    /**
     * 删除任务配置信息
     * 
     * @param id 任务配置主键
     * @return 结果
     */
    public int deleteTaskConfigById(Long id);

    /**
     * 查询用户任务
     * 
     * @param id 用户任务主键
     * @return 用户任务
     */
    public UserTask selectUserTaskById(Long id);

    /**
     * 查询用户任务列表
     * 
     * @param userTask 用户任务
     * @return 用户任务集合
     */
    public List<UserTask> selectUserTaskList(UserTask userTask);

    /**
     * 新增用户任务
     * 
     * @param userTask 用户任务
     * @return 结果
     */
    public int insertUserTask(UserTask userTask);

    /**
     * 修改用户任务
     * 
     * @param userTask 用户任务
     * @return 结果
     */
    public int updateUserTask(UserTask userTask);

    /**
     * 批量删除用户任务
     * 
     * @param ids 需要删除的用户任务主键集合
     * @return 结果
     */
    public int deleteUserTaskByIds(Long[] ids);

    /**
     * 删除用户任务信息
     * 
     * @param id 用户任务主键
     * @return 结果
     */
    public int deleteUserTaskById(Long id);

    /**
     * 查询任务完成日志
     * 
     * @param id 任务完成日志主键
     * @return 任务完成日志
     */
    public TaskCompleteLog selectTaskCompleteLogById(Long id);

    /**
     * 查询任务完成日志列表
     * 
     * @param taskCompleteLog 任务完成日志
     * @return 任务完成日志集合
     */
    public List<TaskCompleteLog> selectTaskCompleteLogList(TaskCompleteLog taskCompleteLog);

    /**
     * 新增任务完成日志
     * 
     * @param taskCompleteLog 任务完成日志
     * @return 结果
     */
    public int insertTaskCompleteLog(TaskCompleteLog taskCompleteLog);

    /**
     * 修改任务完成日志
     * 
     * @param taskCompleteLog 任务完成日志
     * @return 结果
     */
    public int updateTaskCompleteLog(TaskCompleteLog taskCompleteLog);

    /**
     * 批量删除任务完成日志
     * 
     * @param ids 需要删除的任务完成日志主键集合
     * @return 结果
     */
    public int deleteTaskCompleteLogByIds(Long[] ids);

    /**
     * 删除任务完成日志信息
     * 
     * @param id 任务完成日志主键
     * @return 结果
     */
    public int deleteTaskCompleteLogById(Long id);
} 