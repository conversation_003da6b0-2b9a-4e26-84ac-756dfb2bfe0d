package com.fs.swap.common.enums;

/**
 * 任务类型枚举
 */
public enum TaskType implements BaseEnum {
    /**
     * 每日任务
     */
    DAILY("DAILY", "每日任务"),

    /**
     * 一次性任务
     */
    ONCE("ONCE", "一次性任务"),

    /**
     * 每周任务
     */
    WEEKLY("WEEKLY", "每周任务"),

    /**
     * 每月任务
     */
    MONTHLY("MONTHLY", "每月任务");

    private final String code;
    private final String info;

    TaskType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据编码获取枚举
     */
    public static TaskType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskType type : TaskType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 