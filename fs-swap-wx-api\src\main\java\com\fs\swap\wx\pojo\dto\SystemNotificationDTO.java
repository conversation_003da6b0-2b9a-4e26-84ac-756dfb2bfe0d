package com.fs.swap.wx.pojo.dto;

import java.util.List;

/**
 * 系统通知DTO
 * 
 * <AUTHOR>
 * @date 2024
 */
public class SystemNotificationDTO {
    
    /** 消息标题 */
    private String title;
    
    /** 消息内容 */
    private String content;
    
    /** 消息类型 */
    private String type = "system";
    
    /** 目标类型：all-所有用户，specific-指定用户 */
    private String targetType = "all";
    
    /** 目标用户ID列表（当targetType为specific时使用） */
    private List<Long> targetUserIds;
    
    /** 跳转配置 */
    private JumpConfig jumpConfig;
    
    /**
     * 跳转配置内部类
     */
    public static class JumpConfig {
        /** 跳转类型：page-页面，tabBar-标签页，order-订单，activity-活动，none-无跳转 */
        private String jumpType = "none";
        
        /** 跳转URL */
        private String jumpUrl;
        
        /** 关联ID */
        private String relatedId;
        
        /** 操作按钮文本 */
        private String actionText;

        public String getJumpType() {
            return jumpType;
        }

        public void setJumpType(String jumpType) {
            this.jumpType = jumpType;
        }

        public String getJumpUrl() {
            return jumpUrl;
        }

        public void setJumpUrl(String jumpUrl) {
            this.jumpUrl = jumpUrl;
        }

        public String getRelatedId() {
            return relatedId;
        }

        public void setRelatedId(String relatedId) {
            this.relatedId = relatedId;
        }

        public String getActionText() {
            return actionText;
        }

        public void setActionText(String actionText) {
            this.actionText = actionText;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public List<Long> getTargetUserIds() {
        return targetUserIds;
    }

    public void setTargetUserIds(List<Long> targetUserIds) {
        this.targetUserIds = targetUserIds;
    }

    public JumpConfig getJumpConfig() {
        return jumpConfig;
    }

    public void setJumpConfig(JumpConfig jumpConfig) {
        this.jumpConfig = jumpConfig;
    }
} 