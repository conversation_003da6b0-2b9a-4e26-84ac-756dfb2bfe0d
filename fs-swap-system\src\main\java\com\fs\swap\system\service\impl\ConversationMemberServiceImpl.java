package com.fs.swap.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.fs.swap.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fs.swap.system.mapper.ConversationMemberMapper;
import com.fs.swap.common.core.domain.entity.ConversationMember;
import com.fs.swap.system.service.IConversationMemberService;

/**
 * 会话成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024
 */
@Service
public class ConversationMemberServiceImpl implements IConversationMemberService 
{
    @Autowired
    private ConversationMemberMapper conversationMemberMapper;

    /**
     * 查询会话成员
     * 
     * @param id 会话成员主键
     * @return 会话成员
     */
    @Override
    public ConversationMember selectConversationMemberById(Long id)
    {
        return conversationMemberMapper.selectConversationMemberById(id);
    }

    /**
     * 查询会话成员列表
     * 
     * @param conversationMember 会话成员
     * @return 会话成员集合
     */
    @Override
    public List<ConversationMember> selectConversationMemberList(ConversationMember conversationMember)
    {
        return conversationMemberMapper.selectConversationMemberList(conversationMember);
    }

    /**
     * 根据会话ID和用户ID查询会话成员
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 会话成员
     */
    @Override
    public ConversationMember selectByConversationAndUser(String conversationId, Long userId)
    {
        return conversationMemberMapper.selectByConversationAndUser(conversationId, userId);
    }

    /**
     * 查询会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 会话成员集合
     */
    @Override
    public List<ConversationMember> selectMembersByConversation(String conversationId)
    {
        return conversationMemberMapper.selectMembersByConversation(conversationId);
    }

    /**
     * 查询用户参与的所有会话
     * 
     * @param userId 用户ID
     * @return 会话成员集合
     */
    @Override
    public List<ConversationMember> selectConversationsByUser(Long userId)
    {
        return conversationMemberMapper.selectConversationsByUser(userId);
    }

    /**
     * 新增会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    @Override
    public int insertConversationMember(ConversationMember conversationMember)
    {
        // 设置默认值
        if (conversationMember.getUnreadCount() == null) {
            conversationMember.setUnreadCount(0);
        }
        if (conversationMember.getIsMuted() == null) {
            conversationMember.setIsMuted(0);
        }
        if (conversationMember.getStatus() == null) {
            conversationMember.setStatus(0);
        }
        if (conversationMember.getJoinTime() == null) {
            conversationMember.setJoinTime(DateUtils.getNowDate());
        }
        
        return conversationMemberMapper.insertConversationMember(conversationMember);
    }

    /**
     * 批量新增会话成员
     * 
     * @param members 会话成员列表
     * @return 结果
     */
    @Override
    public int batchInsertConversationMembers(List<ConversationMember> members)
    {
        if (members == null || members.isEmpty()) {
            return 0;
        }
        
        Date now = DateUtils.getNowDate();
        for (ConversationMember member : members) {
            if (member.getUnreadCount() == null) {
                member.setUnreadCount(0);
            }
            if (member.getIsMuted() == null) {
                member.setIsMuted(0);
            }
            if (member.getStatus() == null) {
                member.setStatus(0);
            }
            if (member.getJoinTime() == null) {
                member.setJoinTime(now);
            }
        }
        
        return conversationMemberMapper.batchInsertConversationMembers(members);
    }

    /**
     * 修改会话成员
     * 
     * @param conversationMember 会话成员
     * @return 结果
     */
    @Override
    public int updateConversationMember(ConversationMember conversationMember)
    {
        return conversationMemberMapper.updateConversationMember(conversationMember);
    }

    /**
     * 更新未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param unreadCount 未读数量
     * @return 结果
     */
    @Override
    public int updateUnreadCount(String conversationId, Long userId, Integer unreadCount)
    {
        return conversationMemberMapper.updateUnreadCount(conversationId, userId, unreadCount);
    }

    /**
     * 增加未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param increment 增加数量
     * @return 结果
     */
    @Override
    public int incrementUnreadCount(String conversationId, Long userId, Integer increment)
    {
        return conversationMemberMapper.incrementUnreadCount(conversationId, userId, increment);
    }

    /**
     * 清零未读消息数量
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param lastReadMessageId 最后已读消息ID
     * @return 结果
     */
    @Override
    public int clearUnreadCount(String conversationId, Long userId, String lastReadMessageId)
    {
        return conversationMemberMapper.clearUnreadCount(conversationId, userId, lastReadMessageId);
    }

    /**
     * 设置会话免打扰
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @param isMuted 是否免打扰
     * @return 结果
     */
    @Override
    public int updateMuteStatus(String conversationId, Long userId, Integer isMuted)
    {
        return conversationMemberMapper.updateMuteStatus(conversationId, userId, isMuted);
    }

    /**
     * 批量删除会话成员
     * 
     * @param ids 需要删除的会话成员主键
     * @return 结果
     */
    @Override
    public int deleteConversationMemberByIds(Long[] ids)
    {
        return conversationMemberMapper.deleteConversationMemberByIds(ids);
    }

    /**
     * 删除会话成员信息
     * 
     * @param id 会话成员主键
     * @return 结果
     */
    @Override
    public int deleteConversationMemberById(Long id)
    {
        return conversationMemberMapper.deleteConversationMemberById(id);
    }

    /**
     * 删除会话的所有成员
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    @Override
    public int deleteByConversationId(String conversationId)
    {
        return conversationMemberMapper.deleteByConversationId(conversationId);
    }

    /**
     * 用户离开会话（软删除）
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int leaveConversation(String conversationId, Long userId)
    {
        return conversationMemberMapper.leaveConversation(conversationId, userId);
    }

    /**
     * 检查用户是否在会话中
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkMemberExists(String conversationId, Long userId)
    {
        return conversationMemberMapper.checkMemberExists(conversationId, userId) > 0;
    }

    /**
     * 添加用户到会话
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int addMemberToConversation(String conversationId, Long userId)
    {
        // 检查用户是否已经在会话中
        if (checkMemberExists(conversationId, userId)) {
            return 1; // 已存在，返回成功
        }
        
        ConversationMember member = new ConversationMember();
        member.setConversationId(conversationId);
        member.setUserId(userId);
        member.setUnreadCount(0);
        member.setIsMuted(0);
        member.setStatus(0);
        member.setJoinTime(DateUtils.getNowDate());
        
        return insertConversationMember(member);
    }

    /**
     * 批量添加用户到会话
     * 
     * @param conversationId 会话ID
     * @param userIds 用户ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int addMembersToConversation(String conversationId, List<Long> userIds)
    {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }
        
        List<ConversationMember> members = new ArrayList<>();
        Date now = DateUtils.getNowDate();
        
        for (Long userId : userIds) {
            // 检查用户是否已经在会话中
            if (!checkMemberExists(conversationId, userId)) {
                ConversationMember member = new ConversationMember();
                member.setConversationId(conversationId);
                member.setUserId(userId);
                member.setUnreadCount(0);
                member.setIsMuted(0);
                member.setStatus(0);
                member.setJoinTime(now);
                members.add(member);
            }
        }
        
        if (!members.isEmpty()) {
            return batchInsertConversationMembers(members);
        }
        
        return userIds.size(); // 所有用户都已存在
    }
} 