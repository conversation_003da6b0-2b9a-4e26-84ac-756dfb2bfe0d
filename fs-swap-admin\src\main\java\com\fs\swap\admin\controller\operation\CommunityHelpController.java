package com.fs.swap.admin.controller.operation;

import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.AdminBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.CommunityHelp;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.system.service.ICommunityHelpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 邻里互助Controller
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/operation/communityHelp")
public class CommunityHelpController extends AdminBaseController
{
    @Autowired
    private ICommunityHelpService communityHelpService;

    /**
     * 查询邻里互助列表
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityHelp communityHelp)
    {
        startPage();
        List<CommunityHelp> list = communityHelpService.selectCommunityHelpList(communityHelp);
        return getDataTable(list);
    }

    /**
     * 导出邻里互助列表
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:export')")
    @Log(title = "邻里互助", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityHelp communityHelp)
    {
        List<CommunityHelp> list = communityHelpService.selectCommunityHelpList(communityHelp);
        ExcelUtil<CommunityHelp> util = new ExcelUtil<CommunityHelp>(CommunityHelp.class);
        util.exportExcel(response, list, "邻里互助数据");
    }

    /**
     * 获取邻里互助详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityHelpService.selectCommunityHelpById(id));
    }

    /**
     * 新增邻里互助
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:add')")
    @Log(title = "邻里互助", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityHelp communityHelp)
    {
        communityHelp.setCreateBy(getUsername());
        return toAjax(communityHelpService.insertCommunityHelp(communityHelp));
    }

    /**
     * 修改邻里互助
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:edit')")
    @Log(title = "邻里互助", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityHelp communityHelp)
    {
        communityHelp.setUpdateBy(getUsername());
        return toAjax(communityHelpService.updateCommunityHelp(communityHelp));
    }

    /**
     * 删除邻里互助
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:remove')")
    @Log(title = "邻里互助", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityHelpService.deleteCommunityHelpByIds(ids));
    }

    /**
     * 审核邻里互助
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:audit')")
    @Log(title = "邻里互助审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{id}")
    public AjaxResult audit(@PathVariable Long id, @RequestParam String status, @RequestParam(required = false) String auditRemark)
    {
        CommunityHelp communityHelp = new CommunityHelp();
        communityHelp.setId(id);
        communityHelp.setStatus(status);
        communityHelp.setAuditRemark(auditRemark);
        communityHelp.setUpdateBy(getUsername());
        return toAjax(communityHelpService.updateCommunityHelp(communityHelp));
    }

    /**
     * 批量审核邻里互助
     */
    @PreAuthorize("@ss.hasPermi('operation:communityHelp:audit')")
    @Log(title = "邻里互助批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestParam Long[] ids, @RequestParam String status, @RequestParam(required = false) String auditRemark)
    {
        int result = 0;
        for (Long id : ids) {
            CommunityHelp communityHelp = new CommunityHelp();
            communityHelp.setId(id);
            communityHelp.setStatus(status);
            communityHelp.setAuditRemark(auditRemark);
            communityHelp.setUpdateBy(getUsername());
            result += communityHelpService.updateCommunityHelp(communityHelp);
        }
        return toAjax(result);
    }
}
