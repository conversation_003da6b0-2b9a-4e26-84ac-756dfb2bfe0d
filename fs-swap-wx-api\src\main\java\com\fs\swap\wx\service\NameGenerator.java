package com.fs.swap.wx.service;

import java.util.Random;

public class NameGenerator {
    private static final String[] NAMES = {
            "互助小站", "邻里乐活", "搭把手呀", "共享阳光", "帮你带啦", "邻门好呀",
            "互助达人", "隔壁老王", "楼上李姐", "楼下小张", "捎带一脚", "顺手帮啦",
            "邻里暖光", "互助联盟", "借点酱油", "分你颗糖", "共搭便车", "帮取快递",
            "代收外卖", "照看喵汪", "邻里彩虹", "互助火花", "你帮我助", "邻里一家",
            "搭伙买菜", "共享工具", "借把雨伞", "递瓶酱油", "互助小太阳", "邻舍笑哈哈",
            "帮忙不谢", "举手之劳", "邻里加油站", "互助小马达", "隔壁吃货", "分享达人",
            "楼道互助", "单元暖事", "帮抬个箱", "扶你一把", "邻里智囊", "互助宝典",
            "借点醋呗", "分块蛋糕", "共抗风雨", "邻里同心", "搭个顺风", "帮看门户",
            "互助小分队", "邻门福星", "邻里乐捐", "共享厨房", "互助菜园", "借本书呗",
            "换件衣服", "邻里欢乐颂", "互助进行曲", "帮修个灯", "换节电池", "邻舍好帮",
            "互助无忧", "递张纸巾", "分袋垃圾", "邻里暖阳", "互助火花", "借个梯子",
            "递把螺丝刀", "共享 WiFi", "分点水果", "邻里互助社", "搭伴遛弯", "帮接孩子",
            "代扔垃圾", "邻门善举", "互助小确幸", "借点盐巴", "分你半块", "共撑一伞",
            "邻里同心", "帮扛个米", "扶老携幼", "楼道雷锋", "单元活宝", "互助小蜜蜂",
            "邻舍开心果", "借个充电器", "分你瓶水", "邻里互助家", "搭伙拼单", "帮拆快递",
            "代收信件", "邻门热心肠", "互助正能量", "借副碗筷", "分份零食", "共扫楼道",
            "邻里守望", "帮开个门", "递张门禁", "互助小站", "邻里笑谈", "搭手帮忙",
            "共享善意", "帮拎个袋", "邻门乐事", "互助先锋", "隔壁厨神", "楼上学霸",
            "楼下健身", "捎带快递", "顺手关灯", "邻里星光", "互助联盟", "借点白糖",
            "分你块饼", "共乘电梯", "帮取外卖", "代收包裹", "照看花草", "邻里彩虹桥",
            "互助小火苗", "你助我帮", "邻里和睦", "搭伙做饭", "共享玩具", "借把剪刀",
            "递包纸巾", "互助小太阳", "邻舍乐呵呵", "帮忙应该", "举手之劳", "邻里能量站",
            "互助小引擎", "隔壁戏精", "分享狂魔", "楼道帮手", "单元暖事", "帮搬个家",
            "扶上台阶", "邻里智慧库", "互助秘籍", "借点料酒", "分块披萨", "共抗严寒",
            "邻里一心", "搭个顺路", "帮看宠物", "互助小分队", "邻门幸运星", "顺手帮忙",
            "邻里捐赠", "共享餐具", "互助花园", "借本杂志", "换双鞋子", "邻里欢乐多",
            "互助进行时", "帮修水管", "换个灯泡", "邻舍好助", "互助安心", "递包湿巾",
            "分袋零食", "邻里暖阳阳", "互助小火花", "借个锤子", "递把钳子", "共享热点",
            "分点零食", "邻里互助社", "搭伴散步", "帮接老人", "代取快递", "邻门善事",
            "互助小幸福", "借点味精", "分你一半", "共撑雨具", "邻里齐心", "帮扛行李",
            "扶幼助老", "楼道活雷锋", "单元开心果", "互助小蜜蜂", "邻舍欢乐多", "借个充电宝",
            "分你瓶饮料", "邻里互助家", "搭伙团购", "帮拆包裹", "代收报纸", "邻门热心人",
            "互助正能量", "借套餐具", "分份甜点", "共扫单元", "邻里守望者", "帮开单元门",
            "递张门卡"
    };

    private static final Random random = new Random();

    public static String getRandomName() {
        int index = random.nextInt(NAMES.length);
        return NAMES[index];
    }
}
