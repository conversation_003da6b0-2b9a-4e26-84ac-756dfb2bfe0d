/**
 * 区域数据缓存工具
 * 用于管理行政区域、社区、小区数据的统一缓存
 */

// 默认缓存过期时间（24小时）
const DEFAULT_CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

// 缓存键
const CACHE_KEYS = {
  REGIONS: 'area_regions',
  COMMUNITIES: 'area_communities',
  RESIDENTIALS: 'area_residentials'
};

// 时间戳后缀
const TIMESTAMP_SUFFIX = '_timestamp';

/**
 * 区域数据缓存管理类
 */
class AreaDataCache {
  constructor() {
    this.expiration = DEFAULT_CACHE_EXPIRATION;
    this.regions = [];
    this.communities = [];
    this.residentials = [];
    this.isLoaded = false;
    this.regionMap = new Map(); // regionId -> region
    this.communityMap = new Map(); // communityId -> community
    this.residentialMap = new Map(); // residentialId -> residential
    this.regionCommunitiesMap = new Map(); // regionId -> communities[]
    this.communityResidentialsMap = new Map(); // communityId -> residentials[]
  }

  /**
   * 检查缓存是否有效
   * @param {string} key - 缓存键名
   * @returns {boolean} - 缓存是否有效
   */
  isCacheValid(key) {
    const cacheTimestamp = localStorage.getItem(`${key}${TIMESTAMP_SUFFIX}`);
    if (!cacheTimestamp) return false;

    // 检查缓存是否过期
    const now = new Date().getTime();
    return (now - parseInt(cacheTimestamp)) < this.expiration;
  }

  /**
   * 从缓存中获取数据
   * @param {string} key - 缓存键名
   * @returns {Array|null} - 缓存数据或null
   */
  getFromCache(key) {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`从缓存中获取${key}数据失败:`, error);
      return null;
    }
  }

  /**
   * 保存数据到缓存
   * @param {string} key - 缓存键名
   * @param {Array} data - 要缓存的数据
   */
  saveToCache(key, data) {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      localStorage.setItem(`${key}${TIMESTAMP_SUFFIX}`, new Date().getTime().toString());
    } catch (error) {
      console.error(`保存${key}数据到缓存失败:`, error);
    }
  }

  /**
   * 清除所有缓存数据
   */
  clearCache() {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
      localStorage.removeItem(`${key}${TIMESTAMP_SUFFIX}`);
    });

    // 重置内存中的数据
    this.regions = [];
    this.communities = [];
    this.residentials = [];
    this.isLoaded = false;
    this.regionMap.clear();
    this.communityMap.clear();
    this.residentialMap.clear();
    this.regionCommunitiesMap.clear();
    this.communityResidentialsMap.clear();
  }

  /**
   * 构建内存中的索引映射
   * @private
   */
  buildMaps() {
    // 构建区域映射
    this.regionMap.clear();
    this.regions.forEach(region => {
      this.regionMap.set(region.id, region);
    });

    // 构建社区映射和区域-社区映射
    this.communityMap.clear();
    this.regionCommunitiesMap.clear();
    this.communities.forEach(community => {
      this.communityMap.set(community.id, community);

      // 区域-社区映射
      if (!this.regionCommunitiesMap.has(community.regionId)) {
        this.regionCommunitiesMap.set(community.regionId, []);
      }
      this.regionCommunitiesMap.get(community.regionId).push(community);
    });

    // 构建小区映射和社区-小区映射
    this.residentialMap.clear();
    this.communityResidentialsMap.clear();
    this.residentials.forEach(residential => {
      this.residentialMap.set(residential.id, residential);

      // 社区-小区映射
      if (!this.communityResidentialsMap.has(residential.communityId)) {
        this.communityResidentialsMap.set(residential.communityId, []);
      }
      this.communityResidentialsMap.get(residential.communityId).push(residential);
    });
  }

  /**
   * 加载区域数据
   * @param {Function} loadFunc - 加载区域数据的函数
   * @returns {Promise<Array>} - 区域数据数组
   */
  async loadRegions(loadFunc) {
    // 如果数据已加载，直接返回
    if (this.regions.length > 0) {
      return this.regions;
    }

    // 检查缓存
    if (this.isCacheValid(CACHE_KEYS.REGIONS)) {
      const cachedData = this.getFromCache(CACHE_KEYS.REGIONS);
      if (cachedData && cachedData.length > 0) {
        this.regions = cachedData;
        return this.regions;
      }
    }

    // 加载数据
    try {
      const regions = await loadFunc();
      this.regions = regions;
      this.saveToCache(CACHE_KEYS.REGIONS, regions);
      return regions;
    } catch (error) {
      console.error('加载区域数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载社区数据
   * @param {Function} loadFunc - 加载社区数据的函数
   * @returns {Promise<Array>} - 社区数据数组
   */
  async loadCommunities(loadFunc) {
    // 如果数据已加载，直接返回
    if (this.communities.length > 0) {
      return this.communities;
    }

    // 检查缓存
    if (this.isCacheValid(CACHE_KEYS.COMMUNITIES)) {
      const cachedData = this.getFromCache(CACHE_KEYS.COMMUNITIES);
      if (cachedData && cachedData.length > 0) {
        this.communities = cachedData;
        return this.communities;
      }
    }

    // 加载数据
    try {
      const communities = await loadFunc();
      this.communities = communities;
      this.saveToCache(CACHE_KEYS.COMMUNITIES, communities);
      return communities;
    } catch (error) {
      console.error('加载社区数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载小区数据
   * @param {Function} loadFunc - 加载小区数据的函数
   * @returns {Promise<Array>} - 小区数据数组
   */
  async loadResidentials(loadFunc) {
    // 如果数据已加载，直接返回
    if (this.residentials.length > 0) {
      return this.residentials;
    }

    // 检查缓存
    if (this.isCacheValid(CACHE_KEYS.RESIDENTIALS)) {
      const cachedData = this.getFromCache(CACHE_KEYS.RESIDENTIALS);
      if (cachedData && cachedData.length > 0) {
        this.residentials = cachedData;
        return this.residentials;
      }
    }

    // 加载数据
    try {
      const residentials = await loadFunc();
      this.residentials = residentials;
      this.saveToCache(CACHE_KEYS.RESIDENTIALS, residentials);
      return residentials;
    } catch (error) {
      console.error('加载小区数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载所有数据并构建索引
   * @param {Object} loaders - 包含加载函数的对象
   * @param {Function} loaders.loadRegions - 加载区域的函数
   * @param {Function} loaders.loadCommunities - 加载社区的函数
   * @param {Function} loaders.loadResidentials - 加载小区的函数
   * @returns {Promise<void>}
   */
  async loadAll(loaders) {
    if (this.isLoaded) return;

    try {
      await Promise.all([
        this.loadRegions(loaders.loadRegions),
        this.loadCommunities(loaders.loadCommunities),
        this.loadResidentials(loaders.loadResidentials)
      ]);

      this.buildMaps();
      this.isLoaded = true;
    } catch (error) {
      console.error('加载所有区域数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据区域ID获取社区列表
   * @param {number} regionId - 区域ID
   * @returns {Array} - 社区列表
   */
  getCommunitiesByRegionId(regionId) {
    if (!regionId) return [];
    return this.regionCommunitiesMap.get(Number(regionId)) || [];
  }

  /**
   * 根据社区ID获取小区列表
   * @param {number} communityId - 社区ID
   * @returns {Array} - 小区列表
   */
  getResidentialsByCommunityId(communityId) {
    if (!communityId) return [];
    return this.communityResidentialsMap.get(Number(communityId)) || [];
  }

  /**
   * 获取区域名称
   * @param {number} regionId - 区域ID
   * @returns {string} - 区域名称
   */
  getRegionName(regionId) {
    if (!regionId) return '';
    const region = this.regionMap.get(Number(regionId));
    return region ? region.name : '';
  }

  /**
   * 获取社区名称
   * @param {number} communityId - 社区ID
   * @returns {string} - 社区名称
   */
  getCommunityName(communityId) {
    if (!communityId) return '';
    const community = this.communityMap.get(Number(communityId));
    return community ? community.name : '';
  }

  /**
   * 获取小区名称
   * @param {number} residentialId - 小区ID
   * @returns {string} - 小区名称
   */
  getResidentialName(residentialId) {
    if (!residentialId) return '';
    const residential = this.residentialMap.get(Number(residentialId));
    return residential ? residential.name : '';
  }

  /**
   * 初始化区域数据
   * @param {Object} data - 包含rows字段的区域数据对象
   */
  initRegions(data) {
    if (!data) return;

    let regions = [];

    try {
      // 只处理rows格式的数据
      if (data.rows && Array.isArray(data.rows)) {
        regions = data.rows;
      } else {
        console.warn('初始化区域数据失败: 数据格式不正确，缺少rows字段');
        return;
      }

      if (regions.length === 0) {
        console.warn('初始化区域数据失败: 无有效数据');
        return;
      }

      console.log('初始化区域数据成功，数量:', regions.length);
      this.regions = regions;
      this.saveToCache(CACHE_KEYS.REGIONS, regions);
      this.buildMaps();
    } catch (err) {
      console.error('初始化区域数据失败:', err);
    }
  }

  /**
   * 初始化社区数据
   * @param {Object} data - 包含rows字段的社区数据对象
   */
  initCommunities(data) {
    if (!data) return;

    let communities = [];

    try {
      // 只处理rows格式的数据
      if (data.rows && Array.isArray(data.rows)) {
        communities = data.rows;
      } else {
        console.warn('初始化社区数据失败: 数据格式不正确，缺少rows字段');
        return;
      }

      if (communities.length === 0) {
        console.warn('初始化社区数据失败: 无有效数据');
        return;
      }

      console.log('初始化社区数据成功，数量:', communities.length);
      this.communities = communities;
      this.saveToCache(CACHE_KEYS.COMMUNITIES, communities);
      this.buildMaps();
    } catch (err) {
      console.error('初始化社区数据失败:', err);
    }
  }

  /**
   * 初始化小区数据
   * @param {Object} data - 包含rows字段的小区数据对象
   */
  initResidentials(data) {
    if (!data) return;

    let residentials = [];

    try {
      // 只处理rows格式的数据
      if (data.rows && Array.isArray(data.rows)) {
        residentials = data.rows;
      } else {
        console.warn('初始化小区数据失败: 数据格式不正确，缺少rows字段');
        return;
      }

      if (residentials.length === 0) {
        console.warn('初始化小区数据失败: 无有效数据');
        return;
      }

      console.log('初始化小区数据成功，数量:', residentials.length);
      this.residentials = residentials;
      this.saveToCache(CACHE_KEYS.RESIDENTIALS, residentials);
      this.buildMaps();
    } catch (err) {
      console.error('初始化小区数据失败:', err);
    }
  }
}

// 创建单例实例
const areaDataCache = new AreaDataCache();

export default areaDataCache;