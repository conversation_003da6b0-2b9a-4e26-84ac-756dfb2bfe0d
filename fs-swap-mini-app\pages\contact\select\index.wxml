<view class="contact-select-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <text class="iconfont icon-search search-icon"></text>
      <input 
        class="search-input" 
        placeholder="搜索联系人" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
      />
    </view>
  </view>

  <!-- 联系人列表 -->
  <scroll-view class="contact-list" scroll-y enhanced show-scrollbar="{{false}}">
    <view class="contact-items">
      <view 
        class="contact-item"
        wx:for="{{contactList}}" 
        wx:key="userId"
        data-contact="{{item}}"
        bindtap="onContactSelect"
      >
        <!-- 头像 -->
        <view class="avatar-wrapper">
          <image src="{{item.avatar}}" class="avatar"></image>
          <view wx:if="{{item.isOnline}}" class="online-status"></view>
        </view>
        
        <!-- 联系人信息 -->
        <view class="contact-info">
          <text class="contact-name">{{item.nickname}}</text>
          <text class="contact-status">{{item.isOnline ? '在线' : '离线'}}</text>
        </view>

        <!-- 右侧箭头 -->
        <view class="arrow-icon">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{contactList.length === 0 && !isLoading}}">
      <view class="empty-icon">👥</view>
      <text class="empty-text">暂无联系人</text>
      <text class="empty-desc">暂时没有可以聊天的联系人</text>
    </view>

    <!-- 搜索无结果 -->
    <view class="empty-state" wx:if="{{searchKeyword && contactList.length === 0 && !isLoading}}">
      <view class="empty-icon">🔍</view>
      <text class="empty-text">没有找到相关联系人</text>
      <text class="empty-desc">试试其他关键词</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <van-loading size="16px" color="#3B7FFF" />
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view> 